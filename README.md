# Abre-Cron

The Abre Cron SFTP Server (Google Compute) uses sshd to authenticate users. The sshd configuration can be found at '/etc/ssh/' and configured in file 'sshd_config'. You can use vi to edit the configuration (sudo vi sshd_config).

The MaxStartups directive in the sshd_config file is used to control the rate at which new SSH connections are accepted by the SSH server (sshd). It specifies the maximum number of unauthenticated or partially authenticated connection attempts that can be simultaneously pending on the server.

MaxStartups <initial>:<rate>:<max>

After updating the config, save the file in vi and restart ssh with 'sudo systemctl restart sshd'

## Job Timing

Daily Exports (queue-daily-export-jobs): Starts at 4:30PM EST

API (queue-daily-api-import-jobs): Starts at 8PM EST

SFTP (queue-daily-import-jobs): Starts at 12AM EST

Supplemental (queue-daily-supplemental-jobs): Starts at 6AM EST

Slack Bot (cron-result): Alert at 7:30AM EST (Based on Previous 11.5 Hours)

## Node Crons

We have added some crons that run on node. There are a number of things we've needed to do on the cron machine to get it to work.

The crons run under the `www-data` user. To use npm correctly (for `npm install`, specifically), the `www-data` user requires a `.npm` and `.config` folder to be created in it's home directory `/var/www`. The command for these should look like this:

```
sudo mkdir /var/www/.npm
sudo chown www-data:www-data /var/www/.npm
```

We are also hosting this code in `/opt`. Similary `chown` the directory and all files to `www-data`. I'm not sure if these 2 `chown`s are necessary
