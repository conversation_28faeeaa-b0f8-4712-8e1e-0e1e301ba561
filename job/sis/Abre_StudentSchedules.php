<?php

/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config)
{
    $cronName = 'Abre Student Schedules';

    try {

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        //File
        $fileName = "Abre_StudentSchedules";

        //Define
        define("SAFE_COLUMN_COUNT", 15);
        define("MAX_IMPORT_LIMIT", 25);
        $currentSchoolYearID = getCurrentSchoolYearID($db);
        $error = null;
        $skip = null;
        $separator = "\r\n";

        //Connect
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.");
        }

        //Find File
        $cronFile = $sftp->get("$fileName.txt");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileName.csv");
            $fileType = "csv";
            $columnSeparator = ",";
        } else {
            $cronFile = $sftp->get("$fileName.txt");
            $fileType = "txt";
            $columnSeparator = "\t";
        }

        if (!$cronFile) {
            $skip = true;
            throw new Exception("No file found.");
        } else {
            $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
            if ($fileDetails["isEmpty"]) {
                $skip = true;
                throw new Exception("File is empty.");
            } elseif ($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]) {
                $skip = true;
                throw new Exception("File only contains a header row.");
            } elseif (!$fileDetails["hasValidDataRow"]) {
                throw new Exception("No valid data row found.");
            }
        }

        $rowCounter = 0;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO Abre_StudentSchedules (
            StudentID, FirstName, LastName, SchoolCode, CourseCode, SectionCode,
            CourseName, StaffId, TeacherName, TermCode, Period, RoomNumber,
            StudentIEPStatus, StudentGiftedStatus, StudentELLStatus, siteID,
            school_year_id
            ) VALUES ";

        $line = strtok($cronFile, $separator);
        $line = strtok($separator);

        /**
         * Attempt to delete existing data
         * This is done in a loop with retry logic to ensure that all data is deleted
         * Max attempts is set to 3
         * This is to prevent infinite loops in case of a database issue
         * The delete query is executed in batches of 500 rows to prevent locking issues
         **/
        $attempts = 0;
        while ($attempts < 3) {
            $deleteData = deleteExistingData($db, $siteID, $currentSchoolYearID);
            if ($deleteData) {
                break;
            }
            $attempts++;
        }

        if ($attempts == 3) {
            throw new Exception("Could not delete existing data after 3 attempts.");
        }

        do {
            //Split Columns
            if ($fileType == "txt") {
                $data = str_getcsv($line, $columnSeparator);
            } else {
                $data = str_getcsv($line, $columnSeparator);
            }

            if (count($data) >= SAFE_COLUMN_COUNT) {
                $rowCounter++;

                $studentID = trim($db->escape_string($data[0]));
                $firstName = trim($db->escape_string($data[1]));
                $lastName = trim($db->escape_string($data[2]));
                $schoolCode = trim($db->escape_string($data[3]));
                $courseCode = trim($db->escape_string($data[4]));
                $sectionCode = trim($db->escape_string($data[5]));
                $courseName = trim($db->escape_string($data[6]));
                $staffId = trim($db->escape_string($data[7]));
                $teacherName = trim($db->escape_string($data[8]));
                $termCode = trim($db->escape_string($data[9]));
                $termCode = $termCode == "All" ? "Year" : $termCode;
                $period = trim($db->escape_string($data[10]));
                $studentIEPStatus = trim($db->escape_string($data[11]));
                $studentGiftedStatus = trim($db->escape_string($data[12]));
                $studentELLStatus = trim($db->escape_string($data[13]));
                $roomNumber = trim($db->escape_string($data[14]));

                $valuesToImport[] = "(
                    '$studentID', '$firstName', '$lastName', '$schoolCode', '$courseCode',
                    '$sectionCode', '$courseName', '$staffId', '$teacherName', '$termCode',
                    '$period','$roomNumber', '$studentIEPStatus', '$studentGiftedStatus',
                    '$studentELLStatus', '$siteID', $currentSchoolYearID
                )";

                if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $dbColumns, $valuesToImport);
                    $valuesToImport = [];
                }
            }

            $line = strtok($separator);
        } while ($line !== false);

        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport);
        }

    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $details = [
            "rowsInserted" => $rowCounter
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

function deleteExistingData($db, $siteID, $currentSchoolYearID)
{
    // Try up to 3 times with a small delay between attempts
    for ($attempt = 1; $attempt <= 3; $attempt++) {
        // Attempt deletion
        attemptToDeleteExistingData($db, $siteID, $currentSchoolYearID);
        
        // Check if deletion was successful
        if (checkIfDataDeleted($db, $siteID, $currentSchoolYearID)) {
            return true;
        }
        
        // If not successful and not the last attempt, wait before trying again
        if ($attempt < 3) {
            sleep(2);
        }
    }
    
    // If here, all attempts failed
    return false;
}

/**
 * Calculate delay minutes based on retry attempt number using custom backoff
 * @param int $retryAttempt Current retry attempt number (1-based)
 * @return int Delay in minutes
 */
function calculateBackoffDelay($retryAttempt)
{
    // Backoff pattern: 1, 2, 4, 8, 15 minutes
    if ($retryAttempt == 1) return 1;
    else if ($retryAttempt == 2) return 2;
    else if ($retryAttempt == 3) return 4;
    else if ($retryAttempt == 4) return 8;
    else if ($retryAttempt == 5) return 15;
    else return 15;
}

function attemptToDeleteExistingData($db, $siteID, $currentSchoolYearID)
{
    // Execute the delete query with a small batch size to reduce lock duration
    $batchSize = 500;
    // If more than 2.5 million rows, increase the maxBatches size below
    $maxBatches = 5000;
    $batchCount = 0;
    $deletedRows = 1;
    $maxLockRetries = 5;
    $totalLockTime = 0;
    $maxTotalLockTime = 1800;
    
    do {
        $lockRetries = 0;
        $success = false;
        
        while (!$success && $lockRetries < $maxLockRetries && $totalLockTime < $maxTotalLockTime) {
            try {
                // Add delay between batches to reduce pressure
                if ($batchCount > 0) {
                    usleep(100000);
                }
                
                // Delete with batch size limit
                $result = $db->query("DELETE FROM Abre_StudentSchedules WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID LIMIT $batchSize");
                
                if ($result) {
                    $success = true;
                    $deletedRows = $db->affected_rows;
                } else {
                    // Check if error is due to table lock
                    if (strpos($db->error, 'Lock wait timeout exceeded') !== false || 
                        strpos($db->error, 'Deadlock found') !== false) {
                        $lockRetries++;
                        
                        // Use helper function for backoff pattern
                        $delayMinutes = calculateBackoffDelay($lockRetries);
                        
                        $delaySeconds = $delayMinutes * 60;
                        $totalLockTime += $delaySeconds;
                        
                        if ($lockRetries < $maxLockRetries && $totalLockTime < $maxTotalLockTime) {
                            // Log attempt
                            error_log("Lock detected on batch $batchCount, attempt $lockRetries. Waiting $delayMinutes minutes. Total lock time: " . ($totalLockTime / 60) . " minutes");
                            sleep($delaySeconds);
                            continue;
                        }
                    }
                    // If not lock error or max retries reached, throw exception
                    throw new Exception("Error deleting batch: " . $db->error);
                }
            } catch (Exception $e) {
                if ($lockRetries >= $maxLockRetries || $totalLockTime >= $maxTotalLockTime) {
                    throw new Exception("Failed to delete batch after $lockRetries attempts or " . ($totalLockTime / 60) . " minutes of lock time: " . $e->getMessage());
                }
                $lockRetries++;
                
                // Use helper function for backoff pattern
                $delayMinutes = calculateBackoffDelay($lockRetries);
                
                $delaySeconds = $delayMinutes * 60;
                $totalLockTime += $delaySeconds;
                sleep($delaySeconds);
            }
        }
        
        if (!$success) {
            throw new Exception("Failed to delete batch after exhausting all retry attempts");
        }
        
        $batchCount++;
    } while ($deletedRows > 0 && $batchCount < $maxBatches);
}

function checkIfDataDeleted($db, $siteID, $currentSchoolYearID)
{
    // Check if any rows still exist
    $result = $db->query("SELECT COUNT(*) FROM Abre_StudentSchedules WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID");
    return $result->fetch_row()[0] == 0;
}
