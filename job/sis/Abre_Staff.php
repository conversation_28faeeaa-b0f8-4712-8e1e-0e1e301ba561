<?php
/*
* Copyright (C) 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Staff';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "Abre_Staff";

    //Define
    define("SAFE_COLUMN_COUNT", 15);
    define("MAX_IMPORT_LIMIT", 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

    if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

  	$rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_Staff (
      StaffID, SchoolCode, FirstName, MiddleName, LastName, Gender, DateOfBirth,
      Address1, AddressLine2, City, State, Zip, Phone1, EMail1, HiringDate, is_imported, siteID, imported_on,
      school_year_id
     ) VALUES ";

  	$line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $eligibleStaff = [];
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$staffID = trim($db->escape_string($data[0]));
  			$schoolCode = trim($db->escape_string($data[1]));
        $rawFirstName = trim($data[2]);
  			$firstName = $db->escape_string($rawFirstName);
  			$middleName = trim($db->escape_string($data[3]));
        $rawLastName = trim($data[4]);
  			$lastName = $db->escape_string($rawLastName);
  			$gender = trim($db->escape_string($data[5]));
  			$dateOfBirth = trim($db->escape_string($data[6]));
  			$addressLine1 = trim($db->escape_string($data[7]));
  			$addressLine2 = trim($db->escape_string($data[8]));
  			$city = trim($db->escape_string($data[9]));
  			$state = trim($db->escape_string($data[10]));
  			$zip = trim($db->escape_string($data[11]));
  			$phone1 = trim($db->escape_string($data[12]));
        $rawEmail = trim($data[13]);
  			$email = $db->escape_string($rawEmail);
        $hiringDate = trim($db->escape_string($data[14]));

        if($rawEmail != "" && $staffID != ""){
          if(isset($eligibleStaff[$staffID])){
            $eligibleStaff[$staffID]["schools"] []= $schoolCode;
          }else{
            $eligibleStaff[$staffID] = [
              "firstName" => $rawFirstName,
              "lastName" => $rawLastName,
              "email" => $rawEmail,
              "schools" => [
                $schoolCode
              ]
            ];
          }

          $valuesToImport []= "(
            '$staffID', '$schoolCode', '$firstName', '$middleName', '$lastName',
            '$gender', '$dateOfBirth', '$addressLine1', '$addressLine2', '$city', '$state',
            '$zip', '$phone1', '$email', '$hiringDate', 1, '$siteID', NOW(), $currentSchoolYearID
          )";
        }

        if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          $values = implode(",", $valuesToImport);
          $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                          FirstName = VALUES(FirstName),
                          MiddleName = VALUES(MiddleName),
                          LastName = VALUES(LastName),
                          Gender = VALUES(Gender),
                          DateOfBirth = VALUES(DateOfBirth),
                          Address1 = VALUES(Address1),
                          AddressLine2 = VALUES(AddressLine2),
                          City = VALUES(City),
                          State = VALUES(State),
                          Zip = VALUES(Zip),
                          Phone1 = VALUES(Phone1),
                          EMail1 = VALUES(EMail1),
                          HiringDate = VALUES(HiringDate),
                          is_imported = 1,
                          imported_on = NOW(),
                          is_archived = 0";
          $db->query($importQuery);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      $values = implode(",", $valuesToImport);
      $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                      FirstName = VALUES(FirstName),
                      MiddleName = VALUES(MiddleName),
                      LastName = VALUES(LastName),
                      Gender = VALUES(Gender),
                      DateOfBirth = VALUES(DateOfBirth),
                      Address1 = VALUES(Address1),
                      AddressLine2 = VALUES(AddressLine2),
                      City = VALUES(City),
                      State = VALUES(State),
                      Zip = VALUES(Zip),
                      Phone1 = VALUES(Phone1),
                      EMail1 = VALUES(EMail1),
                      HiringDate = VALUES(HiringDate),
                      is_imported = 1,
                      imported_on = NOW(),
                      is_archived = 0";
      $db->query($importQuery);
    }

    //sync new staff with our directory
    $emptyStringEncrypted = encrypt("", $config->dbKey->iv, $config->dbKey->key);
    $insertSql = "INSERT INTO directory
                  (updatedtime, superadmin, admin, picture, firstname, lastname,
                    middlename, address, city, state, zip, email, phone, extension,
                    cellphone, ss, dob, gender, ethnicity, title, contract, classification,
                    location, grade, subject, doh, senioritydate, effectivedate, rategroup,
                    step, educationlevel, salary, hours, probationreportdate,
                    statebackgroundcheck, federalbackgroundcheck, stateeducatorid,
                    licensetype1, licenseissuedate1, licenseexpirationdate1, licenseterm1,
                    licensetype2, licenseissuedate2, licenseexpirationdate2, licenseterm2,
                    licensetype3, licenseissuedate3, licenseexpirationdate3, licenseterm3,
                    licensetype4, licenseissuedate4, licenseexpirationdate4, licenseterm4,
                    licensetype5, licenseissuedate5, licenseexpirationdate5, licenseterm5,
                    licensetype6, licenseissuedate6, licenseexpirationdate6, licenseterm6,
                    permissions, role, contractdays, siteID
                  )
                  VALUES (CURRENT_TIMESTAMP, 0, 0, '', ?, ?, '', ?, ?, ?, ?, ?, ?, '',
                    ?, ?, ?, ?, ?, '', ?, '', '', '', '', ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                  );";
    $insertStmt = $db->stmt_init();
    $insertStmt->prepare($insertSql);

    foreach($eligibleStaff as $staffID => $staff){

      if(!empty($staff["schools"])){
        $quotedSchools = array_map(function($s) { return "'$s'"; }, $staff["schools"]);
        $schoolsForSql = "(" . join(",", $quotedSchools) . ")";

        $schoolDeleteSql = "DELETE FROM abre_staff_schools
                            WHERE school_code NOT IN $schoolsForSql
                              AND is_imported = 1 AND staff_id = ? AND site_id = ?";
        $deleteSchoolsStmt = $db->stmt_init();
        $deleteSchoolsStmt->prepare($schoolDeleteSql);
        $deleteSchoolsStmt->bind_param("si", $staffID, $siteID);
        $deleteSchoolsStmt->execute();
        $deleteSchoolsStmt->close();

        $schoolsToImport = [];
        foreach($staff["schools"] as $school){
          $schoolsToImport []= "('$staffID', '$school', $siteID, 1, NOW())";
        }
        $schoolsImportString = implode(",", $schoolsToImport);

        $schoolInsertSql = "INSERT INTO abre_staff_schools
                              (staff_id, school_code, site_id, is_imported, imported_on)
                            VALUES $schoolsImportString
                            ON DUPLICATE KEY UPDATE
                              is_imported = 1,
                              imported_on = NOW()";
        $insertSchoolStmt = $db->stmt_init();
        $insertSchoolStmt->prepare($schoolInsertSql);
        $insertSchoolStmt->execute();
        $insertSchoolStmt->close();
      }

      $selectSql = "SELECT COUNT(*) FROM directory WHERE email = ? AND siteID = ?";
      $selectStmt = $db->stmt_init();
      $selectStmt->prepare($selectSql);
      $selectStmt->bind_param("si", $staff["email"], $siteID);
      $selectStmt->execute();
      $selectStmt->bind_result($existingRow);
      $selectStmt->fetch();
      $selectStmt->close();

      if(!$existingRow){
        $insertStmt->bind_param("sssssssssssssssssssssssssssssssssssssssssssssssssssssi",
          $staff["firstName"], $staff["lastName"], $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted, $staff["email"],
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
          $emptyStringEncrypted, $siteID);
        $insertStmt->execute();
      }
    }
    $insertStmt->close();

    //remove building relationships for staff no longer in import file
    $deleteOldBuildings = "DELETE FROM abre_staff_schools
                           WHERE site_id = ? AND is_imported = 1
                            AND imported_on < ?";
    $deleteOldStmt = $db->stmt_init();
    $deleteOldStmt->prepare($deleteOldBuildings);
    $deleteOldStmt->bind_param("is", $siteID, $jobStartTime);
    $deleteOldStmt->execute();
    $deleteOldStmt->close();

    //archive staff that are no longer in the import file
    $archiveOldStaff = "UPDATE Abre_Staff SET is_archived = 1
                        WHERE siteID = ? AND is_imported = 1
                          AND imported_on < ? AND school_year_id = ?";
    $archiveStmt = $db->stmt_init();
    $archiveStmt->prepare($archiveOldStaff);
    $archiveStmt->bind_param("isi", $siteID, $jobStartTime, $currentSchoolYearID);
    $archiveStmt->execute();
    $archiveStmt->close();

    //ensure the archive flags in the staff table are consistent with the import
    //flags in the directory
    $directoryArchiveUpdate = "UPDATE directory d
                                JOIN (
                                  SELECT EMail1, siteID, MIN(is_archived) isArchived
                                    FROM Abre_Staff
                                  WHERE siteID = ? AND is_imported = 1 AND school_year_id = ?
                                  GROUP BY EMail1, siteID
                                ) staff
                                ON staff.EMail1 = d.email AND staff.siteID = d.siteID
                                SET d.archived = staff.isArchived";
    $directoryArchiveStmt = $db->stmt_init();
    $directoryArchiveStmt->prepare($directoryArchiveUpdate);
    $directoryArchiveStmt->bind_param("ii", $siteID, $currentSchoolYearID);
    $directoryArchiveStmt->execute();
    $directoryArchiveStmt->close();

  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>
