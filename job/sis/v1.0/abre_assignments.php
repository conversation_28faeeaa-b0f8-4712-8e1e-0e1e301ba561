<?php

/*
* Copyright Abre.io Inc.
*/

ignore_user_abort(true);
set_time_limit(0);

require_once(dirname(__FILE__). '/../../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../../utils/functions.php');
require_once(dirname(__FILE__) . '/../../utils/logging.php');
require_once(dirname(__FILE__) . '/../../utils/DateHelper.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $currentSchoolYearID = getCurrentSchoolYearID($db);
  $isOneRosterDistrict = $config->flags->isOneRoster ?? false;

  try{
    $error = null;
    $skip = null;

    define('MAX_IMPORT_LIMIT', 100);

    $error = null;
    $skip = null;

    // Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception('Login to SFTP failed.');
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-assignments');
    $fileName = 'Abre_Assignments_1.0';
    $fileNameSecondary = 'Abre_Assignments_1';

    // Check for secondary files first
    $isFileLoaded = $sftp->get("$fileName.txt", $localTempFile);
    if (!$isFileLoaded) {
        $isFileLoaded = $sftp->get("$fileNameSecondary.txt", $localTempFile);
    }

    if ($isFileLoaded) {
        importAssignmentData($db, $siteID, $localTempFile, $currentSchoolYearID, $isOneRosterDistrict);
    } else {
        $skip = true;
        throw new Exception('No file found.');
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];
  $cronName = 'Assignment Import v1.0';
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}


function importAssignmentData($db, $siteID, $tempFile, $currentSchoolYearID, $isOneRosterDistrict){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName, $currentSchoolYearID, $isOneRosterDistrict);
    deleteExistingRecords($siteID, $db, $currentSchoolYearID);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE abre_assignments";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName, $currentSchoolYearID, $isOneRosterDistrict){
  $temporaryTableColumnsSQL = "
    INSERT INTO {$temporaryTableName}
    (
      student_id,
      course_code,
      section_code,
      school_code,
      staff_id,
      term_code,
      period,
      title,
      description,
      due_date,
      category,
      earned_points,
      possible_points,
      weight_percentage,
      comment,
      published,
      site_id,
      school_year_id
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, "\t");
    $studentID = trim($lineArray[0]);
    if($studentID != ""){
      array_push($records, buildInsertLine($db, $siteID, $studentID, $currentSchoolYearID, $lineArray, $isOneRosterDistrict));
    }

    if(count($records) == MAX_IMPORT_LIMIT){
      $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
      $stmt = $db->stmt_init();
      $stmt->prepare($temporaryTableInsertSQL);
      $stmt->execute();
      $stmt->close();

      $records = [];
    }
  }

  if(count($records)){
    $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }
}

function buildInsertLine($db, $siteID, $studentID, $currentSchoolYearID, $line, $isOneRosterDistrict){

  $courseCode = $line[1];
  $sectionCode = $line[2];
  $schoolCode = $line[3];
  $staffID = $line[4];
  $termCode = $isOneRosterDistrict ? $line[5]."-$siteID" : $line[5];
  $period = $line[6];
  $title = $db->escape_string($line[7]);

  $description = nullCheckWithQuotes($db->escape_string($line[8]));
  $dueDate = $line[9];
  if($dueDate != ""){
    $dateUTC = DateHelper::getLocalDateInUTC($db, $dueDate, $siteID);
    if($dateUTC === false){
      $dateToStore = "NULL";
    }else{
      $dateToStore = "'".$dateUTC->format("Y-m-d H:i:s")."'";
    }
  }else{
    $dateToStore = "NULL";
  }
  $category = nullCheckWithQuotes($db->escape_string($line[10]));
  $earnedPoints = nullCheck($line[11]);
  $possiblePoints = nullCheck($line[12]);
  $weightPercentage = nullCheck($line[13]);
  $comment = nullCheckWithQuotes($db->escape_string($line[14]));
  $published = nullCheckWithQuotes($line[15]);

  $sql = "
    (
      '$studentID',
      '$courseCode',
      '$sectionCode',
      '$schoolCode',
      '$staffID',
      '$termCode',
      '$period',
      '$title',
      $description,
      $dateToStore,
      $category,
      $earnedPoints,
      $possiblePoints,
      $weightPercentage,
      $comment,
      $published,
      $siteID,
      $currentSchoolYearID
    )";
  return $sql;
}

function deleteExistingRecords($siteID, $db, $currentSchoolYearID){
  $deleteSQL = "DELETE FROM abre_assignments WHERE site_id = ? AND school_year_id = ?";
  $deleteStmt = $db->stmt_init();
  $deleteStmt->prepare($deleteSQL);
  $deleteStmt->bind_param("ii", $siteID, $currentSchoolYearID);
  $deleteStmt->execute();
  $deleteStmt->close();
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $dropIDColumn = "ALTER TABLE {$temporaryTableName} DROP id";
  $dropIDStmt = $db->stmt_init();
  $dropIDStmt->prepare($dropIDColumn);
  $dropIDStmt->execute();
  $dropIDStmt->close();

  $insertSQL = "INSERT INTO abre_assignments
                  SELECT 0, t.* FROM {$temporaryTableName} t";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>
