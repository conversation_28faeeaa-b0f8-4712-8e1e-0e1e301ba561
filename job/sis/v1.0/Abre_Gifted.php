<?php

/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__). '/../../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../../utils/functions.php');
require_once(dirname(__FILE__) . '/../../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Gifted 1.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    // File
    $fileName = 'Abre_Gifted_1.0';
    $fileNameSecondary = 'Abre_Gifted_1';

    // Define
    define('SAFE_COLUMN_COUNT', 29);
    define('MAX_IMPORT_LIMIT', 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    // Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception('Login to SFTP failed.');
    }

    // Check for secondary files first (if it does...delete primary and rename secondary to primary)
    $secondaryFileTxt = $sftp->get("$fileNameSecondary.txt");
    $secondaryFileCsv = $sftp->get("$fileNameSecondary.csv");
    if ($secondaryFileTxt) {
        // Delete primary if it exists
        if ($sftp->file_exists("$fileName.txt")) {
            $sftp->delete("$fileName.txt");
        }
        // Rename secondary to primary
        $sftp->rename("$fileNameSecondary.txt", "$fileName.txt");
    } elseif ($secondaryFileCsv) {
        if ($sftp->file_exists("$fileName.csv")) {
            $sftp->delete("$fileName.csv");
        }
        $sftp->rename("$fileNameSecondary.csv", "$fileName.csv");
    }

    // Find File Primary
    $cronFile = $sftp->get("$fileName.txt");
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileName.csv");
        $fileType = 'csv';
        $columnSeparator = ',';
    } else {
        $cronFile = $sftp->get("$fileName.txt");
        $fileType = 'txt';
        $columnSeparator = "\t";
    }

    // Find File Secondary
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileNameSecondary.txt");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileNameSecondary.csv");
            $fileType = 'csv';
            $columnSeparator = ',';
        } else {
            $cronFile = $sftp->get("$fileNameSecondary.txt");
            $fileType = 'txt';
            $columnSeparator = "\t";
        }
    }

    if (!$cronFile) {
        $skip = true;
        throw new Exception('No file found.');
    } else {
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_Gifted (
      StudentID, Cognitive_Screening, Cognitive_Assessment, Cognitive_Served,
      Cognitive_Identified, Cognitive_Identified_Date, CreativeThinking_Screening,
      CreativeThinking_Assessment, CreativeThinking_Served,
      CreativeThinking_Identified, CreativeThinking_Identified_Date,
      Math_Screening, Math_Assessment, Math_Served, Math_Identified,
      Math_Identified_Date, ReadingWriting_Screening, ReadingWriting_Assessment,
      ReadingWriting_Served, ReadingWriting_Identified,
      ReadingWriting_Identified_Date, SocialStudies_Screening,
      SocialStudies_Assessment, SocialStudies_Served, SocialStudies_Identified,
      SocialStudies_Identified_Date, Science_Screening, Science_Assessment,
      Science_Served, Science_Identified, Science_Identified_Date,
      VisualPerfArts_Screening, VisualPerfArts_Assessment, VisualPerfArts_Served,
      VisualPerfArts_Identified, VisualPerfArts_Identified_Date, siteID,
      school_year_id
     ) VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM Abre_Gifted WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID");
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

      if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

        $studentID = trim($db->escape_string($data[0]));

        $cognitiveScreening = trim($db->escape_string($data[1]));
        $cognitiveAssessment = trim($db->escape_string($data[2]));
        $cognitiveServed = trim($db->escape_string($data[3]));
        $cognitiveIdentified = trim($db->escape_string($data[4]));
        $cognitiveIdentifiedDate = trim($db->escape_string($data[5]));
        $cognitiveIdentifiedDate = $cognitiveIdentifiedDate == "" ? 'null' : "'".$cognitiveIdentifiedDate."'";

        $creativeThinkingScreening = trim($db->escape_string($data[6]));
        $creativeThinkingAssessment = trim($db->escape_string($data[7]));
        $creativeThinkingServed = trim($db->escape_string($data[8]));
        $creativeThinkingIdentified = trim($db->escape_string($data[9]));
        $creativeThinkingIdentifiedDate = trim($db->escape_string($data[10]));
        $creativeThinkingIdentifiedDate = $creativeThinkingIdentifiedDate == "" ? 'null' : "'".$creativeThinkingIdentifiedDate."'";

        $mathScreening = trim($db->escape_string($data[11]));
        $mathAssessment = trim($db->escape_string($data[12]));
        $mathServed = trim($db->escape_string($data[13]));
        $mathIdentified = trim($db->escape_string($data[14]));
        $mathIdentifiedDate = trim($db->escape_string($data[15]));
        $mathIdentifiedDate = $mathIdentifiedDate == "" ? 'null' : "'".$mathIdentifiedDate."'";

        $readingWritingScreening = trim($db->escape_string($data[16]));
        $readingWritingAssessment = trim($db->escape_string($data[17]));
        $readingWritingServed = trim($db->escape_string($data[18]));
        $readingWritingIdentified = trim($db->escape_string($data[19]));
        $readingWritingIdentifiedDate = trim($db->escape_string($data[20]));
        $readingWritingIdentifiedDate = $readingWritingIdentifiedDate == "" ? 'null' : "'".$readingWritingIdentifiedDate."'";

        $socialStudiesScreening = trim($db->escape_string($data[21]));
        $socialStudiesAssessment = trim($db->escape_string($data[22]));
        $socialStudiesServed = trim($db->escape_string($data[23]));
        $socialStudiesIdentified = trim($db->escape_string($data[24]));
        $socialStudiesIdentifiedDate = trim($db->escape_string($data[25]));
        $socialStudiesIdentifiedDate = $socialStudiesIdentifiedDate == "" ? 'null' : "'".$socialStudiesIdentifiedDate."'";

        $visualPerfArtsScreening = trim($db->escape_string($data[26]));
        $visualPerfArtsAssessment = trim($db->escape_string($data[27]));
        $visualPerfArtsServed = trim($db->escape_string($data[28]));
        $visualPerfArtsIdentified = trim($db->escape_string($data[29]));
        $visualPerfArtsIdentifiedDate = trim($db->escape_string($data[30]));
        $visualPerfArtsIdentifiedDate = $visualPerfArtsIdentifiedDate == "" ? 'null' : "'".$visualPerfArtsIdentifiedDate."'";

        $scienceScreening = trim($db->escape_string($data[31]));
        $scienceAssessment = trim($db->escape_string($data[32]));
        $scienceServed = trim($db->escape_string($data[33]));
        $scienceIdentified = trim($db->escape_string($data[34]));
        $scienceIdentifiedDate = trim($db->escape_string($data[35]));
        $scienceIdentifiedDate = $scienceIdentifiedDate == "" ? 'null' : "'".$scienceIdentifiedDate."'";

        $valuesToImport []= "(
          '$studentID', '$cognitiveScreening', '$cognitiveAssessment',
          '$cognitiveServed', '$cognitiveIdentified', $cognitiveIdentifiedDate,
          '$creativeThinkingScreening', '$creativeThinkingAssessment',
          '$creativeThinkingServed', '$creativeThinkingIdentified',
          $creativeThinkingIdentifiedDate, '$mathScreening',
          '$mathAssessment', '$mathServed', '$mathIdentified', $mathIdentifiedDate,
          '$readingWritingScreening', '$readingWritingAssessment',
          '$readingWritingServed', '$readingWritingIdentified',
          $readingWritingIdentifiedDate, '$socialStudiesScreening',
          '$socialStudiesAssessment', '$socialStudiesServed',
          '$socialStudiesIdentified', $socialStudiesIdentifiedDate,
          '$scienceScreening', '$scienceAssessment', '$scienceServed',
          '$scienceIdentified', $scienceIdentifiedDate, '$visualPerfArtsScreening',
          '$visualPerfArtsAssessment', '$visualPerfArtsServed',
          '$visualPerfArtsIdentified', $visualPerfArtsIdentifiedDate, '$siteID',
          $currentSchoolYearID
        )";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
      }

      $line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  
  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
