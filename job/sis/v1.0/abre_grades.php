<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../../utils/functions.php');
require_once(dirname(__FILE__) . '/../../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  ignore_user_abort(true);
  set_time_limit(0);

  $currentSchoolYearID = getCurrentSchoolYearID($db);
  $isOneRosterDistrict = $config->flags->isOneRoster ?? false;

  try{
    $error = null;
    $skip = null;

    define("MAX_IMPORT_LIMIT", 100000);

    $jobStartDateTime = new DateTime("now", new DateTimeZone('UTC'));
    $jobStartTime = $jobStartDateTime->format("Y-m-d H:i:s");

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-grades');
    $isFileLoaded = $sftp->get('Abre_Grades_1.0.txt', $localTempFile);

    if($isFileLoaded){
      importGradeData($db, $siteID, $localTempFile, $jobStartTime, $currentSchoolYearID, $isOneRosterDistrict);
    }else{
      $skip = true;
      throw new Exception("No file found.");
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];
  $cronName = 'Grades Import v1.0';
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}


function importGradeData($db, $siteID, $tempFile, $cutoffDate, $currentSchoolYearID, $isOneRosterDistrict){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName, $currentSchoolYearID, $isOneRosterDistrict);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
    deleteOldRecords($db, $siteID, $cutoffDate, $currentSchoolYearID);
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE abre_grades";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName, $currentSchoolYearID, $isOneRosterDistrict){
  $temporaryTableInsertSQL = "
    INSERT INTO {$temporaryTableName}
    (
      student_id,
      course_code,
      section_code,
      school_code,
      staff_id,
      term_code,
      period,
      letter_grade,
      percentage,
      performance,
      site_id,
      imported_on,
      school_year_id
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, "\t");
    $studentID = trim($lineArray[0]);
    if($studentID != ""){
      array_push($records, buildInsertLine($siteID, $currentSchoolYearID, $studentID, $lineArray, $isOneRosterDistrict));
    }
    if(count($records) == MAX_IMPORT_LIMIT){
      insertRows($db, $temporaryTableInsertSQL, $records, "ON DUPLICATE KEY UPDATE student_id = student_id");
      $records = [];
    }
  }
  if(count($records)){
    insertRows($db, $temporaryTableInsertSQL, $records, "ON DUPLICATE KEY UPDATE student_id = student_id");
  }
}

function buildInsertLine($siteID, $currentSchoolYearID, $studentID, $line, $isOneRosterDistrict){

  $courseCode = $line[1];
  $sectionCode = $line[2];
  $schoolCode = $line[3];
  $staffID = $line[4];
  $termCode = $isOneRosterDistrict ? $line[5]."-$siteID" : $line[5];
  $period = $line[6];

  $letterGrade = nullCheckWithQuotes($line[7]);
  $percentage = nullCheck($line[8]);
  $performance = nullCheckWithQuotes($line[9]);

  $sql = "
    (
      '$studentID',
      '$courseCode',
      '$sectionCode',
      '$schoolCode',
      '$staffID',
      '$termCode',
      '$period',
      $letterGrade,
      $percentage,
      $performance,
      $siteID,
      NOW(),
      $currentSchoolYearID
    )";
  return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $insertSQL = "
    INSERT INTO abre_grades
    (
      student_id,
      course_code,
      section_code,
      school_code,
      staff_id,
      term_code,
      period,
      letter_grade,
      percentage,
      performance,
      site_id,
      imported_on,
      school_year_id
    )
    SELECT
      t.student_id,
      t.course_code,
      t.section_code,
      t.school_code,
      t.staff_id,
      t.term_code,
      t.period,
      t.letter_grade,
      t.percentage,
      t.performance,
      ?,
      t.imported_on,
      t.school_year_id
    FROM {$temporaryTableName} t
    ON DUPLICATE KEY UPDATE
      letter_grade = t.letter_grade,
      percentage = t.percentage,
      performance = t.performance,
      imported_on = NOW();
  ";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->bind_param('i', $siteID);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}

function deleteOldRecords($db, $siteID, $cutoffDate, $currentSchoolYearID){
  $deleteRecordSql = "DELETE FROM abre_grades
                      WHERE site_id = ? AND imported_on < ?
                        AND school_year_id = ?";
  $stmt = $db->stmt_init();
  $stmt->prepare($deleteRecordSql);
  $stmt->bind_param("isi", $siteID, $cutoffDate, $currentSchoolYearID);
  $stmt->execute();
  $stmt->close();
}
?>
