<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../../utils/functions.php');
require_once(dirname(__FILE__) . '/../../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Parent Contacts 1.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    // File
    $fileName = 'Abre_ParentContacts_1.0';
    $fileNameSecondary = 'Abre_ParentContacts_1';

    // Define
    define("SAFE_COLUMN_COUNT", 12);
    define("MAX_IMPORT_LIMIT", 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    // Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    // Check for secondary files first (if it does...delete primary and rename secondary to primary)
    $secondaryFileTxt = $sftp->get("$fileNameSecondary.txt");
    $secondaryFileCsv = $sftp->get("$fileNameSecondary.csv");
    if ($secondaryFileTxt) {
        // Delete primary if it exists
        if ($sftp->file_exists("$fileName.txt")) {
            $sftp->delete("$fileName.txt");
        }
        // Rename secondary to primary
        $sftp->rename("$fileNameSecondary.txt", "$fileName.txt");
    } elseif ($secondaryFileCsv) {
        if ($sftp->file_exists("$fileName.csv")) {
            $sftp->delete("$fileName.csv");
        }
        $sftp->rename("$fileNameSecondary.csv", "$fileName.csv");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

    // Find File Secondary
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileNameSecondary.txt");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileNameSecondary.csv");
            $fileType = 'csv';
            $columnSeparator = ',';
        } else {
            $cronFile = $sftp->get("$fileNameSecondary.txt");
            $fileType = 'txt';
            $columnSeparator = "\t";
        }
    }

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

  	$rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_ParentContacts (
      StudentID, FirstName, MiddleName, LastName, AddressLine1, AddressLine2,
      City, State, Zip, Phone1, Phone2, Email1, relationship, description,
      primary_contact, siteID, school_year_id
     ) VALUES ";

  	$line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM Abre_ParentContacts WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID");
  	do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$studentID = trim($db->escape_string($data[0]));
  			$firstName = trim($db->escape_string($data[1]));
  			$middleName = trim($db->escape_string($data[2]));
  			$lastName = trim($db->escape_string($data[3]));
  			$addressLine1 = trim($db->escape_string($data[4]));
  			$addressLine2 = trim($db->escape_string($data[5]));
  			$city = trim($db->escape_string($data[6]));
  			$state = trim($db->escape_string($data[7]));
  			$zip = trim($db->escape_string($data[8]));
  			$phone1 = trim($db->escape_string($data[9]));
  			$phone2 = trim($db->escape_string($data[10]));
  			$email = trim($db->escape_string($data[11]));
        $relationship = trim($db->escape_string($data[12]));
        $relationship = $relationship == "" ? 'null' : "'".$relationship."'";
        $description = trim($db->escape_string($data[13]));
        $description = $description == "" ? 'null' : "'".$description."'";
        $primaryContact = trim($db->escape_string($data[14]));

        $valuesToImport []= "(
          '$studentID', '$firstName', '$middleName', '$lastName', '$addressLine1',
          '$addressLine2', '$city', '$state', '$zip', '$phone1', '$phone2', '$email',
          $relationship, $description, '$primaryContact', '$siteID', $currentSchoolYearID
        )";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
  	}while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>
