<?php

/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config)
{

    $cronName = 'Abre SFTP to GCS';

    try {

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        // Get the Current Date
        $currentDate = date("Ymd");

        // STEP 1 - MOVE ABRE SFTP FILES
        // Abre SFTP Files - Connect to Database to get supported files
        $jobDefinitionID = 2;
        $allFiles = [];
        $sql = "SELECT jd.filenames FROM job_definition_job_config_definition jdjcd
        LEFT JOIN job_definition jd ON jdjcd.job_definition_id = jd.id
        WHERE jdjcd.job_config_definition_id = ? AND filenames IS NOT NULL";
        $stmt = $db->stmt_init();
        if ($stmt->prepare($sql)) {
            $stmt->bind_param("i", $jobDefinitionID);
            $stmt->execute();

            // Bind the result to a variable
            $stmt->bind_result($filename);

            // Loop through the results
            while ($stmt->fetch()) {
                $filenames = array_map('trim', explode(",", $filename));

                foreach ($filenames as $filename) {
                    $allFiles[] = $filename;
                }
            }

            $stmt->close();
        }

        // Connect to SFTP
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.");
        }

        // Get the list of files on the SFTP server
        $files = $sftp->nlist();

        // Loop through each file and log the filename
        foreach ($files as $file) {

            // Import supported filenames
            if (in_array($file, $allFiles)) {

                // Get the file from SFTP
                $cronFile = $sftp->get($file);

                // Authenticate with service account
                $storage = new StorageClient([
                    'projectId' => "abre-production"
                ]);

                // Upload SFTP File to Google Cloud Storage Bucket
                $bucketName = "prd-landing-zone";
                $bucket = $storage->bucket($bucketName);
                $bucket->upload($cronFile, [
                    'name' => "$currentDate/site-id/$siteID/$file"
                ]);

                // Upload SFTP File to Google Cloud Storage Bucket (Alternate Method)
                $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
                $folderName = pathinfo($file, PATHINFO_FILENAME);
                $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
                $bucketName = "prd-landing-zone";
                $bucket = $storage->bucket($bucketName);
                $bucket->upload($cronFile, [
                    'name' => "$currentDate/filename/$folderName/$modifiedFile"
                ]);
            }
        }

        // STEP 2 - MOVE iREADY SFTP FILES
        // iReady SFTP Files - Connect to Database to get supported files
        $jobDefinitionID = 30;
        $alliReadyFiles = [];
        $sql = "SELECT j.custom_config,
        (SELECT config_settings FROM job_config WHERE target=? AND job_config_definition_id=30 LIMIT 1) AS account
        FROM job j
        WHERE j.target=? AND (j.job_definition_id=102 OR j.job_definition_id=103 OR j.job_definition_id=129)";
        $stmt = $db->stmt_init();
        if ($stmt->prepare($sql)) {
            $stmt->bind_param("ii", $siteID, $siteID);
            $stmt->execute();

            // Bind the result to a variable
            $stmt->bind_result($config, $account);

            // Loop through the jobs
            while ($stmt->fetch()) {

                $configArray = json_decode($config, true);
                $alliReadyFiles[] = $configArray['fileName'];

                // Set Account iReady variables
                $iReadyAccountArray = json_decode($account, true);
                $iReadyUserName = $iReadyAccountArray['userName'];
                $iReadyPassword = $iReadyAccountArray['password'];
                $iReadyIP = $iReadyAccountArray['ip'];
            }

            $stmt->close();
        }

        //If iReady isn't empty
        if (!empty($alliReadyFiles)) {

            // Connect to iReady SFTP
            $sftp = new SFTP($iReadyIP);
            if (!$sftp->login($iReadyUserName, $iReadyPassword)) {
                throw new Exception("Login to SFTP failed.");
            }

            // Loop through each iReady File
            foreach ($alliReadyFiles as $file) {

                //Add CSV Extension
                $file = $file . '.csv';

                // Get the file from SFTP
                $cronFile = $sftp->get($file);

                //Get the real filename
                $file = substr($file, strrpos($file, '/') + 1);

                // Add "iready" in front of the $file
                $file = "iready_" . $file;

                // Authenticate with service account
                $storage = new StorageClient([
                    'projectId' => "abre-production"
                ]);

                // Upload SFTP File to Google Cloud Storage Bucket
                $bucketName = "prd-landing-zone";
                $bucket = $storage->bucket($bucketName);
                $bucket->upload($cronFile, [
                    'name' => "$currentDate/site-id/$siteID/$file"
                ]);

                // Upload SFTP File to Google Cloud Storage Bucket (Alternate Method)
                $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
                $folderName = pathinfo($file, PATHINFO_FILENAME);
                $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
                $bucketName = "prd-landing-zone";
                $bucket = $storage->bucket($bucketName);
                $bucket->upload($cronFile, [
                    'name' => "$currentDate/filename/$folderName/$modifiedFile"
                ]);
            }
        }

        // STEP 3 - MOVE FASTBRIDGE SFTP FILES
        // FastBridge SFTP Files - Connect to Database to get supported files
        $jobDefinitionID = 30;
        $fastBridgeAccountArray = [];
        $sql = "SELECT jc.config_settings
                FROM job_definition_job_config_definition jdjcd
                LEFT JOIN job j
                ON jdjcd.job_definition_id = j.job_definition_id
                LEFT JOIN job_config jc
                ON jdjcd.job_config_definition_id = jc.job_config_definition_id
                LEFT JOIN job_definition jd
                ON jdjcd.job_definition_id = jd.id
                WHERE j.target= ? AND jc.target = ? AND jdjcd.job_config_definition_id = 28
                GROUP BY config_settings";
        $stmt = $db->stmt_init();
        if ($stmt->prepare($sql)) {
            $stmt->bind_param("ii", $siteID, $siteID);
            $stmt->execute();

            // Bind the result to a variable
            $stmt->bind_result($account);

            // Loop through the jobs
            while ($stmt->fetch()) {

                // Set Account FastBridge variables
                $fastBridgeAccountArray = json_decode($account, true);
                $fastBridgeUserName = $fastBridgeAccountArray['userName'];
                $fastBridgePassword = $fastBridgeAccountArray['password'];
                $fastBridgeIP = $fastBridgeAccountArray['ip'];
            }

            $stmt->close();
        }

        //If FastBridge isn't empty
        if (!empty($fastBridgeAccountArray)) {

            // Connect to FastBridge SFTP
            $sftp = new SFTP($fastBridgeIP);
            if (!$sftp->login($fastBridgeUserName, $fastBridgePassword)) {
                throw new Exception("Login to SFTP failed.");
            }

            // Get the list of files on the SFTP server
            $files = $sftp->nlist();

            // Loop through each file and log the filename
            foreach ($files as $file) {

                // Import supported filenames
                if (strpos($file, '.txt') !== false || strpos($file, '.csv') !== false) {

                    // Get the file from SFTP
                    $cronFile = $sftp->get($file);

                    // Add "fastbridge" in front of the $file
                    $file = "fastbridge_" . $file;

                    // Authenticate with service account
                    $storage = new StorageClient([
                        'projectId' => "abre-production"
                    ]);

                    // Upload SFTP File to Google Cloud Storage Bucket
                    $bucketName = "prd-landing-zone";
                    $bucket = $storage->bucket($bucketName);
                    $bucket->upload($cronFile, [
                        'name' => "$currentDate/site-id/$siteID/$file"
                    ]);

                    // Upload SFTP File to Google Cloud Storage Bucket (Alternate Method)
                    $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
                    $folderName = pathinfo($file, PATHINFO_FILENAME);
                    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
                    $bucketName = "prd-landing-zone";
                    $bucket = $storage->bucket($bucketName);
                    $bucket->upload($cronFile, [
                        'name' => "$currentDate/filename/$folderName/$modifiedFile"
                    ]);
                }
            }
        }

        // STEP 4 - MOVE APERTURE EDUCATION SFTP FILES
        // Aperture Education SFTP Files - Connect to Database to get supported files
        $jobDefinitionID = 35;
        $apertureAccountArray = [];
        $sql = "SELECT jc.`config_settings`
                FROM `job_definition_job_config_definition` jdjcd
                LEFT JOIN `job` j
                ON jdjcd.`job_definition_id` = j.`job_definition_id`
                LEFT JOIN `job_config` jc
                ON jdjcd.`job_config_definition_id` = jc.`job_config_definition_id`
                LEFT JOIN `job_definition` jd
                ON jdjcd.`job_definition_id` = jd.`id`
                WHERE j.`target`= ? AND jc.`target` = ? AND jdjcd.`job_config_definition_id` = 35
                GROUP BY `config_settings`";
        $stmt = $db->stmt_init();
        if ($stmt->prepare($sql)) {
            $stmt->bind_param("ii", $siteID, $siteID);
            $stmt->execute();

            // Bind the result to a variable
            $stmt->bind_result($account);

            // Loop through the jobs
            while ($stmt->fetch()) {
                // Set Account Aperture Education variables
                $apertureAccountArray = json_decode($account, true);
                $apertureUserName = $apertureAccountArray['userName'];
                $aperturePassword = $apertureAccountArray['password'];
                $apertureIP = $apertureAccountArray['ip'];
            }

            $stmt->close();
        }

        // If Aperture Education isn't empty
        if (!empty($apertureAccountArray)) {
            // Connect to Aperture Education SFTP
            $sftp = new SFTP($apertureIP);
            if (!$sftp->login($apertureUserName, $aperturePassword)) {
                throw new Exception("Login to Aperture Education SFTP failed.");
            }

            // Get the list of files on the SFTP server
            $files = $sftp->nlist();

            // Loop through each file and log the filename
            foreach ($files as $file) {
                // Import supported filenames (adjust file extensions as needed)
                if (strpos($file, '.txt') !== false || strpos($file, '.csv') !== false) {
                    // Get the file from SFTP
                    $cronFile = $sftp->get($file);

                    // Add "aperture" in front of the $file
                    $file = 'aperture_' . $file;

                    // Authenticate with service account
                    $storage = new StorageClient([
                        'projectId' => 'abre-production'
                    ]);

                    // Upload SFTP File to Google Cloud Storage Bucket
                    $bucketName = 'prd-landing-zone';
                    $bucket = $storage->bucket($bucketName);
                    $bucket->upload($cronFile, [
                        'name' => "$currentDate/site-id/$siteID/$file"
                    ]);

                    // Upload SFTP File to Google Cloud Storage Bucket (Alternate Method)
                    $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
                    $folderName = pathinfo($file, PATHINFO_FILENAME);
                    $modifiedFile = $folderName . '-' . $siteID . '.' . $fileExtension;
                    $bucketName = 'prd-landing-zone';
                    $bucket = $storage->bucket($bucketName);
                    $bucket->upload($cronFile, [
                        'name' => "$currentDate/filename/$folderName/$modifiedFile"
                    ]);
                }
            }
        }

        $status = CRON_SUCCESS;
        $details = "Successfully moved SFTP to GCS";
    } catch (Exception $ex) {
        $error = $ex->getMessage();
        $status = CRON_FAILURE;
        $details = "Could not move SFTP to GCS";
    }

    // Log the cron job finish
    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
