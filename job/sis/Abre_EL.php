<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre EL';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    // File
    $fileName = 'Abre_EL_1.0';
    $fileNameSecondary = 'Abre_EL_1';

    // Define
    define('SAFE_COLUMN_COUNT', 13);
    define('MAX_IMPORT_LIMIT', 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    // Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception('Login to SFTP failed.');
    }

    // Check for secondary files first (if it does...delete primary and rename secondary to primary)
    $secondaryFileTxt = $sftp->get("$fileNameSecondary.txt");
    $secondaryFileCsv = $sftp->get("$fileNameSecondary.csv");
    if ($secondaryFileTxt) {
        // Delete primary if it exists
        if ($sftp->file_exists("$fileName.txt")) {
            $sftp->delete("$fileName.txt");
        }
        // Rename secondary to primary
        $sftp->rename("$fileNameSecondary.txt", "$fileName.txt");
    } elseif ($secondaryFileCsv) {
        if ($sftp->file_exists("$fileName.csv")) {
            $sftp->delete("$fileName.csv");
        }
        $sftp->rename("$fileNameSecondary.csv", "$fileName.csv");
    }

    // Find File Primary
    $cronFile = $sftp->get("$fileName.txt");
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileName.csv");
        $fileType = 'csv';
        $columnSeparator = ',';
    } else {
        $cronFile = $sftp->get("$fileName.txt");
        $fileType = 'txt';
        $columnSeparator = "\t";
    }

    // Find File Secondary
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileNameSecondary.txt");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileNameSecondary.csv");
            $fileType = 'csv';
            $columnSeparator = ',';
        } else {
            $cronFile = $sftp->get("$fileNameSecondary.txt");
            $fileType = 'txt';
            $columnSeparator = "\t";
        }
    }

    if (!$cronFile) {
        $skip = true;
        throw new Exception('No file found.');
    } else {
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

  	$rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO abre_el (
      student_id, status, status_code, date_identified, home_language, home_language_code,
      native_language, native_language_code, place_of_birth, country_of_birth, country_of_birth_code,
      us_start_date, english_proficiency,
      site_id, school_year_id
     ) VALUES ";

  	$line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM abre_el WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

      if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

        $studentId = trim($db->escape_string($data[0]));
        $elStatus = trim($db->escape_string($data[1]));
        $statusCode = trim($db->escape_string($data[2]));
        $dateIdentified = trim($db->escape_string($data[3]));
        $homeLanguage = trim($db->escape_string($data[4]));
        $homeLanguageCode = trim($db->escape_string($data[5]));
        $nativeLanguage = trim($db->escape_string($data[6]));
        $nativeLanguageCode = trim($db->escape_string($data[7]));
        $placeOfBirth = trim($db->escape_string($data[8]));
        $countryOfBirth = trim($db->escape_string($data[9]));
        $countryOfBirthCode = trim($db->escape_string($data[10]));
        $usStartDate = trim($db->escape_string($data[11]));
        $englishProficiency = trim($db->escape_string($data[12]));

        $valuesToImport []= "(
          '$studentId', '$elStatus', '$statusCode',
          '$dateIdentified', '$homeLanguage', '$homeLanguageCode',
          '$nativeLanguage', '$nativeLanguageCode',
          '$placeOfBirth', '$countryOfBirth', '$countryOfBirthCode',
          '$usStartDate', '$englishProficiency', '$siteID',
          $currentSchoolYearID
        )";

        if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
        }
      }

      $line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
