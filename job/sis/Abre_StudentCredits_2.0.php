<?php

/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Student Credits 2.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    // File
    $fileName = 'Abre_StudentCredits_2.0';
    $fileNameSecondary = 'Abre_StudentCredits_2';

    // Define
    define('SAFE_COLUMN_COUNT', 5);
    define('MAX_IMPORT_LIMIT', 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    // Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception('Login to SFTP failed.');
    }

    // Find File
    $cronFile = $sftp->get("$fileName.txt");
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileName.csv");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileNameSecondary.txt");
            if (!$cronFile) {
                $cronFile = $sftp->get("$fileNameSecondary.csv");
                if ($cronFile) {
                    $fileType = 'csv';
                    $columnSeparator = ',';
                }
            } else {
                $fileType = 'txt';
                $columnSeparator = "\t";
            }
        } else {
            $fileType = 'csv';
            $columnSeparator = ',';
        }
    } else {
        $fileType = 'txt';
        $columnSeparator = "\t";
    }

    if (!$cronFile) {
        $skip = true;
        throw new Exception('No file found.');
    } else {
        $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
        if ($fileDetails['isEmpty']) {
            $skip = true;
            throw new Exception('File is empty.');
        } elseif ($fileDetails['hasHeaderRow'] && !$fileDetails['hasDataRow']) {
            $skip = true;
            throw new Exception('File only contains a header row.');
        } elseif (!$fileDetails['hasValidDataRow']) {
            throw new Exception('No valid data row found.');
        }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_StudentCredits (
      student_id, total_credits, academic_end_year, term, subject_area, site_id, school_year_id
     ) VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM Abre_StudentCredits WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
  	do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$studentId = trim($db->escape_string($data[0]));
        $totalCredits = trim($db->escape_string($data[1]));
        $academicEndYear = trim($db->escape_string($data[2]));
        $term = trim($db->escape_string($data[3]));
        $subjectArea = trim($db->escape_string($data[4]));

        $valuesToImport []= "('$studentId', '$totalCredits', '$academicEndYear', '$term', '$subjectArea', '$siteID', $currentSchoolYearID)";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>
