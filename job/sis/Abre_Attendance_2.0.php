<?php

/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Attendance 2.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    // File
    $fileName = 'Abre_Attendance_2.0';
    $fileNameSecondary = 'Abre_Attendance_2';

    // Define
    define('SAFE_COLUMN_COUNT', 8);
    define('MAX_IMPORT_LIMIT', 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    // Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception('Login to SFTP failed.');
    }

    // Check for secondary files first (if it does...delete primary and rename secondary to primary)
    $secondaryFileTxt = $sftp->get("$fileNameSecondary.txt");
    $secondaryFileCsv = $sftp->get("$fileNameSecondary.csv");
    if ($secondaryFileTxt) {
        // Delete primary if it exists
        if ($sftp->file_exists("$fileName.txt")) {
            $sftp->delete("$fileName.txt");
        }
        // Rename secondary to primary
        $sftp->rename("$fileNameSecondary.txt", "$fileName.txt");
    } elseif ($secondaryFileCsv) {
        if ($sftp->file_exists("$fileName.csv")) {
            $sftp->delete("$fileName.csv");
        }
        $sftp->rename("$fileNameSecondary.csv", "$fileName.csv");
    }

    // Find File Primary
    $cronFile = $sftp->get("$fileName.txt");
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileName.csv");
        $fileType = 'csv';
        $columnSeparator = ',';
    } else {
        $cronFile = $sftp->get("$fileName.txt");
        $fileType = 'txt';
        $columnSeparator = "\t";
    }

    // Find File Secondary
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileNameSecondary.txt");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileNameSecondary.csv");
            $fileType = 'csv';
            $columnSeparator = ',';
        } else {
            $cronFile = $sftp->get("$fileNameSecondary.txt");
            $fileType = 'txt';
            $columnSeparator = "\t";
        }
    }

    if (!$cronFile) {
        $skip = true;
        throw new Exception('No file found.');
    } else {
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_Attendance (
      StudentID, SchoolCode, AbsenceDate, AbsenceReasonCode,
      AbsenceReasonDescription, AbsenceCategoryCode, HoursMissed, Level, siteID, school_year_id
     ) VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM Abre_Attendance WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID");
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$studentID = trim($db->escape_string($data[0]));
  			$schoolCode = trim($db->escape_string($data[1]));
  			$absenceDate = trim($db->escape_string($data[2]));
  			$absenceReasonCode = trim($db->escape_string($data[3]));
  			$absenceReasonDescription = trim($db->escape_string($data[4]));
  			$absenceCategoryCode = trim($db->escape_string($data[5]));
        $hoursMissed = trim($db->escape_string($data[6]));
        $attendanceLevel = trim($db->escape_string($data[7]));

        $valuesToImport []= "(
          '$studentID', '$schoolCode', '$absenceDate', '$absenceReasonCode',
          '$absenceReasonDescription', '$absenceCategoryCode', '$hoursMissed', '$attendanceLevel', '$siteID', $currentSchoolYearID
        )";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
  				$valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

  	if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>
