<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Gifted';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "Abre_Gifted";

    //Define
    define("SAFE_COLUMN_COUNT", 29);
    define("MAX_IMPORT_LIMIT", 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

  	$rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_Gifted (
      StudentID, Cognitive_Screening, Cognitive_Assessment,
      Cognitive_Served, Cognitive_Identified, CreativeThinking_Screening,
      CreativeThinking_Assessment, CreativeThinking_Served,
      CreativeThinking_Identified, Math_Screening, Math_Assessment,
      Math_Served, Math_Identified, ReadingWriting_Screening,
      ReadingWriting_Assessment, ReadingWriting_Served,
      ReadingWriting_Identified, SocialStudies_Screening,
      SocialStudies_Assessment, SocialStudies_Served,
      SocialStudies_Identified, Science_Screening, Science_Assessment,
      Science_Served, Science_Identified, VisualPerfArts_Screening,
      VisualPerfArts_Assessment, VisualPerfArts_Served, VisualPerfArts_Identified,
      siteID, school_year_id
     ) VALUES ";

  	$line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM Abre_Gifted WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID");
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

      if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

        $studentID = trim($db->escape_string($data[0]));

        $cognitiveScreening = trim($db->escape_string($data[1]));
        $cognitiveAssessment = trim($db->escape_string($data[2]));
        $cognitiveServed = trim($db->escape_string($data[3]));
        $cognitiveIdentified = trim($db->escape_string($data[4]));

        $creativeThinkingScreening = trim($db->escape_string($data[5]));
        $creativeThinkingAssessment = trim($db->escape_string($data[6]));
        $creativeThinkingServed = trim($db->escape_string($data[7]));
        $creativeThinkingIdentified = trim($db->escape_string($data[8]));

        $mathScreening = trim($db->escape_string($data[9]));
        $mathAssessment = trim($db->escape_string($data[10]));
        $mathServed = trim($db->escape_string($data[11]));
        $mathIdentified = trim($db->escape_string($data[12]));

        $readingWritingScreening = trim($db->escape_string($data[13]));
        $readingWritingAssessment = trim($db->escape_string($data[14]));
        $readingWritingServed = trim($db->escape_string($data[15]));
        $readingWritingIdentified = trim($db->escape_string($data[16]));

        $socialStudiesScreening = trim($db->escape_string($data[17]));
        $socialStudiesAssessment = trim($db->escape_string($data[18]));
        $socialStudiesServed = trim($db->escape_string($data[19]));
        $socialStudiesIdentified = trim($db->escape_string($data[20]));

        $visualPerfArtsScreening = trim($db->escape_string($data[21]));
        $visualPerfArtsAssessment = trim($db->escape_string($data[22]));
        $visualPerfArtsServed = trim($db->escape_string($data[23]));
        $visualPerfArtsIdentified = trim($db->escape_string($data[24]));

        $scienceScreening = trim($db->escape_string($data[25]));
        $scienceAssessment = trim($db->escape_string($data[26]));
        $scienceServed = trim($db->escape_string($data[27]));
        $scienceIdentified = trim($db->escape_string($data[28]));

        $valuesToImport []= "(
          '$studentID', '$cognitiveScreening', '$cognitiveAssessment',
          '$cognitiveServed', '$cognitiveIdentified', '$creativeThinkingScreening',
          '$creativeThinkingAssessment', '$creativeThinkingServed',
          '$creativeThinkingIdentified', '$mathScreening', '$mathAssessment',
          '$mathServed', '$mathIdentified', '$readingWritingScreening',
          '$readingWritingAssessment', '$readingWritingServed',
          '$readingWritingIdentified', '$socialStudiesScreening',
          '$socialStudiesAssessment', '$socialStudiesServed',
          '$socialStudiesIdentified', '$scienceScreening',
          '$scienceAssessment', '$scienceServed', '$scienceIdentified',
          '$visualPerfArtsScreening', '$visualPerfArtsAssessment',
          '$visualPerfArtsServed', '$visualPerfArtsIdentified', '$siteID',
          $currentSchoolYearID
        )";

        if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
        }
      }

      $line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
