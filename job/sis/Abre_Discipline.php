<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Discipline';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "Abre_Discipline";

    //Define
    define("SAFE_COLUMN_COUNT", 28);
    define("MAX_IMPORT_LIMIT", 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

    if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO abre_discipline (
      student_id, end_year, sasid, role, incident_building, incident_id, incident_title, incident_date, incident_time,
      location, submitted_by, status, event_id, event_type, event_code, event_name, event_state_code, event_state_name,
      event_category, action_id, action_code, action_name, action_state_code, action_state_name, suspension_start_date, suspension_end_date, expulsion, law_referred,
      site_id, school_year_id
     ) VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM abre_discipline WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
  	do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$studentId = trim($db->escape_string($data[0]));
        $ssid = trim($db->escape_string($data[1]));
        $endYear = trim($db->escape_string($data[2]));
        $role = trim($db->escape_string($data[3]));
        $incidentBuilding = trim($db->escape_string($data[4]));
        $incidentID = trim($db->escape_string($data[5]));
        $incidentTitle = trim($db->escape_string($data[6]));
        $incidentDate = trim($db->escape_string($data[7]));
        $IncidentTime = trim($db->escape_string($data[8]));
        $location = trim($db->escape_string($data[9]));
        $submittedBy = trim($db->escape_string($data[10]));
        $status = trim($db->escape_string($data[11]));
        $eventID = trim($db->escape_string($data[12]));
        $eventType = trim($db->escape_string($data[13]));
        $eventCode = trim($db->escape_string($data[14]));
        $eventName = trim($db->escape_string($data[15]));
        $eventStateCode = trim($db->escape_string($data[16]));
        $eventStateName = trim($db->escape_string($data[17]));
        $eventCategory = trim($db->escape_string($data[18]));
        $actionID = trim($db->escape_string($data[19]));
        $actionCode = trim($db->escape_string($data[20]));
        $actionName = trim($db->escape_string($data[21]));
        $actionStateCode = trim($db->escape_string($data[22]));
        $actionStateName = trim($db->escape_string($data[23]));
        $suspensionStartDate = trim($db->escape_string($data[24]));
        $suspensionEndDate = trim($db->escape_string($data[25]));
        $expulsion = trim($db->escape_string($data[26]));
        $lawReferred = trim($db->escape_string($data[27]));

        $valuesToImport []= "('$studentId', '$endYear', '$ssid', '$role', '$incidentBuilding', '$incidentID',
                              '$incidentTitle', '$incidentDate', '$IncidentTime', '$location', '$submittedBy',
                              '$status', '$eventID', '$eventType', '$eventCode', '$eventName',
                              '$eventStateCode', '$eventStateName', '$eventCategory', '$actionID',
                              '$actionCode', '$actionName', '$actionStateCode', '$actionStateName', '$suspensionStartDate',
                              '$suspensionEndDate', '$expulsion', '$lawReferred',
                              '$siteID', $currentSchoolYearID)";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
