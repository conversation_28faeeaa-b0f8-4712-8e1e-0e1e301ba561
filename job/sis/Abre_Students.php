<?php
/*
* Copyright (C) 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Students';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "Abre_Students";

    //Define
    define("SAFE_COLUMN_COUNT", 20);
    define("MAX_IMPORT_LIMIT", 25);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
    $separator = "\r\n";

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

  	$rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO Abre_Students (
      StudentId, FirstName, MiddleName, LastName, Gender, EthnicityCode,
      EthnicityDescription, DateOfBirth, Email, SSID, IEP, Gifted,
      EconomicallyDisadvantaged, Title1, Title3, ELL, SchoolCode,
      SchoolName, CurrentGrade, Status, Username,
      Password, Status504, siteID, school_year_id
     ) VALUES ";

  	$line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM Abre_Students WHERE siteID = $siteID AND school_year_id = $currentSchoolYearID");
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$studentID = trim($db->escape_string($data[0]));
  			$firstName = trim($db->escape_string($data[1]));
  			$middleName = trim($db->escape_string($data[2]));
  			$lastName = trim($db->escape_string($data[3]));
  			$gender = trim($db->escape_string($data[4]));
  			$ethnicityCode = trim($db->escape_string($data[5]));
  			$ethnicityDescription = trim($db->escape_string($data[6]));
  			$dateOfBirth = trim($db->escape_string($data[7]));
  			$email = trim($db->escape_string($data[8]));
  			$ssid = trim($db->escape_string($data[9]));
  			$iep = trim($db->escape_string($data[10]));
  			$gifted = trim($db->escape_string($data[11]));
  			$economicallyDisadvantaged = trim($db->escape_string($data[12]));
  			$title1 = trim($db->escape_string($data[13]));
  			$title3 = trim($db->escape_string($data[14]));
  			$ell = trim($db->escape_string($data[15]));
  			$schoolCode = trim($db->escape_string($data[16]));
  			$schoolName = trim($db->escape_string($data[17]));
  			$currentGrade = trim($db->escape_string($data[18]));
  			$status = trim($db->escape_string($data[19]));
  			$username = isset($data[20]) ? $db->escape_string(trim($data[20])) : "";
  			$password = isset($data[21]) ?
                      $db->escape_string(encrypt(trim($data[21]), $config->dbKey->iv, $config->dbKey->key))
                      : $db->escape_string(encrypt("", $config->dbKey->iv, $config->dbKey->key));
        $status504 = isset($data[22]) ? trim($db->escape_string($data[22])) : "";

        $valuesToImport []= "(
          '$studentID', '$firstName', '$middleName', '$lastName',
          '$gender', '$ethnicityCode', '$ethnicityDescription',
          '$dateOfBirth', '$email', '$ssid', '$iep', '$gifted',
          '$economicallyDisadvantaged', '$title1', '$title3',
          '$ell', '$schoolCode', '$schoolName', '$currentGrade',
          '$status', '$username', '$password', '$status504', '$siteID',
          $currentSchoolYearID
        )";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
  	}while($line !== false);

  	if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }

    $buildingSql = "INSERT INTO abre_schools (code, name, site_id, school_year_id)
                      SELECT DISTINCT SchoolCode, SchoolName, siteID, school_year_id
                      FROM Abre_Students students
                      WHERE SchoolCode != '' AND siteID = ? AND school_year_id = ?
                    ON DUPLICATE KEY UPDATE
                      abre_schools.code = students.SchoolCode,
                      abre_schools.name = students.SchoolName,
                      abre_schools.site_id = students.siteID";
    $buildingStmt = $db->stmt_init();
    $buildingStmt->prepare($buildingSql);
    $buildingStmt->bind_param("ii", $siteID, $currentSchoolYearID);
    $buildingStmt->execute();
    $buildingStmt->close();

  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>
