<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

//Required configuration files
require_once(dirname(__FILE__). '/../vendor/autoload.php');
require_once(dirname(__FILE__) . '/utils/functions.php');
require_once(dirname(__FILE__) . '/utils/logging.php');
require(dirname(__FILE__) . '/utils/VendorLink/vendorLinkApi.php');

function runJob($db, $siteID, $config){

    $cronNameRunner = 'Abre VendorLink Runner';
    $uuidRunner = Logger::logCronStart($db, $siteID, $cronNameRunner);

  ignore_user_abort(true);
  set_time_limit(0);

  $error = null;

  try{

    if ($config->vendorlink->clientID == ""
        || $config->vendorlink->clientSecret == ""
        || $config->vendorlink->baseUri == ""
        || $config->vendorlink->subscriptionKey == ""){
      throw new Exception("VendorLink credentials missing.");
    }

    $currentSchoolYearID = getCurrentSchoolYearID($db);

    $vendorLinkApi = VendorLinkAPI::init($config->vendorlink->clientID,
                                         $config->vendorlink->clientSecret,
                                         $config->vendorlink->baseUri,
                                         $config->vendorlink->subscriptionKey);

    require('vendorlink/Abre_VendorLink_SIS_District.php');
    require('vendorlink/Abre_VendorLink_SIS_Schools.php');
    require('vendorlink/Abre_VendorLink_SIS_Staff.php');
    require('vendorlink/Abre_VendorLink_SIS_Students.php');
    require('vendorlink/Abre_VendorLink_SIS_DisciplineCodes.php');
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }


  
  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronNameRunner, $status, $details, $uuidRunner);
}

?>
