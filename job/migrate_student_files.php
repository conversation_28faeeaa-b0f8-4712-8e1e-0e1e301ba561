<?php

/*
* Copyright Abre.io Inc.
*/

// Set unlimited execution time
set_time_limit(0);
ini_set('max_execution_time', 0);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/utils/functions.php';
require_once __DIR__ . '/utils/logging.php';

use phpseclib\Net\SFTP;
use phpseclib\Net\SFTP\Stream;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config)
{
    $cronName = 'Student Files Migration';
    $sftp = null;
    $lockFile = null;
    $uuid = null;
    $status = CRON_FAILURE;
    $details = [];
    $skip = null;

    try 
    {
        // Validate required parameters
        if (empty($siteID) || !ctype_digit((string) $siteID)) 
        {
            throw new Exception('Site ID is required and must be an integer.');
        }

        if (empty($config) || empty($config->sftp) || empty($config->sftp->ip) || 
            empty($config->sftp->userName) || empty($config->sftp->password)) 
        {
            throw new Exception('Invalid SFTP configuration');
        }

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        // Set up lock file
        $lockFile = sys_get_temp_dir() . '/student_files_migration_' . $siteID . '.lock';
        if (file_exists($lockFile)) 
        {
            $pid = file_get_contents($lockFile);
            if (posix_kill($pid, 0)) 
            {
                throw new Exception('Migration already in progress for this site (PID: ' . $pid . ')');
            }
            // If process doesn't exist, remove stale lock
            unlink($lockFile);
        }
        file_put_contents($lockFile, getmypid());

        // Connect to SFTP with timeout and retry logic
        $sftp = new SFTP($config->sftp->ip);
        $sftp->setTimeout(7200);
        
        $maxRetries = 3;
        $retryCount = 0;
        $connected = false;

        while (!$connected && $retryCount < $maxRetries) 
        {
            try 
            {
                if ($sftp->login($config->sftp->userName, $config->sftp->password)) 
                {
                    $connected = true;
                }
            } 
            catch (Exception $e) 
            {
                $retryCount++;
                if ($retryCount >= $maxRetries) 
                {
                    throw new Exception('SFTP Login Failed after ' . $maxRetries . ' attempts: ' . $e->getMessage());
                }
                sleep(2);
            }
        }

        // Disable PHP output buffering
        if (ob_get_level()) 
        {
            ob_end_clean();
        }

        // Check if Student_Files.zip exists
        $remoteFile = 'Student_Files.zip';
        if (!$sftp->file_exists($remoteFile)) 
        {
            $skip = true;
            throw new Exception('No File Found');
        }

        // Check file size
        $fileSize = $sftp->size($remoteFile);
        if ($fileSize === false) 
        {
            throw new Exception('Failed to get file size from SFTP server');
        }

        // 50GB limit
        if ($fileSize > 53687091200)
        {
            throw new Exception(sprintf(
                'File size (%s) exceeds maximum allowed size of 50GB',
                formatBytes($fileSize)
            ));
        }

        // Verify file is actually a zip file by checking its magic number
        $zipHeader = $sftp->get($remoteFile, false, 0, 4);
        if ($zipHeader === false || substr($zipHeader, 0, 2) !== 'PK') 
        {
            throw new Exception('File is not a valid ZIP archive.');
        }

        // LZ Configuration
        $projectId = 'abre-production';
        $bucketName = 'student-attachments-sftp-unzip';
        $gcsUploadTimeout = isset($config->gcs->uploadTimeout) ? $config->gcs->uploadTimeout : 3600;
        $storage = new StorageClient([
            'projectId' => $projectId
        ]);
        
        // Upload to GCS
        $bucket = $storage->bucket($bucketName);

        // Verify bucket exists
        if (!$bucket->exists()) 
        {
            throw new Exception('GCS bucket does not exist');
        }

        // Create a stream from SFTP to avoid loading entire file into memory
        Stream::register();
        
        // Verify SFTP connection is still active
        if (!$sftp->isConnected()) 
        {
            error_log("SFTP connection lost before stream creation for site $siteID");
            throw new Exception('SFTP connection lost before stream creation');
        }
        
        // Debug log the SFTP state
        error_log("SFTP connection state before stream creation for site $siteID: " . ($sftp->isConnected() ? 'Connected' : 'Disconnected'));
        error_log("Attempting to create stream for file: $remoteFile (Size: " . formatBytes($fileSize) . ")");
        
        // Try streaming approach first
        $sftpStream = false;
        try 
        {
            // Create a stream wrapper for SFTP
            $context = stream_context_create([
                'sftp' => [
                    'session' => $sftp,
                    'path' => $remoteFile
                ]
            ]);
            
            // Open the SFTP stream with proper error handling
            $sftpStream = @fopen('sftp://session', 'r', false, $context);
            if ($sftpStream === false) 
            {
                $error = error_get_last();
                $errorMsg = $error ? $error['message'] : 'Unknown error';
                error_log("SFTP stream creation failed for site $siteID: $errorMsg");
                throw new Exception('Failed to open SFTP stream: ' . $errorMsg);
            }
            
            error_log("SFTP stream created successfully for site $siteID");
        } 
        catch (Exception $e) 
        {
            error_log("Streaming approach failed for site $siteID, falling back to chunked reading: " . $e->getMessage());
            
            // Fallback: Create a temporary stream for chunked reading
            $sftpStream = fopen('php://temp', 'w+b');
            if ($sftpStream === false) 
            {
                error_log("Failed to create temporary stream for fallback for site $siteID");
                throw new Exception('Failed to create temporary stream for fallback');
            }
            
            // Read file in chunks and write to stream
            $chunkSize = 8192; // 8KB chunks
            $offset = 0;
            $totalChunks = ceil($fileSize / $chunkSize);
            $currentChunk = 0;
            
            error_log("Starting chunked reading for site $siteID (Total chunks: $totalChunks)");
            
            while ($chunk = $sftp->get($remoteFile, false, $offset, $chunkSize)) 
            {
                $currentChunk++;
                if (fwrite($sftpStream, $chunk) === false) 
                {
                    error_log("Failed to write chunk $currentChunk/$totalChunks for site $siteID");
                    fclose($sftpStream);
                    throw new Exception('Failed to write chunk to stream');
                }
                $offset += strlen($chunk);
                
                // Log progress every 10,000 chunks
                if ($currentChunk % 10000 === 0) 
                {
                    error_log("Processed $currentChunk/$totalChunks chunks for site $siteID");
                }
            }
            
            // Reset stream position for reading after all chunks are written
            if (ftell($sftpStream) !== 0) 
            {
                rewind($sftpStream);
            }
            
            error_log("Completed chunked reading for site $siteID");
            
            // Verify stream size matches original file size
            $streamSize = fstat($sftpStream)['size'];
            if ($streamSize !== $fileSize) 
            {
                throw new Exception(sprintf(
                    'Stream size (%d) does not match original file size (%d)',
                    $streamSize,
                    $fileSize
                ));
            }

            // Upload the zip file directly to GCS using the stream
            $gcsZipPath = "student_files/$siteID/Student_Files.zip";
            error_log("Starting GCS upload for site $siteID to path: $gcsZipPath");
            
            $bucket->upload(
                $sftpStream,
                [
                    'name'     => $gcsZipPath,
                    'metadata' => [
                        'siteId'       => $siteID,
                        'contentType'  => 'application/zip',
                        'originalSize' => $fileSize,
                        'uploadDate'   => date('Y-m-d H:i:s')
                    ],
                    'restOptions' => [
                        'timeout' => $gcsUploadTimeout,
                        'connectTimeout' => 300,
                        'readTimeout' => 7200
                    ]
                ]
            );
            
            error_log("GCS upload completed successfully for site $siteID");
        } 
        finally 
        {
            if ($sftpStream) 
            {
                fclose($sftpStream);
            }
        }

        // Delete the zip file from SFTP after successful upload
        if (!$sftp->file_exists($remoteFile)) 
        {
            error_log("Warning: $remoteFile does not exist on SFTP server");
        } 
        else 
        {
            if (!$sftp->delete($remoteFile)) 
            {
                $error = $sftp->getLastSFTPError();
                error_log("Error deleting $remoteFile from SFTP server: " . $error);
                throw new Exception("Failed to delete $remoteFile from SFTP server: " . $error);
            }
            error_log("Successfully deleted $remoteFile from SFTP server");
        }

        $status = CRON_SUCCESS;
        $details = "Successfully migrated student files to GCS";
    } 
    catch (Exception $ex) 
    {
        $error = $ex->getMessage();
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
        $details = isset($skip) && $skip ? $error : "Could not migrate student files";
    }

    if ($sftp) 
    {
        $sftp->disconnect();
    }

    if ($lockFile && file_exists($lockFile)) 
    {
        unlink($lockFile);
    }

    // Log the cron job finish
    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

// Byte conversion function for logging
function formatBytes($bytes, $precision = 2) 
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, $precision) . ' ' . $units[$pow];
}
