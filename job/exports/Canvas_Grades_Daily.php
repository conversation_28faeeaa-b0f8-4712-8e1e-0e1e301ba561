<?php

/*
* Copyright (C) Abre.io Inc.
*/

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use phpseclib\Net\SFTP;

function insertBatch($insertStmt, $values)
{
    foreach ($values as $value) {
        $insertStmt->bind_param(
            'sssssiii',
            $value['sisId'],
            $value['courseName'],
            $value['courseInstructors'],
            $value['courseGrade'],
            $value['gradeDate'],
            $value['gradingPeriodNumber'],
            $value['siteId'],
            $value['currentSchoolYearId']
        );
        $insertStmt->execute();
    }
}

function runJob($db, $siteId, $config)
{
    $cronName = 'Canvas Grades Daily';

    try {
        $uuid = Logger::logCronStart($db, $siteId, $cronName);

        // Define
        // SAFE_COLUMNS: sis_id, course_name, course_instructors, course_grade, grade_date
        define('SAFE_COLUMN_COUNT', 5);
        define('MAX_IMPORT_LIMIT', 25);
        define('CANVAS_GRADES_PREFIX', 'canvas_grades_daily.csv');
        $currentSchoolYearId = getCurrentSchoolYearId($db);
        $error = null;
        $skip = null;
        $separator = "\r\n";

        // Connect
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception('Login to SFTP failed.');
        }

        // Change to Canvas Daily Grades directory
        if (!$sftp->chdir('Canvas Daily Grades')) {
            throw new Exception('Could not change to Canvas Daily Grades directory.');
        }

        // List files in directory
        $files = $sftp->nlist();
        if (!$files) {
            throw new Exception('Could not list files in Canvas Daily Grades directory.');
        }

        // Find the most recent canvas_grades_daily.csv* file
        $latestFile = null;
        $latestTime = 0;
        foreach ($files as $file) {
            if (strncmp($file, CANVAS_GRADES_PREFIX, strlen(CANVAS_GRADES_PREFIX)) === 0) {
                $fileTime = $sftp->stat($file)['mtime'];
                if ($fileTime > $latestTime) {
                    $latestTime = $fileTime;
                    $latestFile = $file;
                }
            }
        }

        if (!$latestFile) {
            $skip = true;
            throw new Exception('No canvas_grades_daily.csv file found in Canvas Daily Grades directory.');
        }

        // Get the file content
        $cronFile = $sftp->get($latestFile);
        if (!$cronFile) {
            throw new Exception('Could not retrieve file: ' . $latestFile);
        }

        $columnSeparator = ',';

        $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
        if ($fileDetails['isEmpty']) {
            $skip = true;
            throw new Exception('File is empty.');
        } elseif ($fileDetails['hasHeaderRow'] && !$fileDetails['hasDataRow']) {
            $skip = true;
            throw new Exception('File only contains a header row.');
        } elseif (!$fileDetails['hasValidDataRow']) {
            throw new Exception('No valid data row found.');
        }

        $rowCounter = 0;
        $valuesToImport = [];
        $insertStmt = $db->prepare(
            "INSERT INTO `vendor_canvas_grades_daily` (
                `sis_id`, `course_name`, `course_instructors`, `course_grade`,
                `grade_date`, `grading_period_number`, `site_id`, `school_year_id`
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );

        $line = strtok($cronFile, $separator);
        $line = strtok($separator); // skip header row

        // After getting the file but before processing it
        $today = date('Y-m-d');

        // Find current active grading period if any
        $gradingPeriodQuery = "SELECT `grading_period_number`, `start_date`, `end_date` 
                              FROM `district_grading_period_definitions` 
                              WHERE `site_id` = ? 
                              AND `school_year_id` = ? 
                              AND ? BETWEEN `start_date` AND `end_date`";

        $gradingPeriodStmt = $db->prepare($gradingPeriodQuery);
        $gradingPeriodStmt->bind_param('iis', $siteId, $currentSchoolYearId, $today);
        $gradingPeriodStmt->execute();
        $gradingPeriodResult = $gradingPeriodStmt->get_result();

        if ($gradingPeriodResult->num_rows > 0) {
            // We're in an active grading period - clear existing records for this period
            $gradingPeriodData = $gradingPeriodResult->fetch_assoc();        
            // Start transaction
            $db->begin_transaction();
            try {
                // Delete existing records for this grading period
                $deleteStmt = $db->prepare(
                    "DELETE FROM `vendor_canvas_grades_daily` 
                    WHERE `site_id` = ? 
                    AND `school_year_id` = ? 
                    AND `grade_date` BETWEEN ? AND ?"
                );
                $deleteStmt->bind_param(
                    'iiss', 
                    $siteId, 
                    $currentSchoolYearId, 
                    $gradingPeriodData['start_date'], 
                    $gradingPeriodData['end_date']
                );
                $deleteStmt->execute();
            } catch (Exception $e) {
                $db->rollback();
                throw new Exception('Failed to delete existing records: ' . $e->getMessage());
            }
        } else {
            // We're between grading periods - find the next grading period
            $nextGradingPeriodQuery = "SELECT `grading_period_number`, `start_date`, `end_date` 
                                      FROM `district_grading_period_definitions` 
                                      WHERE `site_id` = ? 
                                      AND `school_year_id` = ? 
                                      AND `start_date` > ? 
                                      ORDER BY `start_date` ASC LIMIT 1";
            
            $nextGradingPeriodStmt = $db->prepare($nextGradingPeriodQuery);
            $nextGradingPeriodStmt->bind_param('iis', $siteId, $currentSchoolYearId, $today);
            $nextGradingPeriodStmt->execute();
            $nextGradingPeriodResult = $nextGradingPeriodStmt->get_result();
            
            if ($nextGradingPeriodResult->num_rows > 0) {
                $gradingPeriodData = $nextGradingPeriodResult->fetch_assoc();
                // Start transaction for insert operations
                $db->begin_transaction();
            } else {
                throw new Exception('No active or upcoming grading period found');
            }
        }

        do {
            // Split Columns
            $data = str_getcsv($line, $columnSeparator);

            if (count($data) >= SAFE_COLUMN_COUNT) {
                $rowCounter++;

                $sisId = trim($db->escape_string($data[0]));
                $courseName = trim($db->escape_string($data[1]));
                $courseInstructors = trim($db->escape_string($data[2]));
                $courseGrade = trim($db->escape_string($data[3]));
                $gradeDate = trim($db->escape_string($data[4]));

                // Try to parse the date with explicit format
                $date = DateTime::createFromFormat('m-d-Y H:i:s', $gradeDate);
                if ($date === false) {
                    throw new Exception('Invalid grade date format: ' . $gradeDate . '. Expected format: MM-DD-YYYY HH:mm:ss');
                }
                
                // Format to YYYY-MM-DD HH:mm:ss for database
                $gradeDate = $date->format('Y-m-d H:i:s');

                // Check if grade date is within the grading period (using just the date part)
                $dateOnly = $date->format('Y-m-d');
                if ($dateOnly < $gradingPeriodData['start_date'] || 
                    $dateOnly > $gradingPeriodData['end_date']) {
                    throw new Exception(
                        'Grade date ' . $dateOnly . 
                        ' is outside the grading period range (' . 
                        $gradingPeriodData['start_date'] . ' to ' . 
                        $gradingPeriodData['end_date'] . ')'
                    );
                }

                $valuesToImport[] = [
                    'sisId'               => $sisId,
                    'courseName'          => $courseName,
                    'courseInstructors'   => $courseInstructors,
                    'courseGrade'         => $courseGrade,
                    'gradeDate'           => $gradeDate,
                    'gradingPeriodNumber' => $gradingPeriodData['grading_period_number'],
                    'siteId'              => $siteId,
                    'currentSchoolYearId' => $currentSchoolYearId
                ];

                if (count($valuesToImport) >= MAX_IMPORT_LIMIT) {
                    try {
                        insertBatch($insertStmt, $valuesToImport);
                        $valuesToImport = [];
                    } catch (Exception $e) {
                        $db->rollback();
                        throw new Exception('Failed to insert batch: ' . $e->getMessage());
                    }
                }
            }

            $line = strtok($separator);
        } while ($line !== false);

        if (count($valuesToImport)) {
            try {
                insertBatch($insertStmt, $valuesToImport);
            } catch (Exception $e) {
                $db->rollback();
                throw new Exception('Failed to insert final batch: ' . $e->getMessage());
            }
        }

        // Commit transaction if we got here
        $db->commit();

    } catch(Exception $ex) {
        // Rollback to ensure any open transaction is rolled back from main catch
        $db->rollback();
        $error = $ex->getMessage();
    }

    if (isset($error) && !is_null($error)) {
        $details['error'] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $details = [
            'rowsInserted' => $rowCounter
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteId, $cronName, $status, $details, $uuid);
}
