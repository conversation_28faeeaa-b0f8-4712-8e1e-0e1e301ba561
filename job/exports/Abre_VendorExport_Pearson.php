<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try {

  	$semesterDates = getSemesterDates($db, $siteID);

  	$sftp = new SFTP('sftp.pifdata.net');
  	if (!$sftp->login($config->pearson->userName, $config->pearson->password)) {
  		throw new Exception("Login to Pearson SFTP Server failed");
  	}

  	//Manifest CSV
  	$csv = '"propertyName","value"'."\n";
  	$csv.= '"manifest.version","1.0"'."\n";
  	$csv.= '"oneroster.version","1.1"'."\n";
  	$csv.= '"file.academicSessions","bulk"'."\n";
  	$csv.= '"file.categories","absent"'."\n";
  	$csv.= '"file.classes","bulk"'."\n";
  	$csv.= '"file.classResources","absent"'."\n";
  	$csv.= '"file.courses","bulk"'."\n";
  	$csv.= '"file.courseResources","absent"'."\n";
  	$csv.= '"file.demographics","absent"'."\n";
  	$csv.= '"file.enrollments","bulk"'."\n";
  	$csv.= '"file.lineItems","absent"'."\n";
  	$csv.= '"file.orgs","bulk"'."\n";
  	$csv.= '"file.resources","absent"'."\n";
  	$csv.= '"file.results","absent"'."\n";
  	$csv.= '"file.users","bulk"'."\n";
  	$csv.= '"file.lineItems","absent"'."\n";

  	$manifest = $csv;

  	//academicSessions CSV
  	$csv = '"sourcedId","status","dateLastModified","title","type","startDate","endDate","parentSourcedId","schoolYear"'."\n";
  	$rows = mysqli_query($db, "SELECT * FROM Abre_Courses WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$SchoolCode = $row["SchoolCode"];
  		$CourseCode = $row["CourseCode"];
  		$SectionCode = $row["SectionCode"];
  		$StaffID = $row["StaffID"];
  		$TermCode = $row["TermCode"];
  		$Period = $row["Period"];
  		$AcademicSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$Title = $SchoolCode."-".$StaffID."-".$Period;
  		$SchoolYear="2020";

  		$termInformation = getTermInformation($TermCode, $semesterDates, $SchoolYear);
  		$Type = $termInformation["type"];
  		$StartDate = $termInformation["startDate"];
  		$EndDate = $termInformation["endDate"];

  		$blank = "";

  		$csv.= '"'.$AcademicSourcedId.'","'.$blank.'","'.$blank.'","'.$Title.'","'.$Type.'","'.$StartDate.'","'.$EndDate.'","'.$blank.'","'.$SchoolYear.'"'."\n";
  	}

  	$academicSessions = $csv;

  	//classes CSV
  	$csv = '"sourcedId","status","dateLastModified","title","grades","courseSourcedId","classCode","classType","location","schoolSourcedId","termSourcedIds","subjects","subjectCodes","periods"'."\n";
  	$rows = mysqli_query($db, "SELECT DISTINCT ID, SchoolCode, CourseCode, SectionCode, StaffID, TermCode, Period FROM `Abre_Courses` WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows))
  	{

  		$SchoolCode = $row["SchoolCode"];
  		$CourseCode = $row["CourseCode"];
  		$SectionCode = $row["SectionCode"];
  		$StaffID = $row["StaffID"];
  		$TermCode = $row["TermCode"];
  		$Period = $row["Period"];
  		$ClassesSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$Title = $SchoolCode."-".$StaffID."-".$Period;
  		$CoursesSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$orgSourceId=$SchoolCode.$CourseCode.$SectionCode.$StaffID.$Period;
  		$ClassType="scheduled";
  		$SchoolSourceId=$SchoolCode;
  		$termSourceIds=$SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$blank = "";

  		$csv.= '"'.$ClassesSourcedId.'","'.$blank.'","'.$blank.'","'.$Title.'","'.$blank.'","'.$CoursesSourcedId.'","'.$blank.'","'.$ClassType.'","'.$blank.'","'.$SchoolSourceId.'","'.$termSourceIds.'","'.$blank.'","'.$blank.'","'.$blank.'"'."\n";
  	}

  	$classes = $csv;

  	//courses CSV
  	$csv = '"sourcedId","status","dateLastModified","schoolYearSourcedId","title","courseCode","grades","orgSourcedId","subjects","subjectCodes"'."\n";
  	$rows = mysqli_query($db, "SELECT DISTINCT ID, SchoolCode, CourseCode, SectionCode, StaffID, TermCode, Period
  	FROM `Abre_Courses` WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$SchoolCode = $row["SchoolCode"];
  		$CourseCode = $row["CourseCode"];
  		$SectionCode = $row["SectionCode"];
  		$StaffID = $row["StaffID"];
  		$TermCode = $row["TermCode"];
  		$Period = $row["Period"];
  		$OrgsSourceId=$SchoolCode;
  		$CoursesSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$AcademicSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$Title = $SchoolCode."-".$StaffID."-".$Period;
  		$orgSourceId=$SchoolCode.$CourseCode.$SectionCode.$StaffID.$Period;
  		$blank = "";

  		$csv.= '"'.$CoursesSourcedId.'","'.$blank.'","'.$blank.'","'.$AcademicSourcedId.'","'.$Title.'","'.$blank.'","'.$blank.'","'.$OrgsSourceId.'","'.$blank.'","'.$blank.'"'."\n";
  	}

  	$courses = $csv;

  	//enrollments CSV
  	$fileenrollments = tempnam(sys_get_temp_dir(), 'enroll');
  	$fp = fopen($fileenrollments, "w");

  	$file_csv = '"sourcedId","status","dateLastModified","classSourcedId","schoolSourcedId","userSourcedId","role","primary","beginDate","endDate"'."\n";
  	fwrite($fp, $file_csv);
  	$rows = $db->query("SELECT * FROM Abre_StudentSchedules WHERE siteID = '$siteID'", MYSQLI_USE_RESULT);
  	while ($row = $rows->fetch_assoc())
  	{
  		$StaffID = $row["StaffId"];
  		$StudentID = $row["StudentID"];
  		$SchoolCode = $row["SchoolCode"];
  		$CourseCode = $row["CourseCode"];
  		$SectionCode = $row["SectionCode"];
  		$Period = $row["Period"];
  		$TermCode = $row["TermCode"];
  		$EnrollmentsSourceId=$SchoolCode.$StaffID.$CourseCode.$SectionCode.$Period.$StudentID.$TermCode."s";
  		$ClassesSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$SchoolSourceId=$SchoolCode;
  		$UsersSourcedId = $StudentID;
  		$role="student";

  		$termInformation = getTermInformation($TermCode, $semesterDates, $SchoolYear);
  		$StartDate = $termInformation["startDate"];
  		$EndDate = $termInformation["endDate"];

  		$blank = "";

  		$file_csv = '"'.$EnrollmentsSourceId.'","'.$blank.'","'.$blank.'","'.$ClassesSourcedId.'","'.$SchoolSourceId.'","'.$UsersSourcedId.'","'.$role.'","true","'.$StartDate.'","'.$EndDate.'"'."\n";
  		fwrite($fp, $file_csv);
  	}

  	$rows = $db->query("SELECT * FROM Abre_StaffSchedules WHERE siteID = '$siteID'", MYSQLI_USE_RESULT);
  	while ($row = $rows->fetch_assoc())
  	{
  		$StaffID = $row["StaffID"];
  		$SchoolCode = $row["SchoolCode"];
  		$CourseCode = $row["CourseCode"];
  		$SectionCode = $row["SectionCode"];
  		$Period = $row["Period"];
  		$TermCode = $row["TermCode"];
  		$EnrollmentsSourceId=$SchoolCode.$StaffID.$CourseCode.$SectionCode.$Period.$StudentID.$TermCode."t";
  		$ClassesSourcedId = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$TermCode.$Period;
  		$SchoolSourceId=$SchoolCode;
  		$UsersSourcedId = $StaffID;
  		$role="teacher";

  		$termInformation = getTermInformation($TermCode, $semesterDates, $SchoolYear);
  		$StartDate = $termInformation["startDate"];
  		$EndDate = $termInformation["endDate"];

  		$blank = "";

  		$file_csv = '"'.$EnrollmentsSourceId.'","'.$blank.'","'.$blank.'","'.$ClassesSourcedId.'","'.$SchoolSourceId.'","'.$UsersSourcedId.'","'.$role.'","true","'.$StartDate.'","'.$EndDate.'"'."\n";
  		fwrite($fp, $file_csv);
  	}

  	fclose($fp);

  	//orgs CSV
  	$csv = '"sourcedId","status","dateLastModified","name","type","identifier","parentSourcedId","metadata.address1","metadata.address2","metadata.city","metadata.state","metadata.postCode"'."\n";
  	$rows = mysqli_query($db, "SELECT * FROM Abre_VendorLink_SIS_Schools WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$RefId = $row["RefId"];
  		$LocalId = $row["LocalId"];
  		$name = $row["SchoolName"];
  		$type = "school";
  		$metadataaddress1 = $row["Street"];
  		$metadatacity = $row["City"];
  		$metadatastate = $row["State"];
  		$metadatapostcode = $row["PostalCode"];
  		$OrgsSourceId=$LocalId;
  		$blank = "";

  		$csv.= '"'.$OrgsSourceId.'","'.$blank.'","'.$blank.'","'.$name.'","'.$type.'","'.$blank.'","'.$blank.'","'.$metadataaddress1.'","'.$blank.'","'.$metadatacity.'","'.$metadatastate.'","'.$metadatapostcode.'"'."\n";
  	}
  	$csv.= '"HA","'.$blank.'","'.$blank.'","District Office","district","'.$blank.'","'.$blank.'","533 Dayton Street","'.$blank.'","Hamilton","OH","45012"'."\n";

  	$orgs = $csv;

  	//users CSV
  	$csv = '"sourcedId","status","dateLastModified","enabledUser","orgSourcedIds","role","username","userIds","givenName","familyName","middleName","identifier","email","sms","phone","agentSourcedIds","grades","password"'."\n";
  	$rows = mysqli_query($db, "SELECT * FROM Abre_Staff where (siteID = '$siteID' AND Email1!='') group by StaffID");
  	while ($row = mysqli_fetch_assoc($rows))
  	{

  		$blank = "";
  		$enabledUser = "true";
  		$UsersSourcedIds = $row["StaffID"];
  		$role = "teacher";
  		$username = $row["EMail1"];
  		$givenName = $row["FirstName"];
  		$familyName = $row["LastName"];
  		$email = $row["EMail1"];
  		$SchoolCode = $row["SchoolCode"];
  		$OrgsSourceId=$SchoolCode;

  		$csv.= '"'.$UsersSourcedIds.'","'.$blank.'","'.$blank.'","'.$enabledUser.'","'.$OrgsSourceId.'","'.$role.'","'.$username.'","{Fed:'.$username.'}","'.$givenName.'","'.$familyName.'","'.$blank.'","'.$blank.'","'.$email.'","'.$blank.'","'.$blank.'","'.$blank.'","'.$blank.'","'.$blank.'"'."\n";
  	}

  	$rows = mysqli_query($db, "SELECT * FROM Abre_Students WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows))
  	{

  		$blank = "";
  		$enabledUser = "true";
  		$UsersSourcedIds = $row["StudentId"];
  		$role = "student";
  		$username = "";
  		$givenName = $row["FirstName"];
  		$familyName = $row["LastName"];
  		$email = "";
  		$SchoolCode = $row["SchoolCode"];
  		$OrgsSourceId=$SchoolCode;

      //Grade (Supporting KG, 01-12, Other)
      $grade = $row["CurrentGrade"];
      switch($grade){
        case ("KG"):
            $grade = "KG";
            break;
        default:
          $grade = ltrim($grade, '0');
          if(($grade <= 12) && ($grade != 0)){
            if($grade < 10){
              $grade = "0".$grade;
            }
          }else{
            $grade = "Other";
          }
      }

  		//Find GAFE Credientials given StudentID
  		$StudentEmail = "";
  		$gafequery = mysqli_query($db, "SELECT * FROM Abre_AD where StudentID='$UsersSourcedIds'AND siteID = '$siteID'");
  		while($gaferow = mysqli_fetch_assoc($gafequery)){
  			$StudentEmail = $gaferow["Email"];
  		}

  		$csv.= '"'.$UsersSourcedIds.'","'.$blank.'","'.$blank.'","'.$enabledUser.'","'.$OrgsSourceId.'","'.$role.'","'.$StudentEmail.'","{Fed:'.$StudentEmail.'}","'.$givenName.'","'.$familyName.'","'.$blank.'","'.$blank.'","'.$StudentEmail.'","'.$blank.'","'.$blank.'","'.$blank.'","'.$grade.'","'.$blank.'"'."\n";
  	}

  	$users = $csv;

  	$zip = new ZipArchive();

  	$filecron = tempnam(sys_get_temp_dir(), 'pearson');

  	if ($zip->open($filecron, ZipArchive::CREATE) === TRUE)
  	{
  		$zip->addFromString('manifest.csv', $manifest);
  		$zip->addFromString('academicSessions.csv', $academicSessions);
  		$zip->addFromString('classes.csv', $classes);
  		$zip->addFromString('courses.csv', $courses);
  		$zip->addFile($fileenrollments, 'enrollments.csv');

  		$zip->addFromString('orgs.csv', $orgs);
  		$zip->addFromString('users.csv', $users);

  		$ret = $zip->close();
  	}

  	$remote_directory = '/SIS/';

  	$sftp->put($remote_directory.'Pearson.zip', $filecron, SFTP::SOURCE_LOCAL_FILE);

  	$zip = new ZipArchive();

  	$filecron = tempnam(sys_get_temp_dir(), 'cron');

  	if ($zip->open($filecron, ZipArchive::CREATE) === TRUE)
  	{
  		$zip->addFromString('manifest.csv', $manifest);
  		$zip->addFromString('academicSessions.csv', $academicSessions);
  		$zip->addFromString('classes.csv', $classes);
  		$zip->addFromString('courses.csv', $courses);
  		$zip->addFile($fileenrollments, 'enrollments.csv');
  		$zip->addFromString('orgs.csv', $orgs);
  		$zip->addFromString('users.csv', $users);

  		$ret = $zip->close();
  	}

  	// Put a copy on our SFTP server
  	$sftp = new SFTP($config->sftp->ip);
  	if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
  		throw new Exception("Login to our SFTP Server failed");
  	}

  	$remote_directory = '';

  	$sftp->put($remote_directory.'Pearson.zip', $filecron, SFTP::SOURCE_LOCAL_FILE);
  } catch (Exception $ex) {
  	$error = $ex->getMessage();
  }

  $cronName = 'Pearson Export';
  $details = [];
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
?>
