<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try {

  	$sftp = new SFTP('prod-sftp-1.aws.cainc.com');
  	if (!$sftp->login($config->iready->userName, $config->iready->password)) {
  		throw new Exception("Login to iReady SFTP Server failed");
  	}

  	$remote_directory = '/in/';

  	//Student CSV
  	$csv = '"ClientID","SchoolID","StudentID_OwnerID","StudentNumber","FirstName","LastName","Grade","UserName","Password","DOB","Ethnicity","Hispanic","Gender","EconomicallyDisadvantaged","EnglishLearner","SpecialEducation","Migrant","MathDevelopmentalLevel","EnglishDevelopmentalLevel","PartnerID","Action","RTI Level","Gifted/Talented","Reserved1","Reserved2","Reserved3","Reserved4","Reserved5","Reserved6","Reserved7","Reserved8"'."\n";

  	$rows = mysqli_query($db, "SELECT * FROM Abre_Students WHERE Status != 'I' AND siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows)){
  		$StudentId = mysqli_real_escape_string($db, $row["StudentId"]);

  		//Password Manipulations
  		$Password = $StudentId;
  		if(strlen($StudentId) < 8){ $Password = str_pad($StudentId, 8, "0", STR_PAD_LEFT); }

  		$FirstName = $row["FirstName"];
  		$LastName = $row["LastName"];
  		$CurrentGradeOrigional = mysqli_real_escape_string($db, $row["CurrentGrade"]);
  		$CurrentGrade = mysqli_real_escape_string($db, $row["CurrentGrade"]);

  		//Grade Manipulation
  		if($CurrentGrade == "KG"){ $CurrentGrade = "0"; }
  		if($CurrentGrade == "PS"){ $CurrentGrade = "0"; }
  		if($CurrentGrade == "13"){ $CurrentGrade = "12"; }
  		if($CurrentGrade == "23"){ $CurrentGrade = "12"; }
  		if(strlen($CurrentGrade) == 2){ $CurrentGrade = ltrim($CurrentGrade, '0'); }

  		//Find GAFE Credientials given StudentID
  		$StudentEmail = "";
  		$FoundGAFE = 0;
  		$gafequery = mysqli_query($db, "SELECT * FROM Abre_AD WHERE StudentID = $StudentId AND siteID = '$siteID'");
  		while ($gaferow = mysqli_fetch_assoc($gafequery)){
  			$StudentEmail = $gaferow["Email"];
  			$FoundGAFE = 1;
  		}
  		if($FoundGAFE == 0){
        $StudentEmail = $FirstName.$LastName.$StudentId."@hcsdoh.org";
        $StudentEmail = preg_replace('/[^A-Za-z0-9\-]/', '', $StudentEmail);
      }

  		$StudentEmail = strtolower($StudentEmail);

  		$DateOfBirth = mysqli_real_escape_string($db, $row["DateOfBirth"]);
  		$DateOfBirth = str_replace('-', '/', $DateOfBirth);
  		$DateOfBirth = date("Y-m-d", strtotime($DateOfBirth));
  		$SchoolCode = mysqli_real_escape_string($db, $row["SchoolCode"]);
  		$SSID = $StudentId;

  		//Gender
  		$gender = $row["Gender"];
  		switch($gender){
  	    case "M":
  	        $gender = "Male";
  	        break;
  			default:
  		      $gender = "Female";
  		      break;
  		}

  		//Hispanic
  		$ethnicity = $row["EthnicityDescription"];
  		switch($ethnicity){
  			case "Hispanic/Latino":
  					$hispanic = "true";
  					break;
  			default:
  		      $hispanic = "false";
  		      break;
  		}

  		//Ethnicity
  		switch($ethnicity){
  	    case "American Indian or Alaskan Native":
  	        $ethnicity = "1";
  	        break;
  	    case "Asian":
  	        $ethnicity = "2";
  	        break;
  			case "Black or African-American, Non-Hispanic":
  					$ethnicity = "3";
  					break;
  			case "Native Hawaiian or Other Pacific Islander":
  					$ethnicity = "4";
  					break;
  			case "White, Non-Hispanic":
  					$ethnicity = "5";
  					break;
  			default:
  		      $ethnicity = "6";
  		      break;
  		}

  		//English Learner
  		$ell = $row["ELL"];
  		switch($ell){
  	    case "Y":
  	        $ell = "true";
  	        break;
  			default:
  		      $ell = "false";
  		      break;
  		}

  		//Special Education
  		$iep = $row["IEP"];
  		switch($iep){
  	    case "Y":
  	        $iep = "true";
  	        break;
  			default:
  		      $iep = "false";
  		      break;
  		}

  		//Economically Disadvantaged
  		$economicallyDisadvantaged = $row["EconomicallyDisadvantaged"];
  		switch($economicallyDisadvantaged){
  	    case "Y":
  	        $economicallyDisadvantaged = "true";
  	        break;
  			default:
  		      $economicallyDisadvantaged = "false";
  		      break;
  		}

  		$csv.= '"oh-hamil66375","'.$SchoolCode.'","'.$SSID.'","'.$StudentId.'","'.$FirstName.'","'.$LastName.'","'.$CurrentGrade.'","'.$StudentEmail.'","'.$Password.'","'.$DateOfBirth.'","'.$ethnicity.'","'.$hispanic.'","'.$gender.'","'.$economicallyDisadvantaged.'","'.$ell.'","'.$iep.'","","","","","","","","","","","","","","",""'."\n";
  	}
  	$student = $csv;
  	$sftp->put($remote_directory.'Student.csv', $student);

  	//Staff CSV
  	$csv = '"ClientID","SchoolID","StaffMemberID_OwnerID","FirstName","LastName","Role","Email","UserName","Password","PartnerID","Action","Reserved1","Reserved2","Reserved3","Reserved4","Reserved5","Reserved6","Reserved7","Reserved8","Reserved9","Reserved10"'."\n";

  	$rows = mysqli_query($db, "SELECT staff.SchoolCode, staff.EMail1, staff.LastName, staff.FirstName, staff.StaffID, staffschedules.CourseCode FROM Abre_Staff AS staff LEFT JOIN Abre_StaffSchedules AS staffschedules ON (staff.StaffID = staffschedules.StaffID AND (staff.siteID = staffschedules.siteID OR staffschedules.siteID IS NULL)) WHERE staff.EMail1 != '' AND staff.siteID = '$siteID' GROUP BY staff.StaffID");
  	while ($row = mysqli_fetch_assoc($rows)){
  		$SchoolCode = $row["SchoolCode"];
  		$Email = $row["EMail1"];
  		$Email = strtolower($Email);
  		$EmailPrefix = substr($Email, 0, strpos($Email, "@"));
  		$LastName = $row["LastName"];
  		$FirstName = $row["FirstName"];
  		$StaffID = $row["StaffID"];
  		$Password = $EmailPrefix.$StaffID;
  		$CourseCode = $row["CourseCode"];

  		//Set Coordinator by Default
  		$Role = "Coordinator";

  		//Check if staff has a Schedule
  		if(!is_null($CourseCode))
  		{
  			$Role = "Teacher";
  		}

  		//Set District Admins
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
      if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }
  		if($Email == "<EMAIL>"){ $Role = "DistrictAdministrator"; }

  		if($Role == "DistrictAdministrator"){
  			$csv.= '"oh-hamil66375","","'.$StaffID.'","'.$FirstName.'","'.$LastName.'","'.$Role.'","'.$Email.'","'.$Email.'","'.$Password.'","","","","","","","","","","","",""'."\n";
  		}else{
  			$csv.= '"oh-hamil66375","'.$SchoolCode.'","'.$StaffID.'","'.$FirstName.'","'.$LastName.'","'.$Role.'","'.$Email.'","'.$Email.'","'.$Password.'","","","","","","","","","","","",""'."\n";
  		}
  	}
  	$staff = $csv;
  	$sftp->put($remote_directory.'Staff.csv', $staff);

  	//School CSV
  	$csv = '"ClientID","SchoolID","SchoolName","DistrictName","State","NCESID","PartnerID","Action","Reserved1","Reserved2","Reserved3","Reserved4","Reserved5","Reserved6","Reserved7","Reserved8","Reserved9","Reserved10"'."\n";

  	$rows = mysqli_query($db, "SELECT * FROM Abre_Students WHERE siteID = '$siteID' GROUP BY SchoolName");
  	while ($row = mysqli_fetch_assoc($rows)){
  		$SchoolCode = mysqli_real_escape_string($db, $row["SchoolCode"]);
  		$SchoolName = mysqli_real_escape_string($db, $row["SchoolName"]);
  		$csv.= '"oh-hamil66375","'.$SchoolCode.'","'.$SchoolName.'","Hamilton City School District","OH","","","","","","","","","","","","",""'."\n";
  	}

  	//Add in Board Office
  	$csv.= '"oh-hamil66375","HA","District Office","Hamilton City School District","OH","","","","","","","","","","","","",""'."\n";
  	$school = $csv;
  	$sftp->put($remote_directory.'School.csv', $school);


  	//Section CSV
  	$csv = '"ClientID","SchoolID","SectionID","Name","Grade","Term","Code","Location","PartnerId","Action","Course","Subject","Reserved1","Reserved2","Reserved3","Reserved4","Reserved5","Reserved6","Reserved7","Reserved8"'."\n";
  	$rows = mysqli_query($db, "SELECT * FROM Abre_Courses WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows)){
  		$SchoolCode = mysqli_real_escape_string($db, $row["SchoolCode"]);
  		$CourseCode = mysqli_real_escape_string($db, $row["CourseCode"]);
  		$SectionCode = mysqli_real_escape_string($db, $row["SectionCode"]);
  		$StaffID = mysqli_real_escape_string($db, $row["StaffID"]);
  		$Period = mysqli_real_escape_string($db, $row["Period"]);
  		$Subject = "";
  		$CourseName = "";
  		$SectionID = "";
  		$CourseName = "";
  		$SectionID = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$Period;

  		//Find Course Name
  		$rows2 = mysqli_query($db, "SELECT * FROM Abre_StaffSchedules WHERE SchoolCode = '$SchoolCode' AND CourseCode = '$CourseCode' AND SectionCode = '$SectionCode' AND siteID = '$siteID'");
  		while ($row2 = mysqli_fetch_assoc($rows2)){
  			$CourseName = mysqli_real_escape_string($db, $row2["CourseName"]);
  			//$SectionID = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$Period;

  			//Check for Mathematics
  			if ((strpos($CourseName, 'ALGEBRA') !== false) or (strpos($CourseName, 'GEOMETRY') !== false) or (strpos($CourseName, 'CALCULUS') !== false) or (strpos($CourseName, 'QUANTITATIVE REASONING') !== false) or (strpos($CourseName, 'MATH') !== false) or (strpos($CourseName, 'MATHEMATICS') !== false)){
  				$Subject = "Mathematics";
  			}

  			//Check for English
  			if ((strpos($CourseName, 'ENG') !== false) or (strpos($CourseName, 'MODERN NOVELS') !== false) or (strpos($CourseName, 'WRITING') !== false) or (strpos($CourseName, 'READING') !== false) or (strpos($CourseName, 'ENGLISH') !== false) or (strpos($CourseName, 'SPELLING') !== false)){
  				$Subject = "English";
  			}

  			//Check for Science
  			if ((strpos($CourseName, 'CHEMISTRY') !== false) or (strpos($CourseName, 'HUMAN ANAT') !== false) or (strpos($CourseName, 'PHYSICS') !== false) or (strpos($CourseName, 'SCIENCE') !== false) or (strpos($CourseName, 'BIOLOGY') !== false)){
  				$Subject = "Science";
  			}

  			//Check for Social Studies
  			if ((strpos($CourseName, 'HISTORY') !== false) or (strpos($CourseName, 'WOMENS STUDIES') !== false) or (strpos($CourseName, 'GOVERNMENT') !== false) or (strpos($CourseName, 'HIST') !== false) or (strpos($CourseName, 'SOCIAL STUDIES') !== false)){
  				$Subject = "Social Studies";
  			}
  		}

  		$csv.= '"oh-hamil66375","'.$SchoolCode.'","'.$SectionID.'","'.$CourseName.' - '.$SectionID.'","","","","","","","","'.$Subject.'","","","","","","","",""'."\n";
  	}
  	$section = $csv;
  	$sftp->put($remote_directory.'Section.csv', $section);

  	//StaffSection CSV
  	$csv = '"ClientID","StaffId","SectionId","Action","Reserved1","Reserved2","Reserved3","Reserved4","Reserved5","Reserved6","Reserved7","Reserved8","Reserved9","Reserved10"'."\n";
  	$rows = mysqli_query($db, "SELECT * FROM Abre_StaffSchedules WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows)){
  		$SchoolCode = mysqli_real_escape_string($db, $row["SchoolCode"]);
  		$CourseCode = mysqli_real_escape_string($db, $row["CourseCode"]);
  		$SectionCode = mysqli_real_escape_string($db, $row["SectionCode"]);
  		$StaffID = mysqli_real_escape_string($db, $row["StaffID"]);
  		$Period = mysqli_real_escape_string($db, $row["Period"]);
  		$SectionID = $SchoolCode.$CourseCode.$SectionCode.$StaffID.$Period;
  		$csv.= '"oh-hamil66375","'.$StaffID.'","'.$SectionID.'","","","","","","","","","","",""'."\n";
  	}
  	$staffsection = $csv;
  	$sftp->put($remote_directory.'StaffSection.csv', $staffsection);

  	//StudentSection CSV
  	$filestudent = tempnam(sys_get_temp_dir(), 'student');
  	$fp = fopen($filestudent, 'w');
  	$csv = '"ClientID","StudentId","SectionId","Action","Reserved1","Reserved2","Reserved3","Reserved4","Reserved5","Reserved6","Reserved7","Reserved8","Reserved9","Reserved10"'."\n";
  	fwrite($fp, $csv);

  	$recordCount = 0;
  	while(true) {
  		$rows = mysqli_query($db, "SELECT * FROM Abre_StudentSchedules WHERE siteID = '$siteID' LIMIT $recordCount, 100");

  		while ($row = mysqli_fetch_assoc($rows)){
  			$StudentIDStudentCSV = mysqli_real_escape_string($db, $row["StudentID"]);
  			$SchoolCodeStudentCSV = mysqli_real_escape_string($db, $row["SchoolCode"]);
  			$CourseCodeStudentCSV = mysqli_real_escape_string($db, $row["CourseCode"]);
  			$SectionCodeStudentCSV = mysqli_real_escape_string($db, $row["SectionCode"]);
  			$StaffIDStudentCSV = mysqli_real_escape_string($db, $row["StaffId"]);
  			$PeriodStudentCSV = mysqli_real_escape_string($db, $row["Period"]);
  			$SectionIDStudentCSV = $SchoolCodeStudentCSV.$CourseCodeStudentCSV.$SectionCodeStudentCSV.$StaffIDStudentCSV.$PeriodStudentCSV;

  			$csv= '"oh-hamil66375","'.$StudentIDStudentCSV.'","'.$SectionIDStudentCSV.'","","","","","","","","","","",""'."\n";
  			fwrite($fp, $csv);
  		}

  		if ($rows->num_rows != 100) break;

  		$recordCount += 100;

  		//if ($recordCount > 2000) break;
  	}
  	fclose($fp);
  	$sftp->put($remote_directory.'StudentSection.csv', $filestudent, SFTP::SOURCE_LOCAL_FILE);

  	//Export ZIP File for Archiving
  	$zip = new ZipArchive();

  	$filecron = tempnam(sys_get_temp_dir(), 'cron');

  	if ($zip->open($filecron, ZipArchive::CREATE) === TRUE)
  	{
  		$zip->addFromString('Student.csv', $student);
  		$zip->addFromString('Staff.csv', $staff);
  		$zip->addFromString('School.csv', $school);
  		$zip->addFromString('Section.csv', $section);
  		$zip->addFromString('StaffSection.csv', $staffsection);
  		$zip->addFile($filestudent, 'StudentSection.csv');
  		$ret = $zip->close();
  	}

  	// Put a copy on our SFTP server
  	$sftp = new SFTP($config->sftp->ip);
  	if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
  		throw new Exception("Login to our SFTP Server failed");
  	}

  	$remote_directory = '';

  	$sftp->put($remote_directory.'iReady.zip', $filecron, SFTP::SOURCE_LOCAL_FILE);
  } catch (Exception $ex) {
  	$error = $ex->getMessage();
  }

  $cronName = 'iReady Export';
  $details = [];
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
?>
