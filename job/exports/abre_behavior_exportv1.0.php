<?php
/*
* Copyright (C) 2016-2019 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Net\SFTP;

function csvLine($fields)
{
    $f = fopen('php://temp', 'r+');
    fputcsv($f, $fields);
    rewind($f);
    return fgets($f);
}

function cleanTextForCsv($text)
{
    if ($text === null) {
        return '';
    }
    // Remove HTML tags if any
    $text = strip_tags($text);

    // Replace any kind of line breaks, carriage returns, tabs, multiple spaces with a single space
    $text = preg_replace('/[\r\n\t]+/', ' ', $text);

    // Also collapse multiple spaces into one
    $text = preg_replace('/[ ]+/', ' ', $text);

    // Remove non-ASCII characters
    $text = preg_replace('/[^\x20-\x7E]/', '', $text);

    // Trim leading/trailing spaces
    return trim($text);
}

function runJob($db, $siteID, $config)
{
    try {
        $behaviorFile = tempnam(sys_get_temp_dir(), 'behavior-export');
        $csv = fopen($behaviorFile, "w");

        if ($siteID == '226') {
            $headers = [
                'Incident ID',
                'Submission Time',
                'Incident Date',
                'Incident Time',
                'Submitter Name',
                'Submitter Email',
                'Building',
                'Type',
                'Student ID',
                'Student First Name',
                'Student Middle Name',
                'Student Last Name',
                'Student Grade',
                'Student Ethnicity',
                'Student IEP Status',
                'Student 504 Status',
                'Economically Disadvantaged',
                'Behavior',
                'Behavior Codes',
                'Directed At Student ID',
                'Location',
                'Description/Facts',
                'Information',
                'Consequence Name',
                'Start Date',
                'Thru Date',
                'Total Days',
                'Served Status'
            ];
        } else {
            $headers = [
                'Incident ID',
                'Submission Time',
                'Incident Date',
                'Incident Time',
                'Submitter Name',
                'Submitter Email',
                'Building',
                'Type',
                'Student ID',
                'Student First Name',
                'Student Middle Name',
                'Student Last Name',
                'Student Grade',
                'Student Ethnicity',
                'Student IEP Status',
                'Student 504 Status',
                'Student Economically Disadvantaged Status',
                'Behavior',
                'Behavior Codes',
                'Directed At Student ID',
                'Location',
                'Description (Facts and Details Viewable by Parents)',
                'Information (Not Viewable by Parents)',
                'Response',
                'Start Date',
                'Thru Date',
                'Total Days',
                'Served Status'
            ];
        }
        fwrite($csv, rtrim(csvLine($headers)) . "\r\n");

        $currentSchoolYearID = getCurrentSchoolYearID($db);

        $query = "SELECT cd.ID, cd.Submission_Time, cd.Incident_Date, cd.Incident_Time,
                cd.Owner, cd.Owner_Name, cd.Building, cd.Type, cd.StudentID, cd.Student_IEP,
                cd.Student_FirstName, cd.Student_MiddleName, cd.Student_LastName,
                cd.student_504, cd.student_economically_disadvantaged, cd.Offence, cd.Offence_Codes, cd.directed_at,
                cd.Location, cd.Description, cd.Information,
                cdc.Consequence_Name, cdc.Serve_Date, cdc.Thru_Date, cdc.Total_Days,
                cdc.Consequence_Served, students.EthnicityDescription, students.CurrentGrade
              FROM conduct_discipline cd
              JOIN conduct_discipline_consequences cdc
                ON cd.ID = cdc.Incident_ID AND cd.siteID = cdc.siteID
              LEFT JOIN Abre_Students students
                ON cd.StudentID = students.StudentId AND cd.siteID = students.siteID
              WHERE cd.Archived = 0
                AND cd.siteID = ?
                AND cd.school_year_id = ?
                AND students.school_year_id = ?
              ORDER BY cd.Student_LastName, cd.Student_FirstName";
        $stmt = $db->stmt_init();
        $stmt->prepare($query);
        $stmt->bind_param("iii", $siteID, $currentSchoolYearID, $currentSchoolYearID);
        $stmt->execute();
        $stmt->bind_result(
            $id,
            $submissionTimeRaw,
            $incidentDate,
            $incidentTime,
            $owner,
            $ownerName,
            $building,
            $type,
            $studentID,
            $studentIEP,
            $studentFirstName,
            $studentMiddleName,
            $studentLastName,
            $student504,
            $studentED,
            $offence,
            $offenceCodes,
            $directedAt,
            $location,
            $description,
            $information,
            $consequence,
            $serveDate,
            $thruDate,
            $totalDays,
            $served,
            $ethnicityDescription,
            $currentGrade
        );
        while ($stmt->fetch()) {
            $submissionTime = new \DateTime($submissionTimeRaw, new \DateTimeZone('UTC'));
            $submissionTime->setTimeZone(new \DateTimeZone('America/New_York'));
            $submissionTime = $submissionTime->format("Y-m-d H:i:s");

            $offenceDisplay = str_replace(["'", "\"", "&quot;"], "", $offence);
            $servedText = $served ? "Yes" : "No";

            $data = [
                $id,
                $submissionTime,
                $incidentDate,
                $incidentTime,
                $ownerName,
                $owner,
                $building,
                $type,
                $studentID,
                $studentFirstName,
                $studentMiddleName,
                $studentLastName,
                $currentGrade,
                $ethnicityDescription,
                $studentIEP,
                $student504,
                $studentED,
                $offenceDisplay,
                $offenceCodes,
                $directedAt,
                $location,
                cleanTextForCsv($description),
                cleanTextForCsv($information),
                $consequence,
                $serveDate,
                $thruDate,
                $totalDays,
                $servedText
            ];
            fwrite($csv, rtrim(csvLine($data)) . "\r\n");
        }
        $stmt->close();
        fclose($csv);

        $warnings = [];
        uploadToAbreSftp($config->sftp, "Export_Abre_Behavior.csv", $behaviorFile, $warnings);
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $cronName = 'Behavior Export v1.0';
    $details = ["warnings" => $warnings];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
