<?php
/*
* Copyright (C) 2016-2019 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try {

    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $partnerFile = tempnam(sys_get_temp_dir(), 'partner-export');
    $csv = fopen($partnerFile, "w");

    $headers = [
      "Student Name",
      "Student ID",
      "Partner Name",
      "Program Name",
      "Program Status",
      "Consented"
    ];
    fputcsv($csv, $headers);

    $sql = "SELECT ps.student_id, students.FirstName, students.LastName,
              pp.Name, p.Name, ps.active, ps.access_level
            FROM program_students ps
            JOIN Abre_Students students
              ON ps.student_id = students.StudentId
                AND students.siteID = ?
            JOIN partner_programs pp
              ON ps.program_id = pp.ID AND ps.siteID = pp.siteID
                AND pp.active = 1
            JOIN partner p
              ON pp.partner_id = p.ID AND pp.siteID = p.siteID
                AND p.active = 1
            WHERE ps.siteID = ?
            AND students.school_year_id = ?
            ORDER BY students.LastName";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);
    $stmt->bind_param("iii", $siteID, $siteID, $currentSchoolYearID);
    $stmt->execute();
    $stmt->bind_result($studentID, $firstName, $lastName, $programName,
      $partnerName, $isActive, $accessLevel);
    while($stmt->fetch()){
      $status = $isActive ? "Active" : "Inactive";
      $consented = $accessLevel == "consented" ? "Yes" : "No";

      $studentName = "$firstName $lastName";

      $csvRow = [
        $studentName,
        $studentID,
        $programName,
        $partnerName,
        $status,
        $consented
      ];

      fputcsv($csv, $csvRow);
    }
    $stmt->close();
    fclose($csv);

    $warnings = [];
    uploadToAbreSftp($config->sftp, "Export_Abre_Partners.csv", $partnerFile, $warnings);

  }catch(Exception $ex){
  	$error = $ex->getMessage();
  }

  $cronName = 'Partner Export v1.0';
  $details = ["warnings" => $warnings];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
?>
