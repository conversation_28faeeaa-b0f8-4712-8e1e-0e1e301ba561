<?php

/*
* Copyright (C) Abre.io Inc.
*/

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config)
{
    try {
        $plansFile = tempnam(sys_get_temp_dir(), 'plans-export');
        $csv = fopen($plansFile, "w");

        $headers = [
            'district_id',
            'school_id',
            'student_id',
            'student_name',
            'plan_id',
            'plan_name',
            'plan_creation_date',
            'plan_form_id',
            'plan_response_id',
            'plan_response_date',
            'extended_time_1_5x',
            'extended_time_2_0x',
            'directions_primary_language',
            'bilingual_dictionary',
            'bilingual_test_form',
            'oral_translation',
            'transcription_to_english',
            'accessibility_information',
            'accessibility_information_text',
            'plan_response_user_email',
            'plan_response_user_name'
        ];
        fputcsv($csv, $headers);

        $query = "SELECT 
            stu.siteID as district_id,
            stu.schoolcode as school_id,
            p.subjectID AS student_id,  
            CONCAT(stu.LastName,', ',stu.FirstName) AS student_name,
            p.ID AS plan_id,
            p.name AS plan_name,  
            p.creationDate as plan_creation_date,
            p.formId AS plan_form_id,
            pr.ID AS plan_response_id,
            pr.UpdateDate AS plan_response_date,
            CASE WHEN pr.response LIKE '%Extended Time | 1.5x%' THEN 'True' ELSE 'False' END AS extended_time_1_5x,
            CASE WHEN pr.response LIKE '%Extended Time | 2x%' THEN 'True' ELSE 'False' END AS extended_time_2_0x,
            CASE WHEN pr.response LIKE '%General Directions in Primary Academic Language%' THEN 'True' ELSE 'False' END AS directions_primary_language,
            CASE WHEN pr.response LIKE '%Word-to-Word Bilingual Dictionary%' THEN 'True' ELSE 'False' END AS bilingual_dictionary,
            CASE WHEN pr.response LIKE '%Spanish / Bilingual Test Form%' THEN 'True' ELSE 'False' END AS bilingual_test_form,
            CASE WHEN pr.response LIKE '%Oral Translation of Assessment%' THEN 'True' ELSE 'False' END AS oral_translation,
            CASE WHEN pr.response LIKE '%Transcription (to English)%' THEN 'True' ELSE 'False' END AS transcription_to_english,
            CASE WHEN pr.response LIKE '%Include_information_that_may_affect_and/or_support_the_use_of_accessibility_features_in_instruction_and_assessment.%' THEN 'True' ELSE 'False' END AS accessibility_information,
            JSON_UNQUOTE(
                JSON_EXTRACT(
                    pr.response, 
                    '$.\"Include information that may affect and/or support the use of accessibility features in instruction and assessment.\"'
                )
            ) AS accessibility_information_text,
            pr.user AS plan_response_user_email,
            CONCAT(sta.LastName,', ',sta.FirstName) AS plan_response_user_name
        FROM plans AS p
        INNER JOIN plans_responses AS pr ON p.ID = pr.planId
        INNER JOIN Abre_Students AS stu ON stu.studentID = p.subjectID
                                    AND stu.siteID = p.siteID
                                    AND stu.school_year_id = (SELECT MAX(school_year_id) FROM Abre_Students where siteID = ?)
        INNER JOIN Abre_Staff AS sta ON sta.EMail1 = pr.user 
                                 AND sta.siteID = p.siteID 
                                 AND sta.school_year_id = stu.school_year_id
        WHERE p.siteID = ?";

        $stmt = $db->stmt_init();
        $stmt->prepare($query);
        $stmt->bind_param("ii", $siteID, $siteID);
        $stmt->execute();
        
        // Initialize variables before binding
        $districtId = $schoolId = $studentId = $studentName = $planId = $planName = '';
        $planCreationDate = $planFormId = $planResponseId = $planResponseDate = '';
        $extendedTime15x = $extendedTime20x = $directionsLanguage = $bilingualDict = '';
        $bilingualTest = $oralTranslation = $transcription = $accessibilityInfo = '';
        $accessibilityText = $responseUserEmail = $responseUserName = '';

        $stmt->bind_result(
            $districtId,
            $schoolId,
            $studentId,
            $studentName,
            $planId,
            $planName,
            $planCreationDate,
            $planFormId,
            $planResponseId,
            $planResponseDate,
            $extendedTime15x,
            $extendedTime20x,
            $directionsLanguage,
            $bilingualDict,
            $bilingualTest,
            $oralTranslation,
            $transcription,
            $accessibilityInfo,
            $accessibilityText,
            $responseUserEmail,
            $responseUserName
        );

        while ($stmt->fetch()) {
            $data = [
                $districtId,
                $schoolId,
                $studentId,
                $studentName,
                $planId,
                $planName,
                $planCreationDate,
                $planFormId,
                $planResponseId,
                $planResponseDate,
                $extendedTime15x,
                $extendedTime20x,
                $directionsLanguage,
                $bilingualDict,
                $bilingualTest,
                $oralTranslation,
                $transcription,
                $accessibilityInfo,
                $accessibilityText,
                $responseUserEmail,
                $responseUserName
            ];
            fputcsv($csv, $data);
        }
        $stmt->close();
        fclose($csv);

        $warnings = [];
        uploadToAbreSftp($config->sftp, 'Export_Abre_Plans.csv', $plansFile, $warnings);

        unlink($plansFile);
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $cronName = 'Abre Plans Export';
    $details = ["warnings" => $warnings];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
