<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config) {
  require_once(dirname(__FILE__) . '/../utils/logging.php');

  try {
    if (!$siteID) {
      $siteID = 0; // for logging
      throw new Exception("no siteID provided.");
    }
    $settings = $config->nwea;

    if (is_null($settings)) {
      throw new Exception("siteID $siteID is not unrecognized");
    }

    $warnings = [];
    $rosterFile = tempnam(sys_get_temp_dir(), 'map-roster-');
    generateRoster($db, $rosterFile, $siteID, $warnings);

    $result = uploadFile($settings, $rosterFile);
    if ($result["code"] >= 300) {
      throw new Exception("$result[code] - $result[message]");
    }
  } catch(Exception $ex) {
    $error = $ex->getMessage();
  }

  uploadToAbreSftp($config->sftp, "NWEA Rostering.csv", $rosterFile, $warnings);

  $cronName = 'NWEA Rostering';
  $details = ["warnings" => $warnings];
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}

// FUNCTIONS
function generateRoster($db, $rosterFile, $siteID, &$warnings) {
  // some schools dont have ethnicity codes, so we're using description to be safer
  $ethnicityMap = [
    'am indian/alask' => 'American Indian or Alaskan Native',
    'american indian or alaskan native' => 'American Indian or Alaskan Native',
    'american indian/alaska native' => 'American Indian or Alaskan Native',
    'am indian/alaskan native - persons having origins in any of the peoples of north and south america ()including central america) who maintain tribal affiliation or community attachment.' => 'American Indian or Alaskan Native',
    'american indian, alaskan native' => 'American Indian or Alaskan Native',
    'i' => 'American Indian or Alaskan Native',

    'asian' => 'Asian',
    'a' => 'Asian',
    'peoples of the far east, southeast asia, or the indian subcontinent. ex. cambodia, china, india, japan, korea, malaysia, pakistan, the philippine islands, thailand, and vietnam.' => 'Asian',
    'asian - example:  cambodia, china, india, japan, korea, malaysia, pakistan, the philippine islands, thailand and vietnam.' => 'Asian',

    'black or african-american, non-hispanic' => 'Black or African American',
    'black (non-hispanic)' => 'Black or African American',
    'black/nonhispan' => 'Black or African American',
    'b' => 'Black or African American',
    '(non-hispanic)persons having origins in any of the black racial groups in africa.' => 'Black or African American',
    'black or african american (non-hispanic) - persons having origins in any of the black racial groups in africa.' => 'Black or African American',
    'black' => 'Black or African American',
    'black or african american (non hispanic)' => 'Black or African American',
    'black/african american' => 'Black or African American',
    'black, non hispanic' => 'Black or African American',

    'hispanic/latino' => 'Hispanic or Latino',
    'hispanic' => 'Hispanic or Latino',
    'h' => 'Hispanic or Latino',
    'persons of mexican, puerto rican, cuban, central or south american, or other spanish culture or origin regardless of race.' => 'Hispanic or Latino',
    'hispanic/latino - persons of mexican, puerto rican, cuban, central or south american of other spanish culture or origin regardless of race.' => 'Hispanic or Latino',

    'multiracial' => 'Multi-ethnic',
    'm' => 'Multi-ethnic',
    'multi-racial' => 'Multi-ethnic',
    'multi-racial - persons having origins in two or more of the above options.' => 'Multi-ethnic',

    'native hawaiian or other pacific islander' => 'Native Hawaiian or Other Pacific Islander',
    'native hawaiian or other pacif' => 'Native Hawaiian or Other Pacific Islander',
    'native hawaiian or pacific islander' => 'Native Hawaiian or Other Pacific Islander',
    'native hawaiian or other pacific islander - persons having origins in any of the original peoples of hawaii, guam, samoa, or other pacific islands.' => 'Native Hawaiian or Other Pacific Islander',

    ' ' => 'Not Specified or Other',
    '' => 'Not Specified or Other',

    'white, non-hispanic' => 'White',
    'white - non hispanic' => 'White',
    'white (non-hispanic)' => 'White',
    'people who have origins in any of the original peoples of europe, north africa, or the middle east.' => 'White',
    'white, non-hispanic - people who have origins in any of the original peoples of europe, north africa, or the middle east.' => 'White',
    'white' => 'White',
    'white/nonhispan' => 'White',
    'w' => 'White',
  ];

  $gradeMap = [
    "1" => 1,
    "01" => 1,
    "2" => 2,
    "02" => 2,
    "3" => 3,
    "03" => 3,
    "4" => 4,
    "04" => 4,
    "5" => 5,
    "05" => 5,
    "6" => 6,
    "06" => 6,
    "7" => 7,
    "07" => 7,
    "8" => 8,
    "08" => 8,
    "9" => 9,
    "09" => 9,
    "10" => 10,
    "11" => 11,
    "12" => 12,
    "KG" => "K",
    "ADKG" => "K",
    "PS" => "PK",
    "PST" => "PK"
    // 23
    // 13
    // IN
    // UG
  ];


  $csv = fopen($rosterFile, "w+");
  if ($csv !== false) {
    try {
      $header = [
        "School State Code",          // optional
        "School Name",
        "Previous Instructor ID",     // optional
        "Instructor ID",
        "Instructor State ID",        // optional
        "Instructor Last Name",
        "Instructor First Name",
        "Instructor Middle Initial",  // optional
        "User Name",
        "Email Address",
        "Class Name",
        "Previous Student ID",        // optional
        "Student ID",
        "Student State ID",           // optional
        "Student Last Name",
        "Student First Name",
        "Student Middle Initial",     // optional
        "Student Date Of Birth",
        "Student Gender",
        "Student Grade",
        "Student Ethnic Group Name",
        "Student User Name",          // optional
        "Student Email"               // optional
      ];
      fputcsv($csv, $header);

      $sql = "SELECT stud_sched.SchoolCode, staff.StaffID, staff.LastName AS StaffLastName, staff.FirstName AS StaffFirstName,
                  SUBSTR(staff.MiddleName, 1, 1) AS StaffMiddleInitial, staff.EMail1 AS StaffEmail, stud_sched.CourseCode,
                  stud_sched.CourseName, stud_sched.Period, stud_sched.RoomNumber, stud.StudentId, stud.SSID,
                  stud.LastName AS StudentLastName, stud.FirstName AS StudentFirstName,
                  SUBSTR(stud.MiddleName, 1, 1) AS StudentMiddleInitial, stud.DateOfBirth AS StudentDateOfBirth,
                  stud.Gender AS StudentGender, stud.CurrentGrade AS StudentCurrentGrade,
                  LOWER(stud.EthnicityDescription) AS StudentEthnicity, stud.Email AS StudentEmail
              FROM Abre_StudentSchedules stud_sched
              JOIN Abre_Students stud ON stud_sched.StudentId = stud.StudentId AND stud_sched.siteID = stud.siteID
              JOIN Abre_Staff staff ON stud_sched.StaffId = staff.StaffID AND stud_sched.siteID = staff.siteID
              WHERE stud_sched.siteID = $siteID AND TRIM(staff.EMail1) != ''";

      $result = $db->query($sql);

      $studentIssues = [];
      $staffIssues = [];

      $recordStudentIssue = function($row, $reason) use (&$studentIssues) {
        if (!array_key_exists($row['StudentId'], $studentIssues)) {
          $studentIssues[$row['StudentId']] = [
            "reason" => $reason,
            "row" => $row
          ];
        }
      };

      $recordStaffIssue = function($row, $reason) use (&$staffIssues) {
        if (!array_key_exists($row['StaffID'], $staffIssues)) {
          $staffIssues[$row['StaffID']] = [
            "reason" => $reason,
            "row" => $row
          ];
        }
      };

      $illegalNameCharacters = ['<', '>', '[', ']', '{', '}', '|', '=', '!', '$', '%', '&', '~', '+', ':', ';', '?', '@', '\\', '^'];

      while ($dbRow = $result->fetch_assoc()) {
        if (array_key_exists($dbRow["StudentCurrentGrade"], $gradeMap)) {
          $grade = $gradeMap[$dbRow["StudentCurrentGrade"]];
        } else {
          $recordStudentIssue($dbRow, "Unknown Grade ($dbRow[StudentCurrentGrade])");
          continue;
        }

        if (array_key_exists($dbRow["StudentEthnicity"], $ethnicityMap)) {
          $ethnicity = $ethnicityMap[$dbRow["StudentEthnicity"]];
        } else {
          $recordStudentIssue($dbRow, "Unknown Ethnicity ($dbRow[StudentEthnicity])");
          continue;
        }

        if (TRIM($dbRow["StudentEmail"]) == '') {
          $recordStudentIssue($dbRow, "No email provided");
          continue;
        }

        if (TRIM($dbRow["SSID"]) == '') {
          $recordStudentIssue($dbRow, "No Student State ID");
          continue;
        }

        $illegalChracterFound = false;
        foreach ($illegalNameCharacters as $c) {
          if (strpos($dbRow["StudentFirstName"], $c) !== false ||
              strpos($dbRow["StudentLastName"], $c) !== false ||
              strpos($dbRow["StudentMiddleInitial"], $c) !== false) {
            $recordStudentIssue($dbRow, "Illegal character in name");
            $illegalChracterFound = true;
            break;
          }
          if (strpos($dbRow["StaffFirstName"], $c) !== false ||
              strpos($dbRow["StaffLastName"], $c) !== false ||
              strpos($dbRow["StaffMiddleInitial"], $c) !== false) {
            $recordStaffIssue($dbRow, "Illegal character in name");
            $illegalChracterFound = true;
            break;
          }
        }
        if ($illegalChracterFound) {
          continue;
        }

        // TODO student names will be different in every district
        $studentUsername = substr($dbRow["StudentFirstName"], 0, 1) .
          $dbRow["StudentLastName"] .
          $dbRow["StudentId"];
        $studentUsername = str_replace(' ', '', $studentUsername);

        // TODO Ross is using SSID instead of a local id, this will be district specific
        $studentID = $dbRow["SSID"];

        // TODO? can we force this format on ppl?
        // TODO the Kindergarten change is for Ross -- will other districts need this?
        $course = $grade == "K"
          ? "$dbRow[CourseName] - $dbRow[Period] - $dbRow[StaffID] - $dbRow[RoomNumber]"
          : "$dbRow[CourseName] - $dbRow[Period] - $dbRow[StaffID]";

        $csvRow = [
          '',                             // state school code - TODO Some might have this
          $dbRow["SchoolCode"],
          '',                             // previous staff id
          $dbRow["StaffID"],
          '',                             // Staff State ID
          $dbRow["StaffLastName"],
          $dbRow["StaffFirstName"],
          $dbRow["StaffMiddleInitial"],
          $dbRow["StaffEmail"],
          $dbRow["StaffEmail"],
          $course,
          '',                             // previous student id
          $studentID,
          $dbRow["SSID"],
          $dbRow["StudentLastName"],
          $dbRow["StudentFirstName"],
          $dbRow["StudentMiddleInitial"],
          $dbRow["StudentDateOfBirth"],
          $dbRow["StudentGender"],
          $grade,
          $ethnicity,
          $studentUsername,
          $dbRow["StudentEmail"],
        ];

        fputcsv($csv, $csvRow);
      }

      foreach ($studentIssues as $si) {
        $row = $si['row'];
        $warnings []= "Skipped $row[StudentFirstName] $row[StudentLastName] ($row[StudentId]) - $si[reason]";
      }
      foreach ($staffIssues as $si) {
        $row = $si['row'];
        $warnings []= "Skipped $row[StaffFirstName] $row[StaffLastName] ($row[StaffID]) - $si[reason]";
      }


    } finally {
      fclose($csv);
    }
  }
}

function uploadFile($settings, $rosterFile) {
  $url = "https://api.mapnwea.org/services/rostering/submit/complete/replace";

  $auth = base64_encode("$settings->userName:$settings->password");
  $authHeader = "Authorization: Basic $auth";

  $args = ["file" => new CurlFile($rosterFile, 'application/octet-stream', 'rf.csv')];

  $curl = curl_init();
  try {
    curl_setopt_array($curl, [
      CURLOPT_URL => $url,
      CURLOPT_HTTPHEADER => array($authHeader),
      CURLOPT_POST => 1,
      CURLOPT_POSTFIELDS => $args,
      CURLOPT_RETURNTRANSFER => true
    ]);

    $result = curl_exec($curl);
    $code = curl_getinfo($curl, CURLINFO_RESPONSE_CODE);

    return [
      "code" => $code,
      "message" => $result
    ];
  } finally {
    curl_close($curl);
  }
}
?>
