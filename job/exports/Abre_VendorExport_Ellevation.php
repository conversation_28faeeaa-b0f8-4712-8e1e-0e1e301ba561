<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try {

  	$sftp = new SFTP('sftp.ellevationeducation.com');
  	if (!$sftp->login($config->ellevation->userName, $config->ellevation->password)) {
  		throw new Exception("Login to Ellevation SFTP Server failed");
  	}

  	$remote_directory = '/data/';

  	//Staff Roster CSV
  	$csv = '"StaffID","EmailAddress","FirstName","LastName","SchoolLEACode","SchoolName","Role","Group"'."\n";
  	$rows = mysqli_query($db, "SELECT StaffID, EMail1, FirstName, LastName, SchoolCode FROM Abre_Staff WHERE siteID = '$siteID' GROUP BY StaffID");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$StaffID = $row["StaffID"];
  		$EMail1 = $row["EMail1"];
  		$FirstName = $row["FirstName"];
  		$LastName = $row["LastName"];
  		$SchoolCode = $row["SchoolCode"];

  		//Get Name of Building Given SchoolCode
  		$rows2 = mysqli_query($db, "SELECT SchoolName FROM Abre_Students WHERE SchoolCode = '$SchoolCode' AND siteID = '$siteID' LIMIT 1");
  		while ($row2 = mysqli_fetch_assoc($rows2))
  		{
  			$SchoolName = $row2["SchoolName"];
  		}

  		$csv.= '"'.$StaffID.'","'.$EMail1.'","'.$FirstName.'","'.$LastName.'","'.$SchoolCode.'","'.$SchoolName.'","Teacher","User"'."\n";
  	}
  	$staffroster = $csv;
  	//$sftp->put($remote_directory.'staff_roster.csv', $staffroster);

  	//Student CSV
  	$csv = '"LastName","MiddleName","FirstName","ActiveStatus","SchoolLEACode","SchoolName","ESL Teacher ID","ESL Teacher Name","StudentLocalID","StudentTestID"'."\n";
  	$rows = mysqli_query($db, "SELECT LastName, MiddleName, FirstName, Status, SchoolCode, SchoolName, StudentId FROM Abre_Students WHERE siteID = '$siteID' GROUP BY StudentId");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$LastName = $row["LastName"];
  		$MiddleName = $row["MiddleName"];
  		$FirstName = $row["FirstName"];
  		$Status = $row["Status"];
  		$SchoolCode = $row["SchoolCode"];
  		$SchoolName = $row["SchoolName"];
  		$StudentId = $row["StudentId"];

  		$csv.= '"'.$LastName.'","'.$MiddleName.'","'.$FirstName.'","'.$Status.'","'.$SchoolCode.'","'.$SchoolName.'","","","'.$StudentId.'","'.$StudentId.'"'."\n";
  	}
  	$student = $csv;
  	//$sftp->put($remote_directory.'student.csv', $student);

  	//Schedule CSV
  	$csv = '"StudentID","Course Name","Course Period","Course Code","StaffID","Teacher Name","Term Name"'."\n";
  	$rows = mysqli_query($db, "SELECT StudentID, CourseName, Period, CourseCode, StaffId, TeacherName, TermCode FROM Abre_StudentSchedules WHERE siteID = '$siteID'");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$StudentID = $row["StudentID"];
  		$CourseName = $row["CourseName"];
  		$Period = $row["Period"];
  		$CourseCode = $row["CourseCode"];
  		$StaffId = $row["StaffId"];
  		$TeacherName = $row["TeacherName"];
  		$TermCode = $row["TermCode"];
  		if($TermCode == "Sem1"){ $TermCode="Term 1"; }
  		if($TermCode == "Sem2"){ $TermCode="Term 2"; }
  		if($TermCode == "Year"){ $TermCode="Full Year"; }

  		$csv.= '"'.$StudentID.'","'.$CourseName.'","'.$Period.'","'.$CourseCode.'","'.$StaffId.'","'.$TeacherName.'","'.$TermCode.'"'."\n";
  	}
  	$schedule = $csv;
  	//$sftp->put($remote_directory.'schedule.csv', $schedule);

  	//Create Zip File
  	$zip = new ZipArchive();
  	$filecron = tempnam(sys_get_temp_dir(), 'cron');
  	if ($zip->open($filecron, ZipArchive::CREATE) === TRUE)
  	{
  		$zip->addFromString('staff_roster.csv', $staffroster);
  		$zip->addFromString('student.csv', $student);
  		$zip->addFromString('schedule.csv', $schedule);
  		$ret = $zip->close();
  	}

  	// Put a copy on Abre SFTP server
  	$sftp = new SFTP($config->sftp->ip);
  	if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
  		throw new Exception("Login to our SFTP Server failed");
  	}

  	$remote_directory = '';
  	$sftp->put($remote_directory.'Ellevation.zip', $filecron, SFTP::SOURCE_LOCAL_FILE);
  } catch (Exception $ex) {
  	$error = $ex->getMessage();
  }

  $cronName = 'Ellevation Export';
  $details = [];
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
?>