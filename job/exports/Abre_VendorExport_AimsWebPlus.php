<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try {

  	$semesterDates = getSemesterDates($db, $siteID);

    $districtID = $config->aimsweb->districtID;
    $districtName = $config->aimsweb->districtName;
    $districtAbbreviation = $config->aimsweb->districtAbbreviation;

  	$sftp = new SFTP('aimswebsftp.pearson.com');
  	if (!$sftp->login($config->aimsweb->userName, $config->aimsweb->password)) {
  		throw new Exception("Login to AimsWebPlus SFTP Server failed");
  	}

  	$remote_directory = '/';

    //AimsWeb TXT
  	$schoolYear="2019-2020";
  	$txt = "SchoolYear\tDistrictUID\tDistrictName\tDistrictAbbreviation\tDistrictNCESID\tSchoolUID\tSchoolName\t";
  	$txt.="SchoolLevel\tSchoolAbbreviation\tSchoolNCESID\tClassUID\tClassName\tTeacherUsername\tTeacherUID\tTeacherFirstName\tTeacherMiddleName\tTeacherLastName\tTeacherEmail\tTeacherPhone\t";
  	$txt.="StudentID\tSSID\tStudentFirstName\tStudentMiddleName\tStudentLastName\tActive\tEntryDate\tExitDate\tStudentEmail\tGrade\tEntryGrade\tDOB\tIEP_Reading\tIEP_LangArts\t";
  	$txt.="IEP_Writing\tIEP_Math\tIEP_Behavior\tGender\tServiceCode\tESL\tMealStatus\tEthnicity\tSection504\tAfter_School\tCorrectional\tSummer_School\tIDEA\tGifted_Talented\t";
  	$txt.="Autism\tDeaf_Blindness\tEmotional_Disturbance\tHearing_Impairment_Deafness\tIntellectual_Disability\tMultiple_Disabilities\tOrthopedic_Impairment\tOther_Health_Impairment\t";
  	$txt.="Specific_Learning_Disability\tSpeech_Language_Impairment\tTraumatic_Brain_Injury\tVisual_Impairment_Blindness\tDevelopmental_Delay\tIntervention_Level\tMobility\tBehavioral_Disorder\n";
  	$rows = mysqli_query($db, "SELECT ss.SchoolCode, ss.StaffId, ss.CourseName, ss.CourseCode, ss.SectionCode, ss.TermCode, ss.StudentID, ss.FirstName AS StudentFirstName, ss.LastName AS StudentLastName, s.SchoolName,
  		staff.EMail1, staff.FirstName AS TeacherFirstName, staff.MiddleName AS TeacherMiddleName, staff.LastName AS TeacherLastName, s.CurrentGrade, s.DateOfBirth, s.Gender, s.EthnicityCode, s.IEP
  		FROM Abre_StudentSchedules AS ss JOIN Abre_Students AS s ON ss.StudentID = s.StudentId
  		AND ss.siteID = s.siteID JOIN Abre_Staff AS staff ON ss.StaffId = staff.StaffID
  		AND ss.siteID = staff.siteID WHERE ss.siteID = '$siteID' AND (s.Status='A' OR s.Status='U' OR s.Status='N')");
  	while ($row = mysqli_fetch_assoc($rows))
  	{
  		$SchoolUID = $row["SchoolCode"];
  		$TeacherUID = $row["StaffId"];
  		$TermCode = $row["TermCode"];
  		$ClassName = $row["CourseName"];
  		$CourseCode = $row["CourseCode"];
  		$SectionCode = $row["SectionCode"];
  		$ClassName = '"'.$ClassName.'-'.$TermCode.'-'.$TeacherUID.'-'.$CourseCode.'-'.$SectionCode.'"';
  		$ClassUID = $CourseCode.$SectionCode.$TermCode.$TeacherUID;
  		$ClassUID = preg_replace("/[^a-zA-Z0-9]+/", "", $ClassUID);
  		$StudentID = $row["StudentID"];
  		$StudentFirstName = $row["StudentFirstName"];
  		$StudentLastName = $row["StudentLastName"];
  		$SchoolName = $row["SchoolName"];

  		//Teacher email
  		$TeacherEmail = $row["EMail1"];
  		$teacherUsernameParts = explode("@", $TeacherEmail);
  		$TeacherUsername = $teacherUsernameParts[0];

  		//Teacher name
  		$TeacherFirstName = $row["TeacherFirstName"];
  		$TeacherMiddleName = $row["TeacherMiddleName"];
  		$TeacherLastName = $row["TeacherLastName"];

  		//Grade
  		$Grade = $row["CurrentGrade"];
  		switch($Grade){
  			case "KG":
  					$Grade = "K";
  					break;
  			case "PS":
  					$Grade = "Pre-K";
  					break;
  			case "23":
  					$Grade = "12";
  					break;
  			default:
  					$Grade = $Grade;
  					break;
  		}

      $Grade = ltrim($Grade, '0');

  		$DOB = $row["DateOfBirth"];
      $DOBFormatted = date("m-d-Y", strtotime($DOB));

  		$Gender = $row["Gender"];

  		//Ethnicity
  		$ethnicityCode = $row["EthnicityCode"];
  		$Ethnicity = "";
  		switch($ethnicityCode){
  			case "I":
  					$Ethnicity = "AM";
  					break;
  			case "A":
  					$Ethnicity = "AS";
  					break;
  			case "B":
  					$Ethnicity = "BL";
  					break;
  			case "H":
  					$Ethnicity = "HI";
  					break;
  			case "M":
  					$Ethnicity = "MR";
  					break;
  			case "P":
  					$Ethnicity = "PI";
  					break;
  			case "W":
  					$Ethnicity = "WH";
  					break;
  			default:
  					$Ethnicity = "";
  					break;
  		}

  		//Look up school level
  		$SchoolLevel = "";
  		switch($SchoolUID){
  			case "S001":
  		  	$SchoolLevel = "HS";
  		  	break;
  			case "S003":
  		  	$SchoolLevel = "JH";
  		  	break;
  			case "S007":
  		  	$SchoolLevel = "ELEM";
  		  	break;
  			case "S008":
  				$SchoolLevel = "ELEM";
  				break;
  		  default:
  		 		$SchoolLevel = "ELEM";
  		}

  		//Empty Values
  		$DistrictNCESID = "";
  		$SchoolAbbreviation = "";
  		$SchoolNCESID = "";
  		$TeacherPhone = "";
  		$SSID = "";
  		$StudentMiddleName = "";
  		$Active = "";
  		$EntryDate = "";
  		$exitDate = "07-31-2020";
  		$StudentEmail = "";
  		$EntryGrade = "";
  		$IEP_Reading = "";
  		$IEP_LangArts = "";
  		$IEP_Writing = "";
  		$IEP_Math = "";
  		$IEP_Behavior = "";

  		//Service Code
  		$IEP = $row["IEP"];
  		$ServiceCode = "";
  		switch($IEP){
  			case "Y":
  				$ServiceCode = "S";
  				break;
  			default:
  				$ServiceCode = "G";
  		}

  		$ESL = "";
  		$MealStatus = "";
  		$blank = "";

  		$txt.="$schoolYear\t$districtID\t$districtName\t$districtAbbreviation\t$DistrictNCESID\t$SchoolUID\t$SchoolName\t$SchoolLevel\t$SchoolAbbreviation\t$SchoolNCESID\t$ClassUID\t$ClassName\t$TeacherUsername\t$TeacherUID\t$TeacherFirstName\t$TeacherMiddleName\t$TeacherLastName\t$TeacherEmail\t$TeacherPhone\t$StudentID\t$SSID\t$StudentFirstName\t$StudentMiddleName\t$StudentLastName\t$Active\t$EntryDate\t$exitDate\t$StudentEmail\t$Grade\t$EntryGrade\t$DOBFormatted\t$IEP_Reading\t$IEP_LangArts\t$IEP_Writing\t$IEP_Math\t$IEP_Behavior\t$Gender\t$ServiceCode\t$ESL\t$MealStatus\t$Ethnicity\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\t$blank\n";

  	}

  	$aimsWeb = $txt;

  	$sftp->put($remote_directory.'aimsWebPlus.txt', $txt);

  	$zip = new ZipArchive();
  	$filecron = tempnam(sys_get_temp_dir(), 'cron');
  	if($zip->open($filecron, ZipArchive::CREATE) === TRUE){
  		$zip->addFromString('aimsWebPlus.txt', $aimsWeb);
  		$ret = $zip->close();
  	}

  	// Put a copy on our SFTP server
  	$sftp = new SFTP($config->sftp->ip);
  	if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
  		throw new Exception("Login to our SFTP Server failed");
  	}

  	$remote_directory = '';

  	$sftp->put($remote_directory.'aimsWebPlus.zip', $filecron, SFTP::SOURCE_LOCAL_FILE);
  } catch (Exception $ex) {
  	$error = $ex->getMessage();
  }

  $cronName = 'AimsWebPlus Export';
  $details = [];
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
?>
