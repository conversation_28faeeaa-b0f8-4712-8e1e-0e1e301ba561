<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try {

    //generate unique user id
    function uniqueResonantId($role, $tenantSiteID, $userId){
      $uniqueString = $role . $tenantSiteID . $userId;
      $uniqueId = base64_encode($uniqueString);
      return $uniqueId;
    }

    //get current school year
    $currentSchoolYearID = getCurrentSchoolYearID($db);

    /*
      organizations.csv, organization-properties.csv, users.csv, and user-properties.csv
    */
    $fileOrganizations = tempnam(sys_get_temp_dir(), 'resonant-organizations-export');
    $csvOrganizations = fopen($fileOrganizations, "w");
    $fileOrganizationProperties = tempnam(sys_get_temp_dir(), 'resonant-organizationsproperties-export');
    $csvOrganizationProperties = fopen($fileOrganizationProperties, "w");
    $fileUsers = tempnam(sys_get_temp_dir(), 'resonant-users-export');
    $csvUsers = fopen($fileUsers, "w");
    $fileUserProperties = tempnam(sys_get_temp_dir(), 'resonant-usersproperties-export');
    $csvUserProperties = fopen($fileUserProperties, "w");

    $sqlOrganizations = "SELECT atm.district_name, ar.site_id, ar.buildings, ar.grades, ar.parents
            FROM abre_wellness ar
            LEFT JOIN abre_tenant_map atm
            ON ar.site_id = atm.siteID
            WHERE ar.type = 'resonant'";
    $stmtOrganizations = $db->stmt_init();
    $stmtOrganizations->prepare($sqlOrganizations);
    $stmtOrganizations->execute();
    $stmtOrganizations->store_result();
    $stmtOrganizations->bind_result($organizationName, $tenantSiteID, $resonantBuildings, $resonantGrades, $resonantParents);
    while($stmtOrganizations->fetch()){

      $organizationParentId = "0000";

      //write row to organizations file
      $csvRowOrganizations = [
        $tenantSiteID,
        $organizationName,
        "",
        "",
        "",
        "",
        $organizationParentId,
        "",
        "District",
        ""
      ];
      fputcsv($csvOrganizations, $csvRowOrganizations);

      //write row to organization-properties file
      $csvRowOrganizationProperties = [
        $tenantSiteID,
        "",
        "Partnership",
        "",
        "Abre"
      ];
      fputcsv($csvOrganizationProperties, $csvRowOrganizationProperties);

      //write students to users file if eligible (licensed)
      $gradeQuery = "";
      if($resonantGrades){
        $resonantGradesExploded = explode(',', $resonantGrades);
        foreach($resonantGradesExploded as $grade) {
          $gradeQuery = $gradeQuery . " OR abst.CurrentGrade = '$grade'";
        }
        $gradeQuery = ltrim($gradeQuery," OR ");
        $gradeQuery = "AND " . "(" . $gradeQuery . ")";
      }

      $buildingQuery = "";
      if($resonantBuildings){
        $resonantBuildingsExploded = explode(',', $resonantBuildings);
        foreach($resonantBuildingsExploded as $building) {
          $buildingQuery = $buildingQuery . " OR abst.SchoolCode = '$building' ";
        }
        $buildingQuery = ltrim($buildingQuery," OR ");
        $buildingQuery = "AND " . "(" . $buildingQuery . ")";
      }

      $eligibleStudents = array();
      $sqlStudentUsers = "SELECT abst.StudentId, abst.FirstName, abst.LastName, abrr.Email, abrr.opt_out, abst.Gender, abst.SchoolCode, abst.CurrentGrade
              FROM abre_wellness_respondents abrr
              JOIN Abre_Students abst
              ON abrr.student_id = abst.StudentId
              AND abrr.site_id = abst.siteID
              WHERE abrr.role = 'student'
              $gradeQuery
              $buildingQuery
              AND abst.school_year_id = ?
              AND abrr.site_id = ?
              GROUP BY abst.StudentId";
      $stmtStudentUsers = $db->stmt_init();
      $stmtStudentUsers->prepare($sqlStudentUsers);
      $stmtStudentUsers->bind_param("ii", $currentSchoolYearID, $tenantSiteID);
      $stmtStudentUsers->execute();
      $stmtStudentUsers->store_result();
      $stmtStudentUsers->bind_result($studentId, $studentFirstName, $studentLastName, $studentEmail, $studentAbreOptOutStatus, $studentGender, $studentBuildingCode, $studentGrade);
      while($stmtStudentUsers->fetch()){

        array_push($eligibleStudents, $studentId);
        $studentUniqueId = uniqueResonantId("student", $tenantSiteID, $studentId);

        //write student row to users file
        $csvRowStudentUsers = [
          $studentUniqueId,
          "",
          $studentAbreOptOutStatus,
          $studentFirstName,
          "",
          $studentLastName,
          $studentEmail,
          "",
          "Student",
          $tenantSiteID,
          "Survey Respondent"
        ];
        fputcsv($csvUsers, $csvRowStudentUsers);

        //student grade mapping
        $studentMappedGrade = "";
        switch($studentGrade){
          case "PK":
            $studentMappedGrade = "PK";
          case "K":
          case "KG":
            $studentMappedGrade = "K";
            break;
          case "-2":
          case "-02":
            $studentMappedGrade = "PK";
            break;
          case "-1":
          case "-01":
            $studentMappedGrade = "PK";
            break;
          case "0":
          case "00":
            $studentMappedGrade = "K";
            break;
          case "1":
          case "01":
            $studentMappedGrade = "1";
            break;
          case "2":
          case "02":
            $studentMappedGrade = "2";
            break;
          case "3":
          case "03":
            $studentMappedGrade = "3";
            break;
          case "4":
          case "04":
            $studentMappedGrade = "4";
            break;
          case "5":
          case "05":
            $studentMappedGrade = "5";
            break;
          case "6":
          case "06":
            $studentMappedGrade = "6";
            break;
          case "7":
          case "07":
            $studentMappedGrade = "7";
            break;
          case "8":
          case "08":
            $studentMappedGrade = "8";
            break;
          case "9":
          case "09":
            $studentMappedGrade = "9";
            break;
          case "10":
            $studentMappedGrade = "10";
            break;
          case "11":
            $studentMappedGrade = "11";
            break;
          case "12":
            $studentMappedGrade = "12";
            break;
          default:
            $studentMappedGrade = "";
        }

        //write grade row for students in user-properties file
        if($studentMappedGrade != ""){
          $csvRowStudentUserProperties = [
            $studentUniqueId,
            "resonant.grade",
            "",
            $studentMappedGrade,
            "",
            ""
          ];
          fputcsv($csvUserProperties, $csvRowStudentUserProperties);
        }

      }
      $stmtStudentUsers->close();

      //write teachers (staff members) to users file (if one is connected to a eligible student)
      $staffUniqueIdArray = array();
      $sqlStaffUsers = "SELECT DISTINCT abst.StaffID, abst.FirstName, abst.LastName, abst.EMail1, abrr.student_id, abrr.opt_out, abrr.start_time, abrr.end_time
              FROM abre_wellness_respondents abrr
              JOIN Abre_Staff abst
              ON abrr.email =  abst.EMail1
              AND abrr.site_id = abst.siteID
              WHERE abrr.role = 'staff'
              AND abst.is_archived = 0
              AND abst.school_year_id = ?
              AND abrr.site_id = ?";
      $stmtStaffUsers = $db->stmt_init();
      $stmtStaffUsers->prepare($sqlStaffUsers);
      $stmtStaffUsers->bind_param("ii", $currentSchoolYearID, $tenantSiteID);
      $stmtStaffUsers->execute();
      $stmtStaffUsers->store_result();
      $stmtStaffUsers->bind_result($staffId, $staffFirstName, $staffLastName, $staffEmail, $studentId, $staffAbreOptOutStatus, $effectiveStartTime, $effectiveEndTime);
      while($stmtStaffUsers->fetch()){

        //Check if eligible student
        if(in_array($studentId, $eligibleStudents)){

          $staffUniqueId = uniqueResonantId("staff", $tenantSiteID, $staffId);
          $studentUniqueId = uniqueResonantId("student", $tenantSiteID, $studentId);
          $effectiveStartTimeFormatted = ($effectiveStartTime != NULL) ? $effectiveStartTimeFormatted = date("Y-m-d", strtotime($effectiveStartTime)) : "";
          $effectiveEndTimeFormatted = ($effectiveEndTime != NULL) ? date("Y-m-d", strtotime($effectiveEndTime)) : "";

          //write teacher row to users file
          if(!in_array($staffUniqueId, $staffUniqueIdArray))
          {
            $csvRowStaffUsers = [
              $staffUniqueId,
              "",
              "0",
              $staffFirstName,
              "",
              $staffLastName,
              $staffEmail,
              "",
              "Teacher",
              $tenantSiteID,
              "Survey Respondent"
            ];
            fputcsv($csvUsers, $csvRowStaffUsers);
            array_push($staffUniqueIdArray, $staffUniqueId);
          }

          //write teacherid row for students in user-properties file
          $csvRowStudentUserProperties = [
            $studentUniqueId,
            "Teacher ID",
            "",
            $staffUniqueId,
            $effectiveStartTimeFormatted,
            $effectiveEndTimeFormatted
          ];
          fputcsv($csvUserProperties, $csvRowStudentUserProperties);

        }

      }
      $stmtStaffUsers->close();

      //write parents (family members) to users file (if one is connected to a eligible student)
      $parentUniqueIdArray = array();
      $parentEmailArray = array();
      if($resonantParents){
        $sqlParentUsers = "SELECT DISTINCT abpc.StudentID, abpc.FirstName, abpc.LastName,
                            CONCAT(SUBSTRING_INDEX(abpc.Email1,'@',1), '@', LOWER(SUBSTRING_INDEX(abpc.Email1,'@',-1))) AS Email1,
                            abrr.opt_out, abrr.start_time, abrr.end_time
                FROM abre_wellness_respondents abrr
                JOIN Abre_ParentContacts abpc
                ON abrr.email = abpc.EMail1
                AND abrr.site_id = abpc.siteID
                AND abrr.student_id = abpc.StudentID
                WHERE abrr.role = 'parent'
                AND abpc.school_year_id = ?
                AND abrr.site_id = ?
                GROUP BY abpc.StudentID";
        $stmtParentUsers = $db->stmt_init();
        $stmtParentUsers->prepare($sqlParentUsers);
        $stmtParentUsers->bind_param("ii", $currentSchoolYearID, $tenantSiteID);
        $stmtParentUsers->execute();
        $stmtParentUsers->store_result();
        $stmtParentUsers->bind_result($studentId, $parentFirstName, $parentLastName, $parentEmail, $parentAbreOptOutStatus, $effectiveStartTime, $effectiveEndTime);
        while($stmtParentUsers->fetch()){

          //Check if eligible student
          if(in_array($studentId, $eligibleStudents)){

            $parentUniqueId = uniqueResonantId("parent", $tenantSiteID, $parentEmail);
            $parentEmaillowercase = strtolower($parentEmail);
            $studentUniqueId = uniqueResonantId("student", $tenantSiteID, $studentId);
            $effectiveStartTimeFormatted = ($effectiveStartTime != NULL) ? $effectiveStartTimeFormatted = date("Y-m-d", strtotime($effectiveStartTime)) : "";
            $effectiveEndTimeFormatted = ($effectiveEndTime != NULL) ? date("Y-m-d", strtotime($effectiveEndTime)) : "";

            //write parent row to users file
            if(!in_array($parentUniqueId, $parentUniqueIdArray) && !in_array($parentEmaillowercase, $parentEmailArray))
            {
              $csvRowParentUsers = [
                $parentUniqueId,
                "",
                $parentAbreOptOutStatus,
                $parentFirstName,
                "",
                $parentLastName,
                $parentEmail,
                "",
                "Parent",
                $tenantSiteID,
                "Survey Respondent"
              ];
              fputcsv($csvUsers, $csvRowParentUsers);
              array_push($parentUniqueIdArray, $parentUniqueId);
              array_push($parentEmailArray, $parentEmaillowercase);
            }

            //write parentid row for students in user-properties file
            $csvRowStudentUserProperties = [
              $studentUniqueId,
              "Parent ID",
              "",
              $parentUniqueId,
              $effectiveStartTimeFormatted,
              $effectiveEndTimeFormatted
            ];
            fputcsv($csvUserProperties, $csvRowStudentUserProperties);

          }
        }

        $stmtParentUsers->close();
      }

    }
    $stmtOrganizations->close();

    $warnings = [];
    fclose($csvOrganizations);
    fclose($csvOrganizationProperties);
    fclose($csvUsers);
    fclose($csvUserProperties);
    uploadToAbreSftp($config->sftp, "organizations.csv", $fileOrganizations, $warnings);
    uploadToAbreSftp($config->sftp, "organization-properties.csv", $fileOrganizationProperties, $warnings);
    uploadToAbreSftp($config->sftp, "users.csv", $fileUsers, $warnings);
    uploadToAbreSftp($config->sftp, "user-properties.csv", $fileUserProperties, $warnings);

  } catch (Exception $ex) {
  	$error = $ex->getMessage();
  }

  $cronName = 'Resonant Education Export';
  $details = [];
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}
?>
