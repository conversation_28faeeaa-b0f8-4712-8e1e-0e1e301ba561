<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/
require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require(dirname(__FILE__) . '/../utils/VendorLink/vendorLinkApi.php');

function runJob($db, $siteID, $config){
  ignore_user_abort(true);
  set_time_limit(0);

  $cronName = 'Vendorlink Student Pictures';
    $uuid = Logger::logCronStart($db, $siteID, $cronName);

  try{

    $error = null;

    $rowsAdded = 0;
    $rowsUpdated = 0;

    if ($config->vendorlink->clientID == ""
        || $config->vendorlink->clientSecret == ""
        || $config->vendorlink->baseUri == ""
        || $config->vendorlink->subscriptionKey == ""){
      throw new Exception("VendorLink credentials missing.");
    }

    $currentSchoolYearID = getCurrentSchoolYearID($db);

    $vendorLinkApi = VendorLinkAPI::init($config->vendorlink->clientID,
                                         $config->vendorlink->clientSecret,
                                         $config->vendorlink->baseUri,
                                         $config->vendorlink->subscriptionKey);

  	//Get School Information from Database
  	$studentSql = "SELECT RefId, LocalId, Value IS NOT NULL AS existingValue
                   FROM Abre_VendorLink_SIS_Students student
                   LEFT JOIN Abre_VendorLink_SIS_StudentPictures picture
                    ON student.LocalId = picture.StudentID AND student.siteID = picture.siteID
                   WHERE student.siteID = $siteID AND student.school_year_id = $currentSchoolYearID";
  	$result = $db->query($studentSql);
  	while($row = $result->fetch_assoc()){
      $StudentRefId = trim($row['RefId']);
  		$StudentID = trim($row['LocalId']);
      $existingValue = $row['existingValue'];

  		//VendorLink Request
  		$json = $vendorLinkApi->get("SisService/StudentPicture",
                                  ["studentPersonalRefId" => $StudentRefId]);

      foreach($json['result'] as $element){
      	$SchoolYear = trim($element['SchoolYear']);
      	$OkToPublish = trim($element['OkToPublish']);
      	$Type = trim($element['PictureSource']['Type']);
      	$Value = trim($element['PictureSource']['Value']);

        //Start Downscale the image
        $image = imagecreatefromstring(base64_decode($Value));

        if ($image !== false) {
            // Get the current dimensions of the image
            $width = imagesx($image);
            $height = imagesy($image);

            // Calculate the new dimensions while maintaining aspect ratio
            $maxWidth = 500;
            $maxHeight = 500;

            if ($width > $maxWidth || $height > $maxHeight) {
                $aspectRatio = $width / $height;

                if ($width > $height) {
                    $newWidth = $maxWidth;
                    $newHeight = $maxWidth / $aspectRatio;
                } else {
                    $newHeight = $maxHeight;
                    $newWidth = $maxHeight * $aspectRatio;
                }
            } else {
                $newWidth = $width;
                $newHeight = $height;
            }

            // Create a new empty image with the new dimensions
            $newImage = imagecreatetruecolor($newWidth, $newHeight);

            // Resize and copy the original image to the new image
            imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

            // Output the new image as base64
            ob_start();
            imagejpeg($newImage);
            $newBase64Image = base64_encode(ob_get_clean());

            // Clean up resources
            imagedestroy($image);
            imagedestroy($newImage);

            // Set Image
            $Value = $newBase64Image;
        }
        //End Downscale the image

        if($existingValue){
          //Update Database
          $updateSql = "UPDATE Abre_VendorLink_SIS_StudentPictures SET SchoolYear = ?,
                        OkToPublish = ?, Type = ?, Value = ?
                        WHERE StudentID = ? AND siteID = ? AND school_year_id = ?";
          $updateStmt = $db->stmt_init();
          $updateStmt->prepare($updateSql);
          $updateStmt->bind_param("sssssii", $SchoolYear, $OkToPublish, $Type,
            $Value, $StudentID, $siteID, $currentSchoolYearID);
          $updateStmt->execute();
          $updateStmt->close();
          $rowsUpdated++;
        }else{
    			//Add to Data to Database
          $insertSql = "INSERT INTO Abre_VendorLink_SIS_StudentPictures (StudentID,
                        SchoolYear, OkToPublish, Type, Value, siteID, school_year_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
          $insertStmt = $db->stmt_init();
          $insertStmt->prepare($insertSql);
          $insertStmt->bind_param("sssssii", $StudentID, $SchoolYear,
            $OkToPublish, $Type, $Value, $siteID, $currentSchoolYearID);
          $insertStmt->execute();
          $insertStmt->close();
          $rowsAdded++;
        }
  		}
  	}
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = ["rows added" => $rowsAdded, "rows updated" => $rowsUpdated];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>