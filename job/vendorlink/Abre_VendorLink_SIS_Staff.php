<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
use Google\Cloud\Storage\StorageClient;

$cronName = 'Vendorlink Staff';
$uuid = Logger::logCronStart($db, $siteID, $cronName);

try{
  $error = NULL;

	//Get School Information from Database
	$schoolSql = "SELECT RefId, LocalId, SchoolName
                FROM Abre_VendorLink_SIS_Schools
                WHERE siteID = ? AND school_year_id = ?";
  $schoolStmt = $db->stmt_init();
  $schoolStmt->prepare($schoolSql);
  $schoolStmt->bind_param("ii", $siteID, $currentSchoolYearID);
  $schoolStmt->execute();
  $schoolStmt->store_result();
  $schoolStmt->bind_result($schoolRefId, $schoolLocalId, $schoolName);
  if(!$schoolStmt->num_rows){
    throw new Exception("No schools found for district");
  }

  $insertSql = "INSERT INTO Abre_VendorLink_SIS_Staff (RefId, LocalId, StateProvinceId,
                LastName, FirstName, MiddleName, EmailList, SchoolCode, SchoolName, siteID,
                school_year_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
  $insertStmt = $db->stmt_init();
  $insertStmt->prepare($insertSql);

  $oldRowsRemoved = false;

    // Initialize an array to hold all elements for bucket upload
    $mergedResults = [];

  while($schoolStmt->fetch()){
    //VendorLink Request
    $json = $vendorLinkApi->get("SisService/Staff",
                                ["leaOrSchoolInfoRefId" => $schoolRefId]);

    if(isset($json['result'])){
      if(!$oldRowsRemoved){
        $deleteSql = "DELETE FROM Abre_VendorLink_SIS_Staff
                      WHERE siteID = ? AND school_year_id = ?";
        $deleteStmt = $db->stmt_init();
        $deleteStmt->prepare($deleteSql);
        $deleteStmt->bind_param("ii", $siteID, $currentSchoolYearID);
        $deleteStmt->execute();
        $deleteStmt->close();

        $oldRowsRemoved = true;
      }

      foreach($json['result'] as $element){
      	$refId = trim($element['RefId']);
      	$localId = trim($element['LocalId']) ?? "";
      	$stateProvinceId = trim($element['StateProvinceId']) ?? "";
      	$lastName = trim($element['Name']['LastName']) ?? "";
      	$firstName = trim($element['Name']['FirstName']) ?? "";
      	$middleName = trim($element['Name']['MiddleName']) ?? "";

        $emailListArray = $element['EmailList'];
  			$emailList = json_encode($emailListArray);

  			//Add to Data to Database
  			if($refId != NULL){
  				$insertStmt->bind_param("sssssssssii", $refId, $localId, $stateProvinceId, $lastName,
            $firstName, $middleName, $emailList, $schoolLocalId,
            $schoolName, $siteID, $currentSchoolYearID);
  				$insertStmt->execute();
  			}
  		}

        // Build single JSON file and upload results to Google Cloud Storage
        foreach($json['result'] as $element){
            $mergedResults[] = $element;
        }
    }
  }
  $insertStmt->close();
  $schoolStmt->close();

    // Bucket Upoad Code Starts Here
    // Encode the merged results as a JSON string
    $jsonEncoded = json_encode($mergedResults);

    // Create a temporary file
    $tempFile = tmpfile();

    // Write the JSON data to the temporary file
    fwrite($tempFile, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile);

    // Upload the JSON file to the Google Cloud Storage bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Vendorlink_SIS_Staff.json";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);

    // Upload to the first folder location
    $bucket->upload($tempFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile);

    // Create another temporary file for the second upload
    $tempFile2 = tmpfile();

    // Write the same JSON data to the second temporary file
    fwrite($tempFile2, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile2);

    // Upload to the other folder location in the bucket
    $fileExtension = "json";
    $folderName = "Abre_Vendorlink_SIS_Staff";
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;

    $bucket->upload($tempFile2, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile2);
    // Bucket Upload Code Ends Here
    
}catch(Exception $ex){
  $error = $ex->getMessage();
}

$details = [];
if(isset($error) && !is_null($error)){
  $details["error"] = $error;
  $success = CRON_FAILURE;
}else{
  $success = CRON_SUCCESS;
}

Logger::logCronFinish($db, $siteID, $cronName, $success, $details, $uuid);

?>
