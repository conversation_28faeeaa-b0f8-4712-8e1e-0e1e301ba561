<?php

require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
use Google\Cloud\Storage\StorageClient;

$cronName = 'Vendorlink District';
$uuid = Logger::logCronStart($db, $siteID, $cronName);

try {
    $error = NULL;

	$json = $vendorLinkApi->get("SisService/District");

    // Source to landing zone code update
    // Re-encode the JSON result to remove the outer "result" key
    $jsonEncoded = json_encode($json["result"]);

    // Create a temporary file
    $tempFile = tmpfile();

    // Write the JSON data to the temporary file
    fwrite($tempFile, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile);

    // Pass the temporary file resource to upload function
    // Upload SFTP File to Google Cloud Storage Bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Vendorlink_SIS_District.json";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($tempFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Close and delete the temporary file when done
    fclose($tempFile);

    // Create a temporary file (note: had to create a second temp file as first one was not rewinding correctly after upload)
    $tempFile2 = tmpfile();

    // Write the JSON data to the temporary file
    fwrite($tempFile2, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile2);

    // Upload to the other folder location in landing zone bucket
    $fileExtension = "json";
    $folderName = "Abre_Vendorlink_SIS_District";
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($tempFile2, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    // Close and delete the temporary file when done
    fclose($tempFile2);

    // Import data into the database
    if(isset($json["result"])) {
        $districtInfo = $json["result"][0];

        $refId = trim($districtInfo['RefId']);
        $localId = trim($districtInfo['LocalId']);
        $stateProvinceId = trim($districtInfo['StateProvinceId']);
        $leaName = trim($districtInfo['LeaName']);

        $deleteSql = "DELETE FROM Abre_VendorLink_SIS_District
                    WHERE siteID = ? AND school_year_id = ?";
        $deleteStmt = $db->stmt_init();
        $deleteStmt->prepare($deleteSql);
        $deleteStmt->bind_param("ii", $siteID, $currentSchoolYearID);
        $deleteStmt->execute();
        $deleteStmt->close();

        $insertSql = "INSERT INTO Abre_VendorLink_SIS_District
                    (RefId, LocalId, StateProvinceId, LeaName, siteID, school_year_id)
                    VALUES (?, ?, ?, ?, ?, ?)";
        $insertStmt = $db->stmt_init();
        $insertStmt->prepare($insertSql);
        $insertStmt->bind_param("ssssii", $refId, $localId, $stateProvinceId, $leaName, $siteID,
        $currentSchoolYearID);
        $insertStmt->execute();
        $insertStmt->close();
    }
} catch(Exception $ex) {
    $error = $ex->getMessage();
}

    $details = [];
    if(isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $success = CRON_FAILURE;
    } else {
        $success = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $success, $details, $uuid);

?>
