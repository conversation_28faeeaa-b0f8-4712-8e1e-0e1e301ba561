<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
use Google\Cloud\Storage\StorageClient;

$cronName = 'Vendorlink Discipline';
$uuid = Logger::logCronStart($db, $siteID, $cronName);

try{
  $error = NULL;

	//Get School Information from Database
	$schoolSql = "SELECT RefId
                  FROM Abre_VendorLink_SIS_District vd
                  WHERE vd.siteID = ? AND vd.school_year_id = ?";
  $schoolStmt = $db->stmt_init();
  $schoolStmt->prepare($schoolSql);
  $schoolStmt->bind_param("ii", $siteID, $currentSchoolYearID);
  $schoolStmt->execute();
  $schoolStmt->store_result();
  $schoolStmt->bind_result($districtRefId);
  if(!$schoolStmt->num_rows){
    throw new Exception("No district found");
  }

  $insertSql = "INSERT INTO Abre_VendorLink_SIS_Discipline
                (
                  RefId, SchoolInfoRefId, SchoolInfoRefIdPlain, SchoolYear, IncidentNumber, IncidentName, Description,
                  IncidentDateTime, LEAInfoRefId, IncidentBuildingIRN, IncidentAgainstPropertyInd, DisciplineIncidentPlaceCodeId,
                  DisciplineIncidentTimeFrameCodeId, FacilitiesCodeId, IncidentStatusTypeCodeId, IncidentComment,
                  IncidentReferrer, IncidentCategories, Offenders, DisciplinePersonRefId, DisciplinePersonRefIdPlain, Victims, Witnesses,
                  siteID, school_year_id
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
  $insertStmt = $db->stmt_init();
  $insertStmt->prepare($insertSql);

    // Initialize an array to hold all elements for bucket upload
    $mergedResults = [];

	while($schoolStmt->fetch()){

      $page = 1;
      $limit = 1000;
      $deleteSchool = 1;
    
      do{ 

          $json = $vendorLinkApi->get("SisService/DisciplineIncident", [
            "leaOrSchoolInfoRefId" => $districtRefId,
            "page" => $page,
            "pagesize" => $limit
          ]);

          //Delete existing records for the district
          if($deleteSchool == 1){
            $deleteSql = "DELETE FROM Abre_VendorLink_SIS_Discipline
              WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $currentSchoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();
          }

          if(isset($json['count']) && $json['count'] > 0){
          
            foreach($json['result'] as $element){

              $refId = trim($element['RefId']);
              $schoolInfoRefId = trim($element['SchoolInfoRefId']) ?? "";
              $schoolInfoRefIdPlain = str_replace("-", "", $schoolInfoRefId);
              $schoolYear = trim($element['SchoolYear']) ?? "";
              $incidentNumber = trim($element['IncidentNumber']) ?? "";
              $incidentName = trim($element['IncidentName']) ?? "";
              $description = trim($element['Description']) ?? "";
              $incidentDateTime = trim($element['IncidentDateTime']) ?? "";
              $LEAInfoRefId = trim($element['LEAInfoRefId']) ?? "";
              $incidentBuildingIRN = trim($element['IncidentBuildingIRN']) ?? "";
              $incidentAgainstPropertyInd = trim($element['IncidentAgainstPropertyInd']) ?? "";
              $disciplineIncidentPlaceCodeId = trim($element['DisciplineIncidentPlaceCodeId']) ?? "";
              $disciplineIncidentTimeFrameCodeId = trim($element['DisciplineIncidentTimeFrameCodeId']) ?? "";
              $facilitiesCodeId = trim($element['FacilitiesCodeId']) ?? "";
              $incidentStatusTypeCodeId = trim($element['IncidentStatusTypeCodeId']) ?? "";
              $incidentComment = trim($element['IncidentComment']) ?? "";

              $incidentReferrerArray = $element['IncidentReferrer'];
              $incidentReferrer = json_encode($incidentReferrerArray);

              $incidentCategoriesArray = $element['IncidentCategories'];
              $incidentCategories = json_encode($incidentCategoriesArray);

              $offendersArray = $element['Offenders'];
              $offenders = json_encode($offendersArray);

              if(isset($offendersArray[0]['DisciplinePersonRefId'])){
                $disciplinePersonRefId = $offendersArray[0]['DisciplinePersonRefId'];
              } else {
                $disciplinePersonRefId = "";
              }
              $disciplinePersonRefIdPlain = str_replace("-", "", $disciplinePersonRefId);

              $victimsArray = $element['Victims'];
              $victims = json_encode($victimsArray);

              $witnessesArray = $element['Witnesses'];
              $witnesses = json_encode($witnessesArray);

              //Add to Data to Database
              if($refId != NULL){
                $insertStmt->bind_param("sssssssssssssssssssssssii", $refId, $schoolInfoRefId, $schoolInfoRefIdPlain, $schoolYear, $incidentNumber,
                                        $incidentName, $description, $incidentDateTime,
                                        $LEAInfoRefId, $incidentBuildingIRN, $incidentAgainstPropertyInd, $disciplineIncidentPlaceCodeId,
                                        $disciplineIncidentTimeFrameCodeId, $facilitiesCodeId, $incidentStatusTypeCodeId, $incidentComment,
                                        $incidentReferrer, $incidentCategories, $offenders, $disciplinePersonRefId, $disciplinePersonRefIdPlain, $victims, $witnesses,
                                        $siteID, $currentSchoolYearID);
                $insertStmt->execute();
              }
            }

            // Build single JSON file and upload results to Google Cloud Storage
            foreach($json['result'] as $element){
                $mergedResults[] = $element;
            }
          }

          $deleteSchool = 0;
          $page++;

        }while(isset($json['result']) && count($json['result']) && ($json['count'] >= $limit));


	}
  $insertStmt->close();
  $schoolStmt->close();

    // Bucket Upoad Code Starts Here
    // Encode the merged results as a JSON string
    $jsonEncoded = json_encode($mergedResults);

    // Create a temporary file
    $tempFile = tmpfile();

    // Write the JSON data to the temporary file
    fwrite($tempFile, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile);

    // Upload the JSON file to the Google Cloud Storage bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Vendorlink_SIS_Discipline.json";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);

    // Upload to the first folder location
    $bucket->upload($tempFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile);

    // Create another temporary file for the second upload
    $tempFile2 = tmpfile();

    // Write the same JSON data to the second temporary file
    fwrite($tempFile2, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile2);

    // Upload to the other folder location in the bucket
    $fileExtension = "json";
    $folderName = "Abre_Vendorlink_SIS_Discipline";
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;

    $bucket->upload($tempFile2, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile2);
    // Bucket Upload Code Ends Here
}catch(Exception $ex){
  $error = $ex->getMessage();
}

$details = [];
if(isset($error) && !is_null($error)){
  $details["error"] = $error;
  $success = CRON_FAILURE;
}else{
  $success = CRON_SUCCESS;
}

Logger::logCronFinish($db, $siteID, $cronName, $success, $details, $uuid);

?>
