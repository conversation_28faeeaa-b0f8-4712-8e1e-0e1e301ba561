<?php

require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
use Google\Cloud\Storage\StorageClient;

$cronName = 'Vendorlink Schools';
$uuid = Logger::logCronStart($db, $siteID, $cronName);

try{
  $error = NULL;

	//Get District Information from Database
	$districtSql = "SELECT RefId
                  FROM Abre_VendorLink_SIS_District
                  WHERE siteID = ? AND school_year_id = ?";
  $districtStmt = $db->stmt_init();
  $districtStmt->prepare($districtSql);
  $districtStmt->bind_param("ii", $siteID, $currentSchoolYearID);
  $districtStmt->execute();
  $districtStmt->bind_result($districtRefId);
  if(!$districtStmt->fetch()){
    throw new Exception("District RefId was not found");
  };
  $districtStmt->close();

	//VendorLink Request
	$json = $vendorLinkApi->get("SisService/SchoolInfo",
                              ["leaOrSchoolInfoRefId" => $districtRefId]);

  if(isset($json['result'])){
    $deleteSql = "DELETE FROM Abre_VendorLink_SIS_Schools
                  WHERE siteID = ? AND school_year_id = ?";
    $deleteStmt = $db->stmt_init();
    $deleteStmt->prepare($deleteSql);
    $deleteStmt->bind_param("ii", $siteID, $currentSchoolYearID);
    $deleteStmt->execute();
    $deleteStmt->close();

    $insertSql = "INSERT INTO Abre_VendorLink_SIS_Schools
                   (
                     RefId, LocalId, StateProvinceId, SchoolName, LeaInfoRefId,
                     PrincipalContactName, Street, City, State, Country, PostalCode, siteID,
                     school_year_id
                   )
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $insertStmt = $db->stmt_init();
    $insertStmt->prepare($insertSql);

    foreach($json['result'] as $element){
      $schoolRefId = trim($element['RefId']);
      $localId = trim($element['LocalId']);
      $stateProvinceId = trim($element['StateProvinceId']);
      $schoolName = trim($element['SchoolName']);
      $leaInfoRefId = trim($element['LeaInfoRefId']);
      $principalContactName = trim($element['PrincipalInfo']['ContactName']);
      $street = trim($element['Address'][0]['Street']['Line1']);
      $city = trim($element['Address'][0]['City']);
      $state = trim($element['Address'][0]['StateProvince']);
      $country = trim($element['Address'][0]['Country']);
      $postalCode = trim($element['Address'][0]['PostalCode']);

      //Add to Data to Database
      if($schoolRefId != NULL){
        $insertStmt->bind_param("sssssssssssii", $schoolRefId, $localId, $stateProvinceId,
          $schoolName, $leaInfoRefId, $principalContactName,
          $street, $city, $state, $country, $postalCode, $siteID, $currentSchoolYearID);
        $insertStmt->execute();
      }
    }
    $insertStmt->close();

    // Upload to bucket code starts here
    // Initialize an array to hold all elements
    $mergedResults = [];

    // Build single JSON file and upload results to Google Cloud Storage
    foreach($json['result'] as $element){

        $mergedResults[] = $element;

    }

    // Encode the merged results as a JSON string
    $jsonEncoded = json_encode($mergedResults);

    // Create a temporary file
    $tempFile = tmpfile();

    // Write the JSON data to the temporary file
    fwrite($tempFile, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile);

    // Upload the JSON file to the Google Cloud Storage bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Vendorlink_SIS_Schools.json";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);

    // Upload to the first folder location
    $bucket->upload($tempFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile);

    // Create another temporary file for the second upload
    $tempFile2 = tmpfile();

    // Write the same JSON data to the second temporary file
    fwrite($tempFile2, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile2);

    // Upload to the other folder location in the bucket
    $fileExtension = "json";
    $folderName = "Abre_Vendorlink_SIS_Schools";
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;

    $bucket->upload($tempFile2, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile2);
    // Upload to bucket code ends here

    }
}catch(Exception $ex){
  $error = $ex->getMessage();
}

$details = [];
if(isset($error) && !is_null($error)){
  $details["error"] = $error;
  $success = CRON_FAILURE;
}else{
  $success = CRON_SUCCESS;
}

Logger::logCronFinish($db, $siteID, $cronName, $success, $details, $uuid);

?>
