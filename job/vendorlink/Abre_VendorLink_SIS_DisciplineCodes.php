<?php

require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
use Google\Cloud\Storage\StorageClient;

$cronName = 'Vendorlink Discipline Codes';
$uuid = Logger::logCronStart($db, $siteID, $cronName);
try{
  $error = NULL;

	//Get School Information from Database
	$schoolSql = "SELECT RefId
                FROM Abre_VendorLink_SIS_Schools
                WHERE siteID = ? AND school_year_id = ?";
  $schoolStmt = $db->stmt_init();
  $schoolStmt->prepare($schoolSql);
  $schoolStmt->bind_param("ii", $siteID, $currentSchoolYearID);
  $schoolStmt->execute();
  $schoolStmt->store_result();
  $schoolStmt->bind_result($schoolRefId);
  if(!$schoolStmt->num_rows){
    throw new Exception("No schools found for district");
  }

  $insertSql = "INSERT INTO Abre_VendorLink_SIS_DisciplineCodes
                (SchoolId, SchoolYear, CodeTypes, EMISCodeTypes, siteID, school_year_id)
                VALUES (?, ?, ?, ?, ?, ?)";
  $insertStmt = $db->stmt_init();
  $insertStmt->prepare($insertSql);

  $oldRowsRemoved = false;

    // Initialize an array to hold all elements for bucket upload
    $mergedResults = [];

	while($schoolStmt->fetch()){
		//VendorLink Request
		$json = $vendorLinkApi->get("SisService/DisciplineCodes",
                                ["leaOrSchoolInfoRefId" => $schoolRefId]);

        if(isset($json['result'])){
        if(!$oldRowsRemoved){
            $deleteSql = "DELETE FROM Abre_VendorLink_SIS_DisciplineCodes
                        WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $currentSchoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $oldRowsRemoved = true;
        }
            //Insert Returned Data
            foreach($json['result'] as $element){
                    //Get Returned Data
                    $schoolId = trim($element['SchoolId']);
                    $schoolId = str_replace(["-", "–"], '', $schoolId);
                    $schoolYear = trim($element['SchoolYear']);

                    $codeTypesArray = $element['CodeTypes'];
                    $codeTypes = json_encode($codeTypesArray);

                    $emisCodeTypesArray = $element['EMISCodeTypes'];
                    $emisCodeTypes = json_encode($emisCodeTypesArray);

                    //Add to Data to Database
                    $insertStmt->bind_param("ssssii", $schoolId, $schoolYear, $codeTypes,
            $emisCodeTypes, $siteID, $currentSchoolYearID);
                    $insertStmt->execute();
                }
        }

        // Build single JSON file and upload results to Google Cloud Storage
        foreach($json['result'] as $element){
            $mergedResults[] = $element;
        }
	}
  $insertStmt->close();
  $schoolStmt->close();

    // Bucket Upoad Code Starts Here
    // Encode the merged results as a JSON string
    $jsonEncoded = json_encode($mergedResults);

    // Create a temporary file
    $tempFile = tmpfile();

    // Write the JSON data to the temporary file
    fwrite($tempFile, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile);

    // Upload the JSON file to the Google Cloud Storage bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Vendorlink_SIS_DisciplineCodes.json";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);

    // Upload to the first folder location
    $bucket->upload($tempFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile);

    // Create another temporary file for the second upload
    $tempFile2 = tmpfile();

    // Write the same JSON data to the second temporary file
    fwrite($tempFile2, $jsonEncoded);

    // Rewind the file pointer to the beginning
    rewind($tempFile2);

    // Upload to the other folder location in the bucket
    $fileExtension = "json";
    $folderName = "Abre_Vendorlink_SIS_DisciplineCodes";
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;

    $bucket->upload($tempFile2, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    // Close and delete the temporary file after upload
    fclose($tempFile2);
    // Bucket Upload Code Ends Here

}catch(Exception $ex){
  $error = $ex->getMessage();
}

$details = [];
if(isset($error) && !is_null($error)){
  $details["error"] = $error;
  $success = CRON_FAILURE;
}else{
  $success = CRON_SUCCESS;
}

Logger::logCronFinish($db, $siteID, $cronName, $success, $details, $uuid);

?>
