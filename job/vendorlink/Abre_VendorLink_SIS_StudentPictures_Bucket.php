<?php

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require(dirname(__FILE__) . '/../utils/VendorLink/vendorLinkApi.php');

use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config){
    ignore_user_abort(true);
    set_time_limit(0);

    $cronName = 'Vendorlink Student Pictures Bucket';
    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    $counter = 0;

    try{

    $error = null;

    if ($config->vendorlink->clientID == ""
        || $config->vendorlink->clientSecret == ""
        || $config->vendorlink->baseUri == ""
        || $config->vendorlink->subscriptionKey == ""){
      throw new Exception("VendorLink credentials missing.");
    }

    $currentSchoolYearID = getCurrentSchoolYearID($db);

    $vendorLinkApi = VendorLinkAPI::init($config->vendorlink->clientID,
                                         $config->vendorlink->clientSecret,
                                         $config->vendorlink->baseUri,
                                         $config->vendorlink->subscriptionKey);

    $studentSql = "SELECT RefId, LocalId
                    FROM Abre_VendorLink_SIS_Students student
                    WHERE student.siteID = $siteID AND student.school_year_id = $currentSchoolYearID";
  	$result = $db->query($studentSql);
  	while($row = $result->fetch_assoc()){
      $StudentRefId = trim($row['RefId']);
  		$StudentID = trim($row['LocalId']);

  		//VendorLink Request
  		$json = $vendorLinkApi->get("SisService/StudentPicture",
                                  ["studentPersonalRefId" => $StudentRefId]);

      foreach($json['result'] as $element){
      	$Value = trim($element['PictureSource']['Value']);

        // Validate Base64
        if (!base64_decode($Value, true)) {
            // Invalid Base64 - add log message
            continue;
        }

        // Decode and create image resource
        $image = imagecreatefromstring(base64_decode($Value));
        if ($image === false) {
            // TO DO: Invalid Image - add log message
            continue;
        }

        if ($image !== false) {
            // Get the current dimensions of the image
            $width = imagesx($image);
            $height = imagesy($image);

            // Calculate the new dimensions while maintaining aspect ratio
            $maxWidth = 500;
            $maxHeight = 500;

            if ($width > $maxWidth || $height > $maxHeight) {
                $aspectRatio = $width / $height;

                if ($width > $height) {
                    $newWidth = $maxWidth;
                    $newHeight = $maxWidth / $aspectRatio;
                } else {
                    $newHeight = $maxHeight;
                    $newWidth = $maxHeight * $aspectRatio;
                }
            } else {
                $newWidth = $width;
                $newHeight = $height;
            }

            // Create a new empty image with the new dimensions
            $newImage = imagecreatetruecolor($newWidth, $newHeight);

            // Resize and copy the original image to the new image
            imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

            // Save the resized image to a temporary file
            $tempFilePath = sys_get_temp_dir() . "/$StudentID.jpg";
            if (!imagejpeg($newImage, $tempFilePath)) {
                //Logger::logError("Failed to save JPEG for student ID $StudentID.");
                imagedestroy($image);
                imagedestroy($newImage);
                continue;
            }

            // Clean up resources
            imagedestroy($image);
            imagedestroy($newImage);

            // Upload the file to Google Cloud Storage
            try {
                $storage = new StorageClient(['projectId' => "abre-production"]);
                $bucket = $storage->bucket("abre-production");
                $bucket->upload(fopen($tempFilePath, 'r'), [
                    'name' => "private_html/students/images/$siteID/$StudentID.jpg"
                ]);

            } catch (Exception $e) {
                // TO DO: Add logging
            } finally {
                // Delete the temporary file
                unlink($tempFilePath);
            }

        }

  		}
  	}
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>