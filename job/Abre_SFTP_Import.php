<?php
/*
* Copyright 2016-2023 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../vendor/autoload.php');
require_once(dirname(__FILE__) . '/utils/functions.php');
require_once(dirname(__FILE__) . '/utils/logging.php');

use phpseclib\Net\SFTP;

function runJob($db, $file, $siteID, $config)
{

    try {

        //Match Headers and Mapping Configuration Function
        function matchAndCreateArray($headers, $mapping)
        {
            $resultArray = array();

            foreach ($mapping as $key => $value) {
                $columnPosition = array_search($value['column'], $headers);

                if ($columnPosition !== false) {
                    $resultArray[$key] = array(
                        "column" => $value['column'],
                        "position" => $columnPosition,
                        "default" => isset($value['default']) ? $value['default'] : null
                    );
                } elseif (isset($value['default'])) {
                    $resultArray[$key] = array(
                        "column" => $value['column'],
                        "position" => null,
                        "default" => $value['default']
                    );
                }
            }
            return $resultArray;
        }

        //Convert Date to YYYY-MM-DD (if a date) Function
        function convertToYYYYMMDD($date)
        {
            $dateTime = DateTime::createFromFormat('m/d/Y', $date);

            if ($dateTime !== false) {
                // Format the date as YYYY-MM-DD
                return $dateTime->format('Y-m-d');
            } else {
                // If not a valid date, return as text
                return $date;
            }
        }

        //Get Column Names
        function getColumnNames($array)
        {
            $columnNames = "";
            foreach ($array as $variableName => $info) {
                $columnNames .= $variableName . ', ';
            }
            $columnNames = rtrim($columnNames, ', ');
            return $columnNames;
        }

        //Get Job Mapping Configuration
        $queryMapping = "SELECT job_file, mapping FROM job_mapping WHERE job_file = '$file' LIMIT 1";
        $resultMapping = $db->query($queryMapping);
        if ($resultMapping) {

            while ($row = $resultMapping->fetch_assoc()) {
                $rawMapping = $row['mapping'];
                $rawMapping = json_decode($rawMapping);

                //Add SiteID Value to Mapping
                if (isset($rawMapping->mapping->site_id)) {
                    $rawMapping->mapping->site_id->default = $siteID;
                }

                //Get Cron Values
                $cronType = $rawMapping->cronType;
                $cronName = $rawMapping->cronName;
                $fileName = $rawMapping->fileName;
                $safeColumnCount = $rawMapping->safeColumnCount;
                $importLimit = $rawMapping->importLimit;
                $dbTableName = $rawMapping->dbTableName;
                $siteIdDelete = $rawMapping->siteIdDelete;
                $tableHeader = $rawMapping->tableHeader;
                $removeCharacter = isset($rawMapping->removeCharacter) ? $rawMapping->removeCharacter : "";

                //Get Cron Mappings
                $rawMappingvalue = $rawMapping->mapping;
                $mapping = array();
                foreach ($rawMappingvalue as $key => $value) {
                    $mapping[$key] = (array) $value;
                }
            }
        }

        //Get Any Custom Mapping Configurations (If Exist)
        $queryCustomConfig = "SELECT j.custom_config FROM job_definition jd LEFT JOIN job j ON jd.id = j.job_definition_id WHERE target = '$siteID' AND jd.job_file = '$file' LIMIT 1";
        $resultCustomConfig = $db->query($queryCustomConfig);
        if ($resultCustomConfig) {

            while ($row = $resultCustomConfig->fetch_assoc()) {

                $custom_config = $row['custom_config'];
                $dataCustomConfig = json_decode($custom_config, true);

                //Check if Custom FileName Exists
                if (isset($dataCustomConfig['fileName'])) {
                    $fileName = $dataCustomConfig['fileName'];
                }
            }
        }

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        //Define Constants & Variables
        $currentSchoolYearID = getCurrentSchoolYearID($db);
        define("SAFE_COLUMN_COUNT", $safeColumnCount);
        define("MAX_IMPORT_LIMIT", $importLimit);
        $error = null;
        $skip = null;
        $separator = "\r\n";

        //Connect to SFTP
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.");
        }

        //Find File
        $cronFile = $sftp->get("$fileName.txt");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileName.csv");
            $fileType = "csv";
            $columnSeparator = ",";
        } else {
            $cronFile = $sftp->get("$fileName.txt");
            $fileType = "txt";
            $columnSeparator = "\t";
        }

        if (!$cronFile) {
            $skip = true;
            throw new Exception("No file found.");
        } else {
            $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
            if ($fileDetails["isEmpty"]) {
                $skip = true;
                throw new Exception("File is empty.");
            } elseif ($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]) {
                $skip = true;
                throw new Exception("File only contains a header row.");
            } elseif (!$fileDetails["hasValidDataRow"]) {
                throw new Exception("No valid data row found.");
            }
        }

        //Create Temporary Table
        $dbTableNameTemp = $dbTableName . '_' . random_int(1000000000, 9999999999);
        $db->query("CREATE TABLE $dbTableNameTemp LIKE $dbTableName;");

        //Remove the Temporary Table Indexes
        $query = "SELECT index_name FROM information_schema.statistics WHERE table_name = '$dbTableNameTemp'";
        $result = $db->query($query);
        if ($result) {
            // Loop through the results and generate DROP INDEX statements
            while ($row = $result->fetch_assoc()) {
                $indexName = $row['index_name'];
                $dropQuery = "ALTER TABLE $dbTableNameTemp DROP INDEX $indexName";
                $db->query($dropQuery);
            }

            // Free the Result
            $result->free();
        }

        //Delete Command (Optional)
        if ($siteIdDelete != "") {

            $db->query("DELETE FROM $dbTableName WHERE $siteIdDelete = $siteID AND school_year_id = $currentSchoolYearID");
        }

        if ($cronType == "Header") {

            //Get the Column Header
            $header = str_getcsv(strtok($cronFile, $separator), $columnSeparator);

            //Match the File Header and Mapping
            $matchedArray = matchAndCreateArray($header, $mapping);

            //Create Insert Syntax
            $rowCounter = 0;
            $valuesToImport = [];
            $dbColumns = "INSERT INTO $dbTableNameTemp (";
            foreach ($matchedArray as $variableName => $info) {
                $column = $info["column"];
                $dbColumns .= $variableName . ', ';
            }
            $dbColumns = rtrim($dbColumns, ', ');
            $dbColumns .= ") VALUES ";

            //MySQL 8 does not allow the column name "grouping" without backticks
            $cleanColumns = str_replace(" grouping,", " `grouping`,", $dbColumns);

            //Import Rows
            $line = strtok($cronFile, $separator);

            //Skip Header Row (Required for Header Matching)
            $line = strtok($separator);
            do {

                //Split Columns
                if ($fileType == "txt") {
                    $data = str_getcsv($line, $columnSeparator);
                } else {
                    $data = str_getcsv($line, $columnSeparator);
                }

                if (count($data) >= SAFE_COLUMN_COUNT) {
                    $rowCounter++;

                    //Loop Through to Get Columns and Position
                    foreach ($matchedArray as $key => $value) {
                        if (isset($value['position']) && $value['position'] !== null) {

                            //Handle Dates if Detected
                            $convertedValue = convertToYYYYMMDD($data[$value['position']]);

                            ${$key} = trim($db->escape_string($convertedValue));
                        } elseif (isset($value['default'])) {
                            ${$key} = $value['default'];
                        } else {
                            // Handle the case where both position and default are not set.
                            ${$key} = null;
                        }
                    }

                    //Loop Through to Get Insert
                    $values = array();
                    foreach ($matchedArray as $key => $value) {
                        $values[] = "'${$key}'";
                    }
                    $valuesToImport[] = '(' . implode(', ', $values) . ')';

                    //Insert if Max Hit
                    if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                        insertRows($db, $cleanColumns, $valuesToImport);
                        $valuesToImport = [];
                    }
                }

                $line = strtok($separator);
            } while ($line !== false);

            if (count($valuesToImport)) {
                insertRows($db, $cleanColumns, $valuesToImport);
            }

            //Get Columns Names
            $columnNames = getColumnNames($matchedArray);

            //Move Data to Real Table
            $selectQuery = "SELECT * FROM $dbTableNameTemp";
            $result = $db->query($selectQuery);
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    // Construct the INSERT query for each row
                    $columnNames = implode(', ', array_keys($row));

                    //MySQL 8 does not allow the column name "grouping" without backticks
                    $cleanedColumns = str_replace(" grouping,", " `grouping`,", $columnNames);

                    $columnValues = implode("', '", array_values($row));
                    $insertQuery = "INSERT INTO $dbTableName ($cleanedColumns) VALUES ('$columnValues')";

                    // Execute the INSERT query for each row
                    $insertResult = $db->query($insertQuery);
                }
            }
        } elseif ($cronType == "Column") {

            //Create Insert Syntax
            $rowCounter = 0;
            $valuesToImport = [];
            $dbColumns = "INSERT INTO $dbTableNameTemp (";
            foreach ($mapping as $variableName => $info) {
                $dbColumns .= $variableName . ', ';
            }
            $dbColumns = rtrim($dbColumns, ', ');
            $dbColumns .= ") VALUES ";

            //MySQL 8 does not allow the column name "grouping" without backticks
            $cleanColumns = str_replace(" grouping,", " `grouping`,", $dbColumns);

            //Import Rows
            $line = strtok($cronFile, $separator);

            //Optional Skip Header Row
            if ($tableHeader == "yes") {
                $line = strtok($separator);
            }

            do {

                //Split Columns
                if ($fileType == "txt") {
                    $data = str_getcsv($line, $columnSeparator);
                } else {
                    $data = str_getcsv($line, $columnSeparator);
                }

                //Remove double quotes (") from all fields in the $data array
                if ($removeCharacter !== "") {
                    if ($removeCharacter == '\"') {
                        $removeCharacter = '"';
                    }
                    $data = array_map(function ($field) use ($removeCharacter) {
                        return str_replace($removeCharacter, '', $field);
                    }, $data);
                }

                if (count($data) >= SAFE_COLUMN_COUNT) {

                    $rowCounter++;

                    //Look At Mapping
                    foreach ($mapping as $key => $value) {
                        if (isset($value['position'])) {
                            ${$key} = trim($db->escape_string(mysqli_real_escape_string($db, $data[$value['position']])));
                        } else {
                            if ($key == "siteID" or $key == "site_id") {
                                ${$key} = trim($db->escape_string($siteID));
                            }
                            if ($key == "school_year_id") {
                                ${$key} = trim($db->escape_string($currentSchoolYearID));
                            }
                        }
                    }

                    //Loop Through to Get Insert
                    $values = array();
                    foreach ($mapping as $key => $value) {
                        if (isset($value['position']) or ($key == "siteID" or $key == "site_id" or $key == "school_year_id")) {
                            $values[] = "'${$key}'";
                        }
                    }
                    $valuesToImport[] = '(' . implode(', ', $values) . ')';

                    //Insert if Max Hit
                    if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                        insertRows($db, $cleanColumns, $valuesToImport);
                        $valuesToImport = [];
                    }
                }

                $line = strtok($separator);
            } while ($line !== false);

            if (count($valuesToImport)) {
                insertRows($db, $cleanColumns, $valuesToImport);
            }

            //Get Columns Names
            $columnNames = getColumnNames($mapping);

            //Move Data to Real Table
            $selectQuery = "SELECT * FROM $dbTableNameTemp";
            $result = $db->query($selectQuery);

            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    // Construct the INSERT query for each row
                    $columnNames = implode(', ', array_keys($row));

                    //MySQL 8 does not allow the column name "grouping" without backticks
                    $cleanColumnNames = str_replace(" grouping,", " `grouping`,", $columnNames);

                    $columnValues = implode("', '", array_values($row));
                    $insertQuery = "INSERT INTO $dbTableName ($cleanColumnNames) VALUES ('$columnValues')";

                    // Execute the INSERT query for each row
                    $insertResult = $db->query($insertQuery);
                }
            }
        }
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    //Delete Temporary Table
    if (isset($dbTableNameTemp)) {
        $db->query("DROP TABLE $dbTableNameTemp");
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $details = [
            "rowsInserted" => $rowCounter
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
