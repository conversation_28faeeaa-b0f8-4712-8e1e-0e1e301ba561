<?php
/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__) . '/utils/functions.php');
require_once(dirname(__FILE__) . '/utils/logging.php');
require(dirname(__FILE__) . '/utils/OneRosterAPI.php');

use Google\Cloud\Storage\StorageClient;

// Memory monitoring function - available globally
function logMemoryUsage($label = "") {
    $memoryUsed = memory_get_usage(true);
    $memoryPeak = memory_get_peak_usage(true);
    $memoryUsedMB = round($memoryUsed / 1024 / 1024, 2);
    $memoryPeakMB = round($memoryPeak / 1024 / 1024, 2);
    error_log("MEMORY $label - Current: {$memoryUsedMB}MB, Peak: {$memoryPeakMB}MB");
    
    // Simple memory pressure warning
    if ($memoryUsed > 800 * 1024 * 1024) { // 800MB warning
        error_log("WARNING: Memory usage above 800MB - monitoring for potential issues");
    }
}

// Checkpoint management for large dataset processing
class ProcessingCheckpoint {
    private $siteID;
    private $checkpointFile;
    
    public function __construct($siteID) {
        $this->siteID = $siteID;
        // Secure checkpoint file with restricted permissions
        $secureDir = sys_get_temp_dir() . '/oneroster_secure';
        if (!is_dir($secureDir)) {
            mkdir($secureDir, 0700, true);
        }
        $this->checkpointFile = $secureDir . "/checkpoint_" . hash('sha256', $siteID . $_SERVER['REQUEST_TIME']) . ".json";
    }
    
    public function save($section, $offset, $processedCount, $additionalData = []) {
        $checkpoint = [
            'timestamp' => time(),
            'section' => $section,
            'offset' => $offset,
            'processedCount' => $processedCount,
            'additionalData' => $additionalData
        ];
        
        file_put_contents($this->checkpointFile, json_encode($checkpoint));
        error_log("CHECKPOINT: Saved progress for section '$section' at offset $offset (processed: $processedCount)");
    }
    
    public function load() {
        if (file_exists($this->checkpointFile)) {
            $checkpoint = json_decode(file_get_contents($this->checkpointFile), true);
            if ($checkpoint && (time() - $checkpoint['timestamp']) < 3600) { // Valid for 1 hour
                error_log("CHECKPOINT: Restored progress from section '{$checkpoint['section']}' at offset {$checkpoint['offset']}");
                return $checkpoint;
            }
        }
        return null;
    }
    
    public function clear() {
        if (file_exists($this->checkpointFile)) {
            unlink($this->checkpointFile);
            error_log("CHECKPOINT: Cleared checkpoint file");
        }
    }
}

// Simple cURL-based OneRoster API for special processing sites
class SimpleCurlOneRosterAPI
{
    private $config;
    private $token;
    
    public function __construct($config)
    {
        $this->config = $config;
        $this->token = null;
    }
    
    public function get($endpoint, $params = [])
    {
        return $this->oneRosterGet($endpoint, $params);
    }
    
    public function getWithRetry($endpoint, $params = [], $maxRetries = 3)
    {
        // For simplicity, just call the regular get method
        // The oneRosterGet method already has built-in retry logic
        return $this->oneRosterGet($endpoint, $params);
    }
    
    public function getConsecutiveFailures()
    {
        // Return 0 for compatibility with diagnostic logging
        return 0;
    }
    
    private function oneRosterGet($endpoint, $params = [], $retry = true, $allow404 = false)
    {
        if (!$this->token) $this->token = $this->refreshToken();
        
        $url = rtrim($this->config->oneRoster->baseUri, "/") . "/" . ltrim($endpoint, "/");
        if ($params) $url .= '?' . http_build_query($params);
        
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => ["Authorization: Bearer " . $this->token],
            CURLOPT_TIMEOUT => 60,  // Increased timeout for larger requests
            CURLOPT_CONNECTTIMEOUT => 10,  // Fast connection timeout
        ]);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $err = curl_error($ch);
        curl_close($ch);
        
        if (($httpCode == 502 || $httpCode == 401 || stripos($err, 'SSL') !== false) && $retry) {
            error_log("HTTP $httpCode or SSL error, refreshing token and retrying...");
            $this->token = $this->refreshToken();
            return $this->oneRosterGet($endpoint, $params, false, $allow404);
        }
        
        if ($httpCode === 404 && $allow404) {
            return null;
        }
        
        if ($httpCode !== 200) {
            error_log("OneRoster GET failed ($httpCode): $response");
            return false;
        }
        
        return json_decode($response, true);
    }
    
    private function refreshToken()
    {
        error_log("Refreshing OneRoster token using simple cURL method");
        
        $tokenUrl = $this->config->oneRoster->authUri;
        $ch = curl_init($tokenUrl);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query([
                'grant_type' => 'client_credentials',
                'client_id' => $this->config->oneRoster->clientID,
                'client_secret' => $this->config->oneRoster->clientSecret
            ]),
            CURLOPT_HTTPHEADER => ['Content-Type: application/x-www-form-urlencoded'],
            CURLOPT_TIMEOUT => 30,
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("Token refresh failed ($httpCode): $response");
        }
        
        $tokenData = json_decode($response, true);
        if (!isset($tokenData['access_token'])) {
            throw new Exception("No access token in response: $response");
        }
        
        error_log("Token refreshed successfully using simple cURL method");
        return $tokenData['access_token'];
    }
}

// Enhanced Token rotation wrapper class for 2.0 version with robust retry logic
class OneRosterAPIWithTokenRotation
{
    private $api;
    private $config;
    private $tokenExpiresAt;
    private $lastApiCallTime;
    private $consecutiveFailures;
    
    public function __construct($api, $config)
    {
        $this->api = $api;
        $this->config = $config;
        // Set initial token expiration (30 minutes based on observed behavior)
        $this->tokenExpiresAt = time() + 1800;
        $this->lastApiCallTime = 0;
        $this->consecutiveFailures = 0;
    }
    
    public function get($endpoint, $params = [])
    {
        return $this->getWithRetry($endpoint, $params, 5);
    }
    
    public function getWithRetry($endpoint, $params = [], $maxRetries = 5)
    {
        $attempt = 1;
        
        while ($attempt <= $maxRetries) {
            if ($this->isTokenExpired()) {
                error_log("Token expired, refreshing before attempt $attempt for endpoint: $endpoint");
                $this->refreshToken();
            }
            
            $timeSinceLastCall = microtime(true) - $this->lastApiCallTime;
            if ($timeSinceLastCall < 0.1) {
                usleep((0.1 - $timeSinceLastCall) * 1000000);
            }
            
            $this->lastApiCallTime = microtime(true);
            $result = $this->api->get($endpoint, $params);
            
            if ($result !== false) {
                $this->consecutiveFailures = 0;
                return $result;
            }
            
            $this->consecutiveFailures++;
            error_log("API call failed for endpoint: $endpoint (attempt $attempt/$maxRetries, consecutive failures: {$this->consecutiveFailures})");
            
            if ($attempt < $maxRetries) {
                $baseDelay = min(300, pow(2, $attempt) * 2);
                
                $jitter = rand(0, 1000) / 1000;
                $delay = $baseDelay + $jitter;
                
                error_log("Retrying in {$delay}s (attempt $attempt/$maxRetries)");
                sleep($delay);
                
                // Reinitialize connection on SSL errors or after multiple failures
                if ($this->consecutiveFailures >= 3 || $attempt >= 3) {
                    error_log("DIAGNOSTIC: Reinitializing API connection due to consecutive failures ($this->consecutiveFailures) or multiple retry attempts ($attempt)");
                    try {
                        $this->api = OneRosterAPI::init(
                            $this->config->oneRoster->service,
                            $this->config->oneRoster->clientID,
                            $this->config->oneRoster->clientSecret,
                            $this->config->oneRoster->baseUri,
                            $this->config->oneRoster->authUri
                        );
                        $this->tokenExpiresAt = time() + 1800;
                        error_log("DIAGNOSTIC: API connection reinitialized in retry logic");
                    } catch (Exception $e) {
                        error_log("WARNING: Failed to reinitialize API in retry logic: " . $e->getMessage());
                    }
                } else {
                    $this->refreshToken();
                }
            }
            
            $attempt++;
        }
        
        error_log("CRITICAL: API call failed after $maxRetries attempts for endpoint: $endpoint");
        return false;
    }
    
    public function validateAPIHealth()
    {
        error_log("Performing API health check");
        $startTime = microtime(true);
        
        $test = $this->getWithRetry("ims/oneroster/v1p1/schools", ["limit" => 1], 3);
        
        $duration = microtime(true) - $startTime;
        
        if ($test === false) {
            error_log("CRITICAL: API health check FAILED after {$duration}s");
            throw new Exception("API health check failed - cannot proceed with data sync");
        }
        
        error_log("API health check PASSED in {$duration}s");
        return true;
    }
    
    private function isTokenExpired()
    {
        return time() >= ($this->tokenExpiresAt - 900);
    }
    
    private function refreshToken()
    {
        try {
            error_log("Refreshing OneRoster API token for 2.0 (consecutive failures: {$this->consecutiveFailures})");
            $this->api = OneRosterAPI::init(
                $this->config->oneRoster->service,
                $this->config->oneRoster->clientID,
                $this->config->oneRoster->clientSecret,
                $this->config->oneRoster->baseUri,
                $this->config->oneRoster->authUri
            );
            $this->tokenExpiresAt = time() + 1800;
            error_log("OneRoster API token refreshed successfully for 2.0");
        } catch (Exception $e) {
            error_log("CRITICAL: Token refresh failed in 2.0: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function getConsecutiveFailures()
    {
        return $this->consecutiveFailures;
    }
}

// Streaming GCS uploader class for chunked uploads
class StreamingGCSUploader
{
    private $bucket;
    private $currentDate;
    private $siteID;
    private $chunkSize;
    private $chunks;
    private $totalRecords;
    
    public function __construct($bucket, $currentDate, $siteID, $chunkSize = 1000)
    {
        $this->bucket = $bucket;
        $this->currentDate = $currentDate;
        $this->siteID = $siteID;
        $this->chunkSize = $chunkSize;
        $this->chunks = [];
        $this->totalRecords = [];
    }
    
    public function addRecord($type, $record)
    {
        if (!isset($this->chunks[$type])) {
            $this->chunks[$type] = [];
            $this->totalRecords[$type] = 0;
        }
        
        $this->chunks[$type][] = $record;
        $this->totalRecords[$type]++;
        
        if (count($this->chunks[$type]) >= $this->chunkSize) {
            $this->uploadChunk($type);
        }
    }
    
    public function finalize($type)
    {
        if (isset($this->chunks[$type]) && !empty($this->chunks[$type])) {
            $this->uploadChunk($type);
        }
        
        $totalCount = isset($this->totalRecords[$type]) ? $this->totalRecords[$type] : 0;
        error_log("Streaming upload completed for $type: $totalCount total records uploaded");
    }
    
    private function uploadChunk($type)
    {
        try {
            if (empty($this->chunks[$type])) {
                return;
            }
            
            $chunkData = $this->chunks[$type];
            $chunkNumber = ceil($this->totalRecords[$type] / $this->chunkSize);
            
            $jsonEncoded = json_encode($chunkData);
            if ($jsonEncoded === false) {
                error_log("Failed to encode JSON for type: $type, chunk: $chunkNumber");
                return;
            }
            
            $tempFile1 = tmpfile();
            fwrite($tempFile1, $jsonEncoded);
            rewind($tempFile1);

            $fileName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$type}_chunk_{$chunkNumber}.json";
            $this->bucket->upload($tempFile1, [
                'name' => "{$this->currentDate}/site-id/{$this->siteID}/$fileName"
            ]);
            // Close file handle immediately after upload
            fclose($tempFile1);

            $tempFile2 = tmpfile();
            fwrite($tempFile2, $jsonEncoded);
            rewind($tempFile2);

            $folderName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$type}";
            $this->bucket->upload($tempFile2, [
                'name' => "{$this->currentDate}/filename/$folderName/$folderName-{$this->siteID}_chunk_{$chunkNumber}.json"
            ]);
            // Close file handle immediately after upload
            fclose($tempFile2);
            
            $chunkSize = count($chunkData);
            error_log("Successfully uploaded $type chunk $chunkNumber to GCS ($chunkSize records)");
            
            $this->chunks[$type] = [];
            
            if ($chunkNumber % 5 == 0) {
                gc_collect_cycles();
            }
            
        } catch (Exception $e) {
            error_log("Error uploading $type chunk to GCS: " . $e->getMessage());
        }
    }
    
    public function getRecordCount($type)
    {
        return isset($this->totalRecords[$type]) ? $this->totalRecords[$type] : 0;
    }
}

// Designate private function (kept for backward compatibility)
function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID)
{
    try {
        if (empty($data)) {
            error_log("No data to upload for type: $type");
            return;
        }

        // Convert data to JSON
        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log("Failed to encode JSON for type: $type");
            return;
        }

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);

        $fileName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$type}.json";
        $bucket->upload($tempFile1, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);
        fclose($tempFile1);

        // Upload to filename folder using a separate file handle
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);

        $folderName = "Abre_OneRoster_1.1_API_ChildTerms_2.0_{$type}";
        $bucket->upload($tempFile2, [
            'name' => "$currentDate/filename/$folderName/$folderName-{$siteID}.json"
        ]);
        fclose($tempFile2);

        error_log("Successfully uploaded $type data to GCS");
    } catch (Exception $e) {
        error_log("Error uploading $type data to GCS: " . $e->getMessage());
    }
}

// Streaming batch processing functions - defined globally


function processStaffBatch($db, $staffBatch, $schoolsCache, $siteID, $schoolYearID,
                          $dbColumns, $emptyStringEncrypted, $api,
                          &$usersCache, &$classUniqueArray, &$teacherCache, $streamingUploader) {
    $valuesToImport = [];
    
    // Create prepared statement for this batch to prevent memory leaks
    $insertSql = "INSERT INTO directory
                  (updatedtime, superadmin, admin, picture, firstname, lastname,
                    middlename, address, city, state, zip, email, phone, extension,
                    cellphone, ss, dob, gender, ethnicity, title, contract, classification,
                    location, grade, subject, doh, senioritydate, effectivedate, rategroup,
                    step, educationlevel, salary, hours, probationreportdate,
                    statebackgroundcheck, federalbackgroundcheck, stateeducatorid,
                    licensetype1, licenseissuedate1, licenseexpirationdate1, licenseterm1,
                    licensetype2, licenseissuedate2, licenseexpirationdate2, licenseterm2,
                    licensetype3, licenseissuedate3, licenseexpirationdate3, licenseterm3,
                    licensetype4, licenseissuedate4, licenseexpirationdate4, licenseterm4,
                    licensetype5, licenseissuedate5, licenseexpirationdate5, licenseterm5,
                    licensetype6, licenseissuedate6, licenseexpirationdate6, licenseterm6,
                    permissions, role, contractdays, siteID
                  )
                  VALUES (CURRENT_TIMESTAMP, 0, 0, '', ?, ?, '', ?, ?, ?, ?, ?, ?, '',
                    ?, ?, ?, ?, ?, '', ?, '', '', '', '', ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                  );";
    $insertStmt = $db->stmt_init();
    $insertStmt->prepare($insertSql);
    
    foreach ($staffBatch as $user) {
        // Stream staff data immediately (only for teachers to match original logic)
        if ($user["role"] == "teacher") {
            $streamingUploader->addRecord('staff', $user);
        }
        
        if ($user["role"] == "teacher") {
            $staffID = $user["identifier"] != "" ? $user["identifier"] : $user["sourcedId"];
            $escapedStaffID = trim($db->escape_string($staffID));

            $firstName = trim($db->escape_string($user["givenName"]));
            $middleName = trim($db->escape_string($user["middleName"]));
            $lastName = trim($db->escape_string($user["familyName"]));
            $email = trim($db->escape_string($user["email"]));

            $valuesToImport[] = "(
              '$escapedStaffID', '$firstName', '$middleName', '$lastName', '$email', 1, NOW(), $siteID, $schoolYearID
            )";

            // Handle school relationships
            $quotedSchools = array_map(function ($school) use ($schoolsCache) {
                $schoolID = $schoolsCache[$school["sourcedId"]]["id"];
                return "'$schoolID'";
            }, $user["orgs"]);
            
            // Skip school deletion if user has no organizations
            if (empty($quotedSchools)) {
                error_log("Staff ID $staffID has no organizations - skipping school deletion");
                continue;
            }
            
            $schoolsForSql = "(" . join(",", $quotedSchools) . ")";

            $schoolDeleteSql = "DELETE FROM abre_staff_schools
                    WHERE school_code NOT IN $schoolsForSql
                      AND is_imported = 1 AND staff_id = ? AND site_id = ?";
            
            // Check database connection and reconnect if needed
            if (!$db->ping()) {
                error_log("Database connection lost during staff processing, attempting to reconnect...");
                $db->close();
                $db = new mysqli($_ENV['DB_HOST'], $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], $_ENV['DB_NAME']);
                if ($db->connect_error) {
                    error_log("Failed to reconnect to database: " . $db->connect_error);
                    continue;
                }
                error_log("Database reconnected successfully");
            }
            
            $deleteSchoolsStmt = $db->stmt_init();
            if (!$deleteSchoolsStmt || !$deleteSchoolsStmt->prepare($schoolDeleteSql)) {
                error_log("Failed to prepare school delete statement for staff ID $staffID: " . $db->error);
                continue;
            }
            
            if (!$deleteSchoolsStmt->bind_param("si", $staffID, $siteID)) {
                error_log("Failed to bind parameters for school delete statement for staff ID $staffID");
                $deleteSchoolsStmt->close();
                continue;
            }
            $deleteSchoolsStmt->execute();
            $deleteSchoolsStmt->close();

            $schoolsToImport = [];
            foreach ($user["orgs"] as $school) {
                $schoolID = $schoolsCache[$school["sourcedId"]]["id"];
                $schoolsToImport[] = "('$escapedStaffID', '$schoolID', $siteID, 1, NOW())";
            }
            $schoolsImportString = implode(",", $schoolsToImport);

            $schoolInsertSql = "INSERT INTO abre_staff_schools
                      (staff_id, school_code, site_id, is_imported, imported_on)
                    VALUES $schoolsImportString
                    ON DUPLICATE KEY UPDATE
                      is_imported = 1,
                      imported_on = NOW()";
            
            $insertSchoolStmt = $db->stmt_init();
            if (!$insertSchoolStmt || !$insertSchoolStmt->prepare($schoolInsertSql)) {
                error_log("Failed to prepare school insert statement for staff ID $staffID: " . $db->error);
                continue;
            }
            $insertSchoolStmt->execute();
            $insertSchoolStmt->close();

            // Handle directory insertion
            if ($user["email"] != "") {
                $existingRow = 0;
                $selectSql = "SELECT COUNT(*) FROM directory WHERE email = ? AND siteID = ?";
                $selectStmt = $db->stmt_init();
                if (!$selectStmt || !$selectStmt->prepare($selectSql)) {
                    error_log("Failed to prepare directory select statement for staff ID $staffID: " . $db->error);
                    continue;
                }
                if (!$selectStmt->bind_param("si", $user["email"], $siteID)) {
                    error_log("Failed to bind parameters for directory select statement for staff ID $staffID");
                    $selectStmt->close();
                    continue;
                }
                $selectStmt->execute();
                $selectStmt->bind_result($existingRow);
                $selectStmt->fetch();
                $selectStmt->close();

                if (!$existingRow) {
                    $insertStmt->bind_param(
                        "sssssssssssssssssssssssssssssssssssssssssssssssssssssi",
                        $user["givenName"], $user["familyName"], $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $user["email"], $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $emptyStringEncrypted,
                        $emptyStringEncrypted, $emptyStringEncrypted, $siteID
                    );
                    $insertStmt->execute();
                }
            }

            $usersCache[$user["sourcedId"]] = [
                "localId" => $staffID,
                "firstName" => $user["givenName"],
                "middleName" => $user["middleName"],
                "lastName" => $user["familyName"]
            ];

            // Handle teacher classes with enhanced retry logic
            $teacherClasses = $api->getWithRetry("ims/oneroster/v1p1/teachers/" . $user["sourcedId"] . "/classes", [], 3);
            if ($teacherClasses !== false) {
                foreach ($teacherClasses["classes"] as $class) {
                    $classUnique = $class["sourcedId"] . '-' . $staffID;
                    if (!isset($classUniqueArray[$classUnique])) {
                        $classUniqueArray[$classUnique] = [
                            "sourcedId" => $class["sourcedId"],
                            "localId" => $staffID,
                            "firstName" => $user["givenName"],
                            "middleName" => $user["middleName"],
                            "lastName" => $user["familyName"]
                        ];
                    }

                    if (!isset($teacherCache[$class["sourcedId"]])) {
                        $teacherCache[$class["sourcedId"]] = [
                            "localId" => $staffID,
                            "firstName" => $user["givenName"],
                            "middleName" => $user["middleName"],
                            "lastName" => $user["familyName"]
                        ];
                    }
                }
                
                // Explicitly free API response memory
                $teacherClasses = null;
                unset($teacherClasses);
            } else {
                error_log("WARNING: Failed to retrieve teacher classes for staff ID: $staffID (sourcedId: {$user['sourcedId']}) after retries");
            }
        }
    }
    
    // Insert batched staff records
    if (!empty($valuesToImport)) {
        $values = implode(",", $valuesToImport);
        $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                      FirstName = VALUES(FirstName),
                      MiddleName = VALUES(MiddleName),
                      LastName = VALUES(LastName),
                      EMail1 = VALUES(EMail1),
                      is_imported = 1,
                      imported_on = NOW(),
                      is_archived = 0";
        $db->query($importQuery);
    }
    
    // Close prepared statement to prevent memory leaks
    $insertStmt->close();
    
    // Explicit memory cleanup for batch processing
    unset($valuesToImport);
    unset($insertSql);
    unset($insertStmt);
}

function processCourseBatch($db, $courseBatch, &$courseCache, $streamingUploader) {
    foreach ($courseBatch as $course) {
        // Stream course data immediately
        $streamingUploader->addRecord('courses', $course);
        
        $courseID = $course["courseCode"] != "" ? $course["courseCode"] : $course["sourcedId"];
        $courseCache[$course["sourcedId"]] = [
            "id" => $courseID
        ];
    }
}

function processClassBatch($db, $classBatch, $schoolsCache, $courseCache, $teacherCache,
                         $gradingPeriods, $siteID, $schoolYearID, $dbColumns, $api, &$classCache, $streamingUploader) {
    $valuesToImport = [];
    
    foreach ($classBatch as $class) {
        // Stream class data immediately
        $streamingUploader->addRecord('classes', $class);
        
        $classOrg = isset($class["school"]["sourcedId"]) ? $class["school"]["sourcedId"] : '';
        if (empty($classOrg)) {
            continue; // Skip class if no school ID
        }
        $classOrg = trim($db->escape_string($classOrg));

        $schoolCode = $schoolsCache[$classOrg]["id"];
        $schoolCode = trim($db->escape_string($schoolCode));

        $sectionCode = $class["sourcedId"];
        $sectionCode = trim($db->escape_string($sectionCode));

        if (isset($class["section_alias"])) {
            $courseTitle = trim($db->escape_string($class["section_alias"]));
        } else {
            $courseTitle = trim($db->escape_string($class["title"]));
        }

        $courseCode = "";
        $courseSourcedId = isset($class["course"]["sourcedId"]) ? $class["course"]["sourcedId"] : '';
        if (empty($courseSourcedId)) {
            continue; // Skip class if no course ID
        }
        if (!isset($courseCache[$courseSourcedId])) {
            $course = $api->getWithRetry("ims/oneroster/v1p1/courses/" . $courseSourcedId, [], 3);
            if ($course !== false) {
                $courseCode = $course["course"]["courseCode"] != "" ? $course["course"]["courseCode"] : $course["course"]["sourcedId"];
                $courseCache[$courseSourcedId] = [
                    "id" => $courseCode
                ];
                $courseCode = trim($db->escape_string($courseCode));
            } else {
                error_log("WARNING: Failed to retrieve course data for course sourcedId: {$courseSourcedId} after retries");
                $courseCode = trim($db->escape_string($courseSourcedId)); // Fallback to sourcedId
            }
        } else {
            $courseCode = trim($db->escape_string($courseCache[$courseSourcedId]["id"]));
        }

        if (!isset($classCache[$class["sourcedId"]])) {
            $classCache[$class["sourcedId"]] = [
                "terms" => $class["terms"],
                "periods" => $class["periods"],
                "sectionCode" => $sectionCode,
                "courseCode" => $courseCode,
                "title" => $courseTitle
            ];
        }

        $teacher = [];
        if (isset($teacherCache[$class["sourcedId"]])) {
            $teacher = $teacherCache[$class["sourcedId"]];
        }

        foreach ($class["terms"] as $term) {
            foreach ($class["periods"] as $period) {
                $escapedPeriod = trim($db->escape_string($period));
                $teacherID = isset($teacher["localId"]) ? trim($db->escape_string($teacher["localId"])) : "";

                $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"];
                if (count($cachedTerms["children"])) {
                    foreach ($cachedTerms["children"] as $childTerm) {
                        $escapedTerm = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                        $valuesToImport[] = "(
                          '$schoolCode', '$courseCode', '$sectionCode', '$teacherID',
                          '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                        )";
                    }
                } else {
                    $escapedTerm = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                    $valuesToImport[] = "(
                      '$schoolCode', '$courseCode', '$sectionCode', '$teacherID',
                      '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                    )";
                }
            }
        }
    }
    
    // Insert batched class records
    if (!empty($valuesToImport)) {
        insertRows($db, $dbColumns, $valuesToImport);
    }
}

function processEnrollmentBatch($db, $enrollmentBatch, $schoolsCache, $usersCache, $classCache,
                              $classUniqueArray, $gradingPeriods, $siteID, $schoolYearID,
                              $staffDbColumns, $studentDbColumns, $api, $streamingUploader) {
    $staffValues = [];
    $studentValues = [];

    foreach ($enrollmentBatch as $enrollmentRecord) {
        // Stream enrollment data immediately
        $streamingUploader->addRecord('enrollments', $enrollmentRecord);
        
        $userSourcedId = isset($enrollmentRecord["user"]["sourcedId"]) ? $enrollmentRecord["user"]["sourcedId"] : '';
        $classSourcedId = isset($enrollmentRecord["class"]["sourcedId"]) ? $enrollmentRecord["class"]["sourcedId"] : '';

        if (empty($userSourcedId) || empty($classSourcedId)) {
            continue; // Skip enrollment if missing required IDs
        }

        $userID = "";
        $firstName = "";
        $lastName = "";
        $fullName = "";
        if (isset($usersCache[$userSourcedId])) {
            $userID = trim($db->escape_string($usersCache[$userSourcedId]["localId"]));
            $firstName = trim($db->escape_string($usersCache[$userSourcedId]["firstName"]));
            $lastName = trim($db->escape_string($usersCache[$userSourcedId]["lastName"]));
            $fullName = trim($db->escape_string("$firstName $lastName"));
        } else {
            continue;
        }

        $courseSourcedId = "";
        $terms = [];
        $periods = [];
        $sectionCode = "";
        $courseName = "";
        $courseCode = "";
        if (isset($classCache[$classSourcedId])) {
            $terms = $classCache[$classSourcedId]["terms"];
            $periods = $classCache[$classSourcedId]["periods"];
            $sectionCode = trim($db->escape_string($classCache[$classSourcedId]["sectionCode"]));
            $courseName = trim($db->escape_string($classCache[$classSourcedId]["title"]));
            $courseCode = $classCache[$classSourcedId]["courseCode"];
        } else {
            $classRecord = $api->getWithRetry("ims/oneroster/v1p1/classes/$classSourcedId", [], 3);
            if ($classRecord !== false) {
                $terms = $classRecord["class"]["terms"];
                $periods = $classRecord["class"]["periods"];
                $sectionCode = trim($db->escape_string($classRecord["class"]["sourcedId"]));
                if (isset($classRecord["class"]["section_alias"])) {
                    $courseName = trim($db->escape_string($classRecord["class"]["section_alias"]));
                } else {
                    $courseName = trim($db->escape_string($classRecord["class"]["title"]));
                }
                $courseSourcedId = trim($db->escape_string($classRecord["class"]["course"]["sourcedId"]));
            } else {
                error_log("CRITICAL: Failed to retrieve class record for classSourcedId: $classSourcedId after retries - enrollment may be incomplete");
                continue; // Skip this enrollment if we can't get class data
            }

            if ($courseSourcedId != "") {
                $courseCode = "";
                $courseRecord = $api->getWithRetry("ims/oneroster/v1p1/courses/$courseSourcedId", [], 3);
                if ($courseRecord !== false) {
                    $courseCode = $courseRecord["course"]["courseCode"] != "" ? $courseRecord["course"]["courseCode"] : $courseRecord["course"]["sourcedId"];
                    $courseCode = trim($db->escape_string($courseCode));
                } else {
                    error_log("WARNING: Failed to retrieve course record for courseSourcedId: $courseSourcedId after retries - using sourcedId as fallback");
                    $courseCode = trim($db->escape_string($courseSourcedId));
                }
            }
        }

        $schoolSourcedId = isset($enrollmentRecord["school"]["sourcedId"]) ? $enrollmentRecord["school"]["sourcedId"] : '';
        if (empty($schoolSourcedId) || !isset($schoolsCache[$schoolSourcedId])) {
            continue; // Skip enrollment if school not found
        }
        $schoolCode = trim($db->escape_string($schoolsCache[$schoolSourcedId]["id"]));

        if ($enrollmentRecord["role"] == "teacher" || $enrollmentRecord["role"] == "administrator") {
            foreach ($terms as $term) {
                foreach ($periods as $period) {
                    $escapedPeriod = trim($db->escape_string($period));

                    $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"];
                    if (count($cachedTerms["children"])) {
                        foreach ($cachedTerms["children"] as $childTerm) {
                            $escapedTermCode = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                            $staffValues[] = "(
              '$userID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
              '$escapedPeriod', '$courseName', '$fullName', $siteID, $schoolYearID
            )";
                        }
                    } else {
                        $escapedTermCode = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                        $staffValues[] = "(
            '$userID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
            '$escapedPeriod', '$courseName', '$fullName', $siteID, $schoolYearID
          )";
                    }

                    // Insert staff records in larger batches for better performance
                    if (count($staffValues) >= 250) {
                        insertRows($db, $staffDbColumns, $staffValues);
                        $staffValues = [];
                    }
                }
            }
        } elseif ($enrollmentRecord["role"] == "student") {
            // Loop Through All Unique Classes (Primary and Secondary Teachers) - EXACT original logic
            foreach ($classUniqueArray as $key => $value) {
                if (strpos($key, $classSourcedId) !== false) {
                    $staffID = $value['localId'];
                    $teacherName = trim($db->escape_string($value["firstName"] . " " . $value["lastName"]));

                    foreach ($terms as $term) {
                        foreach ($periods as $period) {
                            $escapedPeriod = trim($db->escape_string($period));
                            $cachedTerms = $gradingPeriods[$term["sourcedId"] . "-$siteID"];
                            if (count($cachedTerms["children"])) {
                                foreach ($cachedTerms["children"] as $childTerm) {
                                    $escapedTerm = trim($db->escape_string($childTerm["sourcedId"] . "-$siteID"));
                                    $studentValues[] = "(
                      '$userID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
                      '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                    )";
                                }
                            } else {
                                $escapedTerm = trim($db->escape_string($term["sourcedId"] . "-$siteID"));
                                $studentValues[] = "(
                    '$userID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
                    '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
                  )";
                            }

                            // Insert student records in larger batches for better performance
                            if (count($studentValues) >= 1000) {
                                insertRows($db, $studentDbColumns, $studentValues, "ON DUPLICATE KEY UPDATE FirstName = VALUES(FirstName), LastName = VALUES(LastName), CourseName = VALUES(CourseName), StaffId = VALUES(StaffId), TeacherName = VALUES(TeacherName)");
                                $studentValues = [];
                            }
                        }
                    }
                }
            }
        }
    }

    // Insert any remaining records
    if (!empty($staffValues)) {
        insertRows($db, $staffDbColumns, $staffValues);
    }
    if (!empty($studentValues)) {
        insertRows($db, $studentDbColumns, $studentValues, "ON DUPLICATE KEY UPDATE FirstName = VALUES(FirstName), LastName = VALUES(LastName), CourseName = VALUES(CourseName), StaffId = VALUES(StaffId), TeacherName = VALUES(TeacherName)");
    }
}

// Special processing function for sites with unusual OneRoster implementations
function processAllUsersFirst($db, $api, $config, $siteID, $schoolYearID, $jobStartTime, $schoolsCache, &$usersCache, &$classUniqueArray, &$teacherCache, $streamingUploader) {
    error_log("SPECIAL PROCESSING: Starting complete users processing first");
    
    $limit = 2000;  // Increased from 1000 - fewer API calls
    $offset = 0;
    $batchSize = 1000;  // Increased from 500 - larger batches
    $processedCount = 0;
    $staffBatch = [];
    
    $dbColumns = "INSERT INTO Abre_Staff
          (StaffID, FirstName, MiddleName, LastName, EMail1,
            is_imported, imported_on, siteID, school_year_id)
          VALUES ";

    $emptyStringEncrypted = encrypt("", $config->dbKey->iv, $config->dbKey->key);
    
    $maxIterations = calculateMaxIterations('users', $limit);
    $iteration = 0;
    $consecutiveEmpty = 0;
    
    do {
        if (++$iteration > $maxIterations) {
            error_log("WARNING: Users pagination reached maximum iterations ($maxIterations)");
            break;
        }
        
        error_log("SPECIAL: Processing users at offset $offset");
        $users = $api->getWithRetry("ims/oneroster/v1p1/users", [
            "offset" => $offset,
            "limit" => $limit
        ], 5);
        
        if (isset($users["users"]) && count($users["users"])) {
            $consecutiveEmpty = 0;
            foreach ($users["users"] as $user) {
                $staffBatch[] = $user;
                $processedCount++;

                if (count($staffBatch) >= $batchSize) {
                    processStaffBatch($db, $staffBatch, $schoolsCache, $siteID, $schoolYearID,
                                    $dbColumns, $emptyStringEncrypted, $api,
                                    $usersCache, $classUniqueArray, $teacherCache, $streamingUploader);
                    $staffBatch = [];

                    // Only run garbage collection every 5 batches to reduce overhead
                    static $batchCount = 0;
                    if (++$batchCount % 5 == 0) {
                        gc_collect_cycles();
                    }
                }
            }
            $offset += $limit;
        } else {
            $consecutiveEmpty++;
            if ($consecutiveEmpty >= 5) {
                error_log("SPECIAL: Users API returned empty results 5 times consecutively");
                break;
            }
            $offset += $limit;
        }
    } while (isset($users["users"]) && count($users["users"]));

    // Process any remaining staff
    if (!empty($staffBatch)) {
        processStaffBatch($db, $staffBatch, $schoolsCache, $siteID, $schoolYearID,
                        $dbColumns, $emptyStringEncrypted, $api,
                        $usersCache, $classUniqueArray, $teacherCache, $streamingUploader);
    }
    
    error_log("SPECIAL PROCESSING: Completed all users - classUniqueArray has " . count($classUniqueArray) . " entries");
    error_log("SPECIAL PROCESSING: usersCache has " . count($usersCache) . " users total");
}

function processAllEnrollments($db, $api, $config, $siteID, $schoolYearID, $schoolsCache, $usersCache, $classCache, $classUniqueArray, $gradingPeriods, $streamingUploader) {
    error_log("SPECIAL PROCESSING: Starting enrollments with complete classUniqueArray");
    
    $staffDbColumns = "INSERT INTO Abre_StaffSchedules
                  (StaffID, SchoolCode, CourseCode, SectionCode, TermCode, Period,
                    CourseName, TeacherName, siteID, school_year_id)
                  VALUES ";

    $studentDbColumns = "INSERT INTO Abre_StudentSchedules
                    (StudentID, FirstName, LastName, SchoolCode, CourseCode, SectionCode,
                      CourseName, StaffId, TeacherName, TermCode, Period, siteID, school_year_id)
                    VALUES ";

    $limit = 2000;  // Increased from 1000 - fewer API calls
    $offset = 0;
    $deleteRecords = true;
    $batchSize = 1000;  // Increased from 500 - larger batches
    $enrollmentBatch = [];
    $processedCount = 0;

    $maxIterations = calculateMaxIterations('enrollments', $limit);
    $iteration = 0;
    $consecutiveEmpty = 0;
    
    do {
        if (++$iteration > $maxIterations) {
            error_log("WARNING: Enrollments pagination reached maximum iterations ($maxIterations)");
            break;
        }

        // Memory monitoring every 25 iterations (reduced frequency)
        if ($iteration % 25 == 0) {
            logMemoryUsage("enrollment iteration $iteration");
        }

        $enrollments = $api->getWithRetry("ims/oneroster/v1p1/enrollments", [
            "offset" => $offset,
            "limit" => $limit
        ], 5);

        if (isset($enrollments["enrollments"]) && count($enrollments["enrollments"])) {
            $consecutiveEmpty = 0;
            if ($deleteRecords) {
                // Delete existing records once
                $db->autocommit(false);
                try {
                    $deleteSql = "DELETE FROM Abre_StaffSchedules WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();

                    $deleteSql = "DELETE FROM Abre_StudentSchedules WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();

                    $db->commit();
                    $db->autocommit(true);
                    $deleteRecords = false;
                    error_log("SPECIAL: Deleted existing schedule records");
                } catch (Exception $e) {
                    $db->rollback();
                    $db->autocommit(true);
                    error_log("ERROR: Schedule deletion failed - " . $e->getMessage());
                    throw $e;
                }
            }

            foreach ($enrollments["enrollments"] as $enrollmentRecord) {
                $enrollmentBatch[] = $enrollmentRecord;
                $processedCount++;

                if (count($enrollmentBatch) >= $batchSize) {
                    logMemoryUsage("before processing enrollment batch of $batchSize");
                    processEnrollmentBatch($db, $enrollmentBatch, $schoolsCache, $usersCache, $classCache,
                                         $classUniqueArray, $gradingPeriods, $siteID, $schoolYearID,
                                         $staffDbColumns, $studentDbColumns, $api, $streamingUploader);

                    // Aggressive memory cleanup for enrollments
                    $enrollmentBatch = [];
                    unset($enrollmentBatch);
                    $enrollmentBatch = [];

                    gc_collect_cycles();
                    logMemoryUsage("after processing enrollment batch");

                    // Memory pressure check
                    $memoryUsed = memory_get_usage(true);
                    if ($memoryUsed > 1024 * 1024 * 1024) { // 1GB warning
                        error_log("WARNING: High memory usage detected: " . round($memoryUsed / 1024 / 1024, 2) . "MB");
                    }
                }
            }
            $offset += $limit;
        } else {
            $consecutiveEmpty++;
            if ($consecutiveEmpty >= 5) {
                error_log("SPECIAL: Enrollments API returned empty results 5 times consecutively");
                break;
            }
            $offset += $limit;
        }

        // Clear API response memory at end of each iteration to prevent buildup
        $enrollments = null;
        unset($enrollments);
        gc_collect_cycles();

    } while (true); // Continue until we hit max iterations or consecutive empty results

    // Process any remaining enrollments
    if (!empty($enrollmentBatch)) {
        logMemoryUsage("before processing final enrollment batch of " . count($enrollmentBatch));
        processEnrollmentBatch($db, $enrollmentBatch, $schoolsCache, $usersCache, $classCache,
                             $classUniqueArray, $gradingPeriods, $siteID, $schoolYearID,
                             $staffDbColumns, $studentDbColumns, $api, $streamingUploader);
        logMemoryUsage("after processing final enrollment batch");
    }
    
    // Aggressive memory cleanup after enrollment processing
    $enrollmentBatch = null;
    $enrollments = null;
    unset($enrollmentBatch, $enrollments, $processedCount, $batchSize, $consecutiveEmpty);
    gc_collect_cycles();
    
    logMemoryUsage("after enrollment section cleanup");
    error_log("SPECIAL PROCESSING: Completed all enrollments");
}

// Calculate dynamic pagination limits based on data type and site size
function calculateMaxIterations($dataType, $batchSize) {
    // Estimated maximum records by type for large educational sites
    $estimates = [
        'schools' => 500,           // Most districts have < 500 schools
        'academicSessions' => 50,   // Usually < 50 terms/sessions per year  
        'students' => 100000,       // Large districts can have 100k+ students
        'users' => 120000,          // Staff + students + admins
        'courses' => 10000,         // Thousands of course offerings
        'classes' => 50000,         // Many class sections
        'enrollments' => 2000000    // Millions of enrollment records (students x classes)
    ];
    
    $estimatedRecords = isset($estimates[$dataType]) ? $estimates[$dataType] : 50000;
    $calculatedLimit = ceil($estimatedRecords / $batchSize) + 100; // Add 100 iteration buffer
    
    // Set reasonable bounds
    $minLimit = 10;   // At least 10 iterations
    $maxLimit = 5000; // Cap at 5000 to prevent runaway
    
    $finalLimit = max($minLimit, min($maxLimit, $calculatedLimit));
    
    error_log("PAGINATION: $dataType - Estimated records: $estimatedRecords, Batch size: $batchSize, Max iterations: $finalLimit");
    
    return $finalLimit;
}

// Database reconnection helper function
function reconnectDatabase($config) {
    if (isset($config->environment->db)) {
        $dbConfig = $config->environment->db;
        $newDb = new mysqli(
            $dbConfig->host,
            $dbConfig->user,
            $dbConfig->password,
            $dbConfig->database
        );
        
        if ($newDb->connect_error) {
            error_log("CRITICAL: Failed to reconnect to database: " . $newDb->connect_error);
            return false;
        }
        
        error_log("Database reconnected successfully");
        return $newDb;
    }
    
    error_log("CRITICAL: Database config not available for reconnection");
    return false;
}

function runJob($db, $siteID, $config)
{
    ignore_user_abort(true);
    set_time_limit(0);
    $cronName = 'Abre One Roster 1.1 - API Child Terms 2.0';

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        define("MAX_IMPORT_LIMIT", 250);

        $jobStartTime = (new DateTime("now", new DateTimeZone('UTC')))->format("Y-m-d H:i:s");
        $schoolYearID = getCurrentSchoolYearID($db);
        $currentEndingSchoolYear = getCurrentEndSchoolYear($db);
        
        // Site-specific configuration for problematic OneRoster implementations
        $needsSpecialProcessing = false;
        if (isset($config->oneRoster->specialProcessing) && $config->oneRoster->specialProcessing === true) {
            $needsSpecialProcessing = true;
            error_log("DIAGNOSTIC: Site configured for special processing (teachers first, then enrollments)");
        }

        $error = null;
        $skip = null;
        $separator = "\r\n";

        $schoolsCache = [];
        $gradingPeriods = [];
        $usersCache = [];
        $teacherCache = [];
        $classUniqueArray = [];
        $classCache = [];
        $courseCache = [];

        // Initialize Google Cloud Storage once
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $currentDate = date("Ymd");

        // Initialize streaming GCS uploader with larger chunk size for better performance
        $streamingUploader = new StreamingGCSUploader($bucket, $currentDate, $siteID, 2000);

        $baseApi = OneRosterAPI::init(
            $config->oneRoster->service,
            $config->oneRoster->clientID,
            $config->oneRoster->clientSecret,
            $config->oneRoster->baseUri,
            $config->oneRoster->authUri
        );
        
        // Wrap with enhanced token rotation functionality
        $api = new OneRosterAPIWithTokenRotation($baseApi, $config);
        
        // Perform initial API health check
        try {
            $api->validateAPIHealth();
        } catch (Exception $e) {
            error_log("CRITICAL: Initial API health check failed: " . $e->getMessage());
            throw $e;
        }

        error_log("starting schools");
        $limit = 1000;
        $offset = 0;
        $valuesToImport = [];
        $dbColumns = "";
        // Dynamic pagination limits based on expected data size
        $maxIterations = calculateMaxIterations('schools', $limit);
        $iteration = 0;
        $consecutiveEmpty = 0;
        
        do {
            if (++$iteration > $maxIterations) {
                error_log("WARNING: Schools pagination reached maximum iterations ($maxIterations). This may indicate all data was processed or an API issue.");
                break;
            }
            
            $schools = $api->get("ims/oneroster/v1p1/schools", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            
            // DIAGNOSTIC LOGGING - Add logging to validate paging diagnosis
            if (isset($schools["orgs"])) {
                error_log("DIAGNOSTIC: Schools API returned " . count($schools["orgs"]) . " schools at offset $offset");
                if (isset($schools["meta"])) {
                    error_log("DIAGNOSTIC: Schools API meta data: " . json_encode($schools["meta"]));
                }
            } else {
                error_log("DIAGNOSTIC: Schools API returned no orgs array at offset $offset");
            }
            
            if (isset($schools["orgs"]) && count($schools["orgs"])) {
                $consecutiveEmpty = 0; // Reset counter on successful data
                if ($dbColumns == "") {
                    $dbColumns = "INSERT INTO abre_schools (code, name, site_id, school_year_id) VALUES ";
                }

                foreach ($schools["orgs"] as $school) {
                    $code = $school["identifier"] != "" ? $school["identifier"] : $school["sourcedId"];
                    $codeEscaped = trim($db->escape_string($code));

                    $nameEscaped = trim($db->escape_string($school["name"]));

                    $valuesToImport[] = "(
              '$codeEscaped', '$nameEscaped', $siteID, $schoolYearID
            )";

                    if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                        insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
                        $valuesToImport = [];
                    }

                    $schoolsCache[$school["sourcedId"]] = [
                        "id" => $code,
                        "name" => $school["name"],
                        "type" => $school["type"]
                    ];

                    // Stream school data immediately
                    $streamingUploader->addRecord('schools', $school);
                }
            } else {
                $consecutiveEmpty++;
                if ($consecutiveEmpty >= 3) {
                    error_log("Schools API returned empty results 3 times consecutively. Assuming end of data.");
                    break;
                }
            }
            $offset += $limit;
        } while (isset($schools["orgs"]) && count($schools["orgs"]));
        
        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
        }
        
        // Finalize streaming upload for schools
        $streamingUploader->finalize('schools');

        // Memory cleanup after schools section
        $schools = null;
        unset($valuesToImport);
        unset($dbColumns);
        gc_collect_cycles();
        error_log("Memory cleanup completed after schools section");
        error_log("done with schools");

        error_log("starting academic sessions");
        $limit = 1000;
        $offset = 0;
        $valuesToImport = [];
        $termColumns = "";
        $termDefinitions = [];
        $deleteExecuted = false;
        
        $maxIterations = calculateMaxIterations('academicSessions', $limit);
        $iteration = 0;
        $consecutiveEmpty = 0;
        
        do {
            if (++$iteration > $maxIterations) {
                error_log("WARNING: Academic sessions pagination reached maximum iterations ($maxIterations). This may indicate all data was processed.");
                break;
            }
            
            $academicSessions = $api->get("ims/oneroster/v1p1/academicSessions", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            
            // DIAGNOSTIC LOGGING - Add logging to validate paging diagnosis
            if (isset($academicSessions["academicSessions"])) {
                error_log("DIAGNOSTIC: Academic Sessions API returned " . count($academicSessions["academicSessions"]) . " sessions at offset $offset");
                if (isset($academicSessions["meta"])) {
                    error_log("DIAGNOSTIC: Academic Sessions API meta data: " . json_encode($academicSessions["meta"]));
                }
            } else {
                error_log("DIAGNOSTIC: Academic Sessions API returned no academicSessions array at offset $offset");
            }
            
            if (isset($academicSessions["academicSessions"]) && count($academicSessions["academicSessions"])) {
                $consecutiveEmpty = 0; // Reset counter on successful data
                // Only delete once on first iteration
                if (!$deleteExecuted) {
                    $deleteSql = "DELETE FROM abre_term WHERE site_id = ? AND term_year_id = ? AND is_imported = 1";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteExecuted = true;
                }

                if ($termColumns == "") {
                    $termColumns = "INSERT INTO abre_term
                                (site_id, term_definition_id, term_year_id, start_date, end_date, is_imported)
                              VALUES ";
                }
                
                foreach ($academicSessions["academicSessions"] as $session) {
                    // Stream academic session data immediately
                    $streamingUploader->addRecord('academicSessions', $session);

                    // $session["schoolYear"] == $currentEndingSchoolYear
                    //   &&
                    if ($session["type"] != "schoolYear") {

                        $customSourceID = $session["sourcedId"] . "-$siteID";
                        if (!isset($termDefinitions[$customSourceID])) {
                            $existingDefinitionID = null;
                            $sessionSelectSql = "SELECT id FROM abre_term_definition WHERE term_input = ?";
                            $sessionSelectStmt = $db->stmt_init();
                            $sessionSelectStmt->prepare($sessionSelectSql);
                            $sessionSelectStmt->bind_param("s", $customSourceID);
                            $sessionSelectStmt->execute();
                            $sessionSelectStmt->bind_result($existingDefinitionID);
                            $sessionSelectStmt->fetch();
                            $sessionSelectStmt->close();

                            if ($existingDefinitionID === null) {
                                $insertTerm = "INSERT INTO abre_term_definition (term_input, display_short, display_long, is_hidden)
                                  VALUES (?, ?, ?, 0)";
                                $insertStmt = $db->stmt_init();
                                $insertStmt->prepare($insertTerm);
                                $insertStmt->bind_param("sss", $customSourceID, $session["title"], $session["title"]);
                                $insertStmt->execute();
                                $newTermID = $insertStmt->insert_id;
                                $insertStmt->close();
                                $termDefinitions[$customSourceID] = $newTermID;
                            } else {
                                $termDefinitions[$customSourceID] = $existingDefinitionID;
                            }
                        }

                        $escapedTermID = trim($db->escape_string($termDefinitions[$customSourceID]));
                        $escapedStartDate = trim($db->escape_string($session["startDate"]));
                        $escapedEndDate = trim($db->escape_string($session["endDate"]));
                        $valuesToImport[] = "(
                $siteID, $escapedTermID, $schoolYearID, '$escapedStartDate', '$escapedEndDate', 1
              )";

                        if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                            insertRows($db, $termColumns, $valuesToImport);
                            $valuesToImport = [];
                        }

                        $gradingPeriods[$customSourceID] = [
                            "children" => $session["children"]
                        ];
                    }
                }
            } else {
                $consecutiveEmpty++;
                if ($consecutiveEmpty >= 3) {
                    error_log("Academic sessions API returned empty results 3 times consecutively. Assuming end of data.");
                    break;
                }
            }
            $offset += $limit;
        } while (isset($academicSessions["academicSessions"]) && count($academicSessions["academicSessions"]));
        
        if (count($valuesToImport)) {
            insertRows($db, $termColumns, $valuesToImport);
        }
        
        // Finalize streaming upload for academic sessions
        $streamingUploader->finalize('academicSessions');

        // Memory cleanup after academic sessions section
        $academicSessions = null;
        unset($valuesToImport);
        unset($termColumns);
        unset($termDefinitions);
        unset($deleteExecuted);
        gc_collect_cycles();
        error_log("Memory cleanup completed after academic sessions section");
        error_log("done with academic sessions");

        error_log("starting students");
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $valuesToImport = [];
        $emailsToImport = [];
        $dbColumns = "INSERT INTO Abre_Students
                  (StudentId, FirstName, MiddleName, LastName, Email, SSID, SchoolCode,
                    SchoolName, CurrentGrade, okay_to_publish, siteID, school_year_id)
                  VALUES ";
        $adColumns = "INSERT INTO Abre_AD (Email, StudentID, siteID, school_year_id) VALUES ";

        do {
            $students = $api->get("ims/oneroster/v1p1/students", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            
            if (isset($students["users"]) && count($students["users"])) {
                if ($deleteRecords) {
                    $deleteSql = "DELETE FROM Abre_Students WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();

                    $deleteSql = "DELETE FROM Abre_AD WHERE siteID = ? AND school_year_id = ?";
                    $deleteStmt = $db->stmt_init();
                    $deleteStmt->prepare($deleteSql);
                    $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                    $deleteStmt->execute();
                    $deleteStmt->close();
                    $deleteRecords = false;
                }

                foreach ($students["users"] as $id => $student) {
                    if ($student["enabledUser"] && $student["status"] == "active") {
                        if (isset($config->oneRoster->studentId)) {
                            if ($config->oneRoster->studentId === 'sourcedId') {
                                $studentID = $student["sourcedId"];
                            } elseif ($config->oneRoster->studentId === 'identifier') {
                                $studentID = $student["identifier"];
                            } else {
                                $studentID = $student["identifier"] != "" ? $student["identifier"] : $student["sourcedId"];
                            }
                        } else {
                            $studentID = $student["identifier"] != "" ? $student["identifier"] : $student["sourcedId"];
                        }
                        $studentID = trim($db->escape_string($studentID));

                        $stateId = '';
                        if (isset($student['userIds'])) {
                            foreach ($student['userIds'] as $userId) {
                                if ($userId['type'] === 'stateId') {
                                    $stateId = trim($db->escape_string($userId['identifier']));
                                    break;
                                }
                            }
                        }

                        $firstName = trim($db->escape_string($student["givenName"]));
                        $middleName = trim($db->escape_string($student["middleName"]));
                        $lastName = trim($db->escape_string($student["familyName"]));
                        $email = trim($db->escape_string($student["email"]));
                        $currentGrade = isset($student["grades"][0]) ? trim($db->escape_string($student["grades"][0])) : '';

                        $emailsToImport[] = "('$email', '$studentID', $siteID, $schoolYearID)";

                        if (count($emailsToImport) == MAX_IMPORT_LIMIT) {
                            insertRows($db, $adColumns, $emailsToImport);
                            $emailsToImport = [];
                        }

                        foreach ($student["orgs"] as $building) {
                            if (!isset($schoolsCache[$building["sourcedId"]])) {
                                error_log("DIAGNOSTIC ERROR: School sourcedId '{$building["sourcedId"]}' not found in schoolsCache for student '{$studentID}'");
                                continue;
                            }
                            
                            $schoolCode = $schoolsCache[$building["sourcedId"]]["id"];
                            $schoolCode = trim($db->escape_string($schoolCode));
                            $buildingName = trim($db->escape_string($schoolsCache[$building["sourcedId"]]["name"]));
                            $buildingType = trim($db->escape_string($schoolsCache[$building["sourcedId"]]["type"]));

                            if ($buildingType == "school") {
                                $valuesToImport[] = "(
                                  '$studentID', '$firstName', '$middleName', '$lastName', '$email', '$stateId', '$schoolCode',
                                  '$buildingName', '$currentGrade', 1, $siteID, $schoolYearID
                                )";

                                if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                                    insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE FirstName = VALUES(FirstName), MiddleName = VALUES(MiddleName), LastName = VALUES(LastName), Email = VALUES(Email), SSID = VALUES(SSID), SchoolCode = VALUES(SchoolCode), SchoolName = VALUES(SchoolName), CurrentGrade = VALUES(CurrentGrade)");
                                    $valuesToImport = [];
                                }
                            }
                        }

                        $usersCache[$student["sourcedId"]] = [
                            "localId" => $studentID,
                            "firstName" => $student["givenName"],
                            "middleName" => $student["middleName"],
                            "lastName" => $student["familyName"]
                        ];

                        // Add students to data collection and stream immediately
                        $studentsData[] = $student;
                        $streamingUploader->addRecord('students', $student);
                    }
                }
            }
            $offset += $limit;
        } while (isset($students["users"]) && count($students["users"]));

        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE FirstName = VALUES(FirstName), MiddleName = VALUES(MiddleName), LastName = VALUES(LastName), Email = VALUES(Email), SSID = VALUES(SSID), SchoolCode = VALUES(SchoolCode), SchoolName = VALUES(SchoolName), CurrentGrade = VALUES(CurrentGrade)");
        }
        if (count($emailsToImport)) {
            insertRows($db, $adColumns, $emailsToImport);
        }

        // Upload students data to GCS
        if (!empty($studentsData)) {
            _uploadToGCS($studentsData, 'students', $bucket, $currentDate, $siteID);
        }

        // Finalize streaming upload for students
        $streamingUploader->finalize('students');

        // Memory cleanup after students section
        $students = null;
        unset($studentBatch);
        unset($dbColumns);
        unset($adColumns);
        unset($deleteRecords);
        gc_collect_cycles();
        error_log("Memory cleanup completed after students section");
        error_log("done with students");

        if ($needsSpecialProcessing) {
            error_log("DIAGNOSTIC: Using special processing with simplified cURL API - will process ALL users first, then ALL enrollments");
            
            // Use simplified cURL-based API for special processing sites
            $api = new SimpleCurlOneRosterAPI($config);
            
            // Process ALL Users first to fully populate classUniqueArray
            processAllUsersFirst($db, $api, $config, $siteID, $schoolYearID, $jobStartTime, $schoolsCache, $usersCache, $classUniqueArray, $teacherCache, $streamingUploader);
            
            // Then process ALL Enrollments with complete classUniqueArray
            processAllEnrollments($db, $api, $config, $siteID, $schoolYearID, $schoolsCache, $usersCache, $classCache, $classUniqueArray, $gradingPeriods, $streamingUploader);
            
            // Special processing complete - skip regular processing to avoid duplicates
            error_log("SPECIAL PROCESSING: Complete - skipping regular processing to avoid duplicates");
            
            // Final memory cleanup
            logMemoryUsage("before_final_cleanup");
            $schoolsCache = null;
            $usersCache = null;
            $classCache = null;
            $classUniqueArray = null;
            $teacherCache = null;
            $gradingPeriods = null;
            gc_collect_cycles();
            logMemoryUsage("after_final_cleanup");
            error_log("Final comprehensive memory cleanup completed");
            
            // Exit here to prevent regular processing from running and creating duplicates
            return;
            
        } else {
            error_log("DIAGNOSTIC: Using standard processing");
            
            error_log("starting staff");
            // Initial memory monitoring
            logMemoryUsage("before staff processing");
            
            $limit = 1000;
            $offset = 0;
            $batchSize = 500; // Process in smaller chunks
            $processedCount = 0;
            $staffBatch = [];
            
            $dbColumns = "INSERT INTO Abre_Staff
                  (StaffID, FirstName, MiddleName, LastName, EMail1,
                    is_imported, imported_on, siteID, school_year_id)
                  VALUES ";

            $emptyStringEncrypted = encrypt("", $config->dbKey->iv, $config->dbKey->key);


        $maxIterations = calculateMaxIterations('users', $limit);
        $iteration = 0;
        $consecutiveEmpty = 0;
        
        do {
            if (++$iteration > $maxIterations) {
                error_log("WARNING: Users pagination reached maximum iterations ($maxIterations). This may indicate all data was processed.");
                break;
            }
            
            error_log("DIAGNOSTIC: Starting users API call at offset $offset, iteration $iteration");
            $users = $api->getWithRetry("ims/oneroster/v1p1/users", [
                "offset" => $offset,
                "limit" => $limit
            ], 5); // Use retry logic with 5 attempts
            error_log("DIAGNOSTIC: Users API call completed at offset $offset");
            
            // DIAGNOSTIC LOGGING - Add logging to validate pagination and token refresh
            if (isset($users["users"])) {
                error_log("DIAGNOSTIC: Users API returned " . count($users["users"]) . " users at offset $offset (consecutive failures: " . $api->getConsecutiveFailures() . ")");
            } else {
                error_log("DIAGNOSTIC: Users API returned no users array at offset $offset - this could indicate auth failure (consecutive failures: " . $api->getConsecutiveFailures() . ")");
            }
            
            if (isset($users["users"]) && count($users["users"])) {
                $consecutiveEmpty = 0; // Reset counter on successful data
                foreach ($users["users"] as $user) {
                    $staffBatch[] = $user;
                    $processedCount++;

                    // Process in smaller batches to reduce memory usage
                    if (count($staffBatch) >= $batchSize) {
                        logMemoryUsage("before processing batch of $batchSize staff");
                        processStaffBatch($db, $staffBatch, $schoolsCache, $siteID, $schoolYearID,
                                        $dbColumns, $emptyStringEncrypted, $api,
                                        $usersCache, $classUniqueArray, $teacherCache, $streamingUploader);

                        // Memory cleanup after each batch
                        $staffBatch = [];
                        unset($staffBatch);
                        $staffBatch = [];
                        
                        // Periodic cache cleanup every 10 batches to prevent memory buildup
                        if ($processedCount % (10 * $batchSize) == 0) {
                            $cacheSize = count($classUniqueArray);
                            $teacherCacheSize = count($teacherCache);
                            error_log("Performing periodic cache cleanup - classUniqueArray: $cacheSize, teacherCache: $teacherCacheSize");
                            
                            // Keep only recent entries (last 2000 for each cache)
                            if ($cacheSize > 2000) {
                                $classUniqueArray = array_slice($classUniqueArray, -2000, null, true);
                            }
                            if ($teacherCacheSize > 2000) {
                                $teacherCache = array_slice($teacherCache, -2000, null, true);
                            }
                            
                            gc_collect_cycles();
                            error_log("Cache cleanup completed");
                        }
                        
                        gc_collect_cycles();
                        logMemoryUsage("after processing batch of $batchSize staff");
                    }

                    // Staff are now streamed immediately in processStaffBatch function
                }
                $offset += $limit;
            } else {
                $consecutiveEmpty++;
                error_log("DIAGNOSTIC: No users returned at offset $offset (consecutiveEmpty=$consecutiveEmpty)");
                
                if ($consecutiveEmpty >= 5) { // Increased from 3 to 5 for better resilience
                    error_log("Users API returned empty results 5 times consecutively. Assuming end of data.");
                    break;
                }
                
                // Reinitialize API connection after consecutive failures to reset SSL state
                if ($consecutiveEmpty >= 2) {
                    error_log("DIAGNOSTIC: Reinitializing API connection after $consecutiveEmpty consecutive failures");
                    try {
                        $api = new OneRosterAPIWithTokenRotation(
                            OneRosterAPI::init(
                                $config->oneRoster->service,
                                $config->oneRoster->clientID,
                                $config->oneRoster->clientSecret,
                                $config->oneRoster->baseUri,
                                $config->oneRoster->authUri
                            ),
                            $config
                        );
                        error_log("DIAGNOSTIC: API connection reinitialized successfully");
                    } catch (Exception $e) {
                        error_log("WARNING: Failed to reinitialize API connection: " . $e->getMessage());
                    }
                }
                
                // Add a small delay before retrying to help with server issues
                if ($consecutiveEmpty > 1) {
                    sleep(3); // Increased delay after reinitializing
                }
                
                $offset += $limit;
            }
        } while (isset($users["users"]) && count($users["users"]));

        // Process any remaining staff in the final batch
        if (!empty($staffBatch)) {
            logMemoryUsage("before processing final batch of " . count($staffBatch) . " staff");
            processStaffBatch($db, $staffBatch, $schoolsCache, $siteID, $schoolYearID,
                            $dbColumns, $emptyStringEncrypted, $api,
                            $usersCache, $classUniqueArray, $teacherCache, $streamingUploader);
            logMemoryUsage("after processing final batch");
        }

        //remove building relationships for staff no longer in import file
        $deleteOldBuildings = "DELETE FROM abre_staff_schools
                           WHERE site_id = ? AND is_imported = 1
                             AND imported_on < ?";
        $deleteOldStmt = $db->stmt_init();
        $deleteOldStmt->prepare($deleteOldBuildings);
        $deleteOldStmt->bind_param("is", $siteID, $jobStartTime);
        $deleteOldStmt->execute();
        $deleteOldStmt->close();

        //archive staff that are no longer in the import file
        $archiveOldStaff = "UPDATE Abre_Staff SET is_archived = 1
                        WHERE siteID = ? AND is_imported = 1
                          AND imported_on < ? AND school_year_id = ?";
        $archiveStmt = $db->stmt_init();
        $archiveStmt->prepare($archiveOldStaff);
        $archiveStmt->bind_param("isi", $siteID, $jobStartTime, $schoolYearID);
        $archiveStmt->execute();
        $archiveStmt->close();

        //ensure the archive flags in the staff table are consistent with the import
        //flags in the directory
        $directoryArchiveUpdate = "UPDATE directory d
                                JOIN (
                                  SELECT EMail1, siteID, MIN(is_archived) isArchived
                                    FROM Abre_Staff
                                  WHERE siteID = ? AND is_imported = 1 AND school_year_id = ?
                                  GROUP BY EMail1, siteID
                                ) staff
                                ON staff.EMail1 = d.email AND staff.siteID = d.siteID
                                SET d.archived = staff.isArchived";
        $directoryArchiveStmt = $db->stmt_init();
        $directoryArchiveStmt->prepare($directoryArchiveUpdate);
        $directoryArchiveStmt->bind_param("ii", $siteID, $schoolYearID);
        $directoryArchiveStmt->execute();
        $directoryArchiveStmt->close();

        // Finalize streaming upload for staff
        $streamingUploader->finalize('staff');

        // Memory cleanup after staff section
        $users = null;
        unset($dbColumns);
        unset($emptyStringEncrypted);
        unset($staffBatch);
        unset($processedCount);
        unset($batchSize);
        
        // Final cache cleanup after staff processing
        error_log("Final staff cache cleanup - classUniqueArray: " . count($classUniqueArray) . ", teacherCache: " . count($teacherCache));
        
        gc_collect_cycles();
        logMemoryUsage("after staff section cleanup");
        error_log("Memory cleanup completed after staff section");
        error_log("done with staff");

        error_log("starting courses");
        // Initial memory monitoring
        logMemoryUsage("before courses processing");
        
        $limit = 1000;
        $offset = 0;
        $batchSize = 500; // Process in smaller chunks
        $processedCount = 0;
        $courseBatch = [];


        $maxIterations = calculateMaxIterations('courses', $limit);
        $iteration = 0;
        $consecutiveEmpty = 0;
        
        do {
            if (++$iteration > $maxIterations) {
                error_log("WARNING: Courses pagination reached maximum iterations ($maxIterations). This may indicate all data was processed.");
                break;
            }
            
            $courses = $api->get("ims/oneroster/v1p1/courses", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            
            if (isset($courses["courses"]) && count($courses["courses"])) {
                $consecutiveEmpty = 0; // Reset counter on successful data
                foreach ($courses["courses"] as $course) {
                    $courseBatch[] = $course;
                    $processedCount++;

                    // Process in smaller batches to reduce memory usage
                    if (count($courseBatch) >= $batchSize) {
                        logMemoryUsage("before processing batch of $batchSize courses");
                        processCourseBatch($db, $courseBatch, $courseCache, $streamingUploader);

                        // Memory cleanup after each batch
                        $courseBatch = [];
                        unset($courseBatch);
                        $courseBatch = [];
                        gc_collect_cycles();

                        logMemoryUsage("after processing batch of $batchSize courses");
                    }

                    // Courses are now streamed immediately in processCourseBatch function
                }
                $offset += $limit;
            } else {
                $consecutiveEmpty++;
                if ($consecutiveEmpty >= 3) {
                    error_log("Courses API returned empty results 3 times consecutively. Assuming end of data.");
                    break;
                }
                $offset += $limit;
            }
        } while (isset($courses["courses"]) && count($courses["courses"]));

        // Process any remaining courses in the final batch
        if (!empty($courseBatch)) {
            logMemoryUsage("before processing final batch of " . count($courseBatch) . " courses");
            processCourseBatch($db, $courseBatch, $courseCache, $streamingUploader);
            logMemoryUsage("after processing final batch");
        }

        // Finalize streaming upload for courses
        $streamingUploader->finalize('courses');

        // Memory cleanup after courses section
        $courses = null;
        unset($courseBatch);
        gc_collect_cycles();
        error_log("Memory cleanup completed after courses section");
        error_log("done with courses");

        error_log("starting classes");
        // Initial memory monitoring
        logMemoryUsage("before classes processing");
        
        $limit = 1000;
        $offset = 0;
        $deleteRecords = true;
        $batchSize = 500; // Process in smaller chunks
        $processedCount = 0;
        $classBatch = [];
        
        $dbColumns = "INSERT INTO Abre_Courses
                    (SchoolCode, CourseCode, SectionCode, StaffID, TermCode, Period, siteID, school_year_id)
                  VALUES ";


        $maxIterations = calculateMaxIterations('classes', $limit);
        $iteration = 0;
        $consecutiveEmpty = 0;
        
        do {
            if (++$iteration > $maxIterations) {
                error_log("WARNING: Classes pagination reached maximum iterations ($maxIterations). This may indicate all data was processed.");
                break;
            }
            
            $classes = $api->get("ims/oneroster/v1p1/classes", [
                "offset" => $offset,
                "limit" => $limit
            ]);
            
            if (isset($classes["classes"]) && count($classes["classes"])) {
                $consecutiveEmpty = 0; // Reset counter on successful data
                if ($deleteRecords) {
                    // Start transaction for class deletion and insertion
                    $db->autocommit(false);
                    try {
                        $deleteSql = "DELETE FROM Abre_Courses WHERE siteID = ? AND school_year_id = ?";
                        $deleteStmt = $db->stmt_init();
                        $deleteStmt->prepare($deleteSql);
                        $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                        $deleteStmt->execute();
                        $deleteStmt->close();

                        // Commit the deletion
                        $db->commit();
                        $db->autocommit(true);
                        $deleteRecords = false;
                    } catch (Exception $e) {
                        // Rollback on failure
                        $db->rollback();
                        $db->autocommit(true);
                        error_log("TRANSACTION ERROR: Class deletion failed - " . $e->getMessage());
                        throw $e;
                    }
                }

                foreach ($classes["classes"] as $class) {
                    $classBatch[] = $class;
                    $processedCount++;

                    // Process in smaller batches to reduce memory usage
                    if (count($classBatch) >= $batchSize) {
                        logMemoryUsage("before processing batch of $batchSize classes");
                        processClassBatch($db, $classBatch, $schoolsCache, $courseCache, $teacherCache,
                                        $gradingPeriods, $siteID, $schoolYearID, $dbColumns, $api, $classCache, $streamingUploader);

                        // Memory cleanup after each batch
                        $classBatch = [];
                        unset($classBatch);
                        $classBatch = [];
                        gc_collect_cycles();

                        logMemoryUsage("after processing batch of $batchSize classes");
                    }

                    // Classes are now streamed immediately in processClassBatch function
                }
                $offset += $limit;
            } else {
                $consecutiveEmpty++;
                if ($consecutiveEmpty >= 3) {
                    error_log("Classes API returned empty results 3 times consecutively. Assuming end of data.");
                    break;
                }
                $offset += $limit;
            }
        } while (isset($classes["classes"]) && count($classes["classes"]));

        // Process any remaining classes in the final batch
        if (!empty($classBatch)) {
            logMemoryUsage("before processing final batch of " . count($classBatch) . " classes");
            processClassBatch($db, $classBatch, $schoolsCache, $courseCache, $teacherCache,
                            $gradingPeriods, $siteID, $schoolYearID, $dbColumns, $api, $classCache, $streamingUploader);
            logMemoryUsage("after processing final batch");
        }

        // Finalize streaming upload for classes
        $streamingUploader->finalize('classes');

        // Memory cleanup after classes section
        $classes = null;
        unset($classBatch);
        unset($dbColumns);
        unset($deleteRecords);
        gc_collect_cycles();
        error_log("Memory cleanup completed after classes section");
        error_log("done with classes");

        error_log("starting enrollments");
        
        // Perform API health check before critical enrollments section
        try {
            $api->validateAPIHealth();
        } catch (Exception $e) {
            error_log("CRITICAL: API health check failed before enrollments: " . $e->getMessage());
            throw $e;
        }
        
        // Initialize checkpoint system
        $checkpoint = new ProcessingCheckpoint($siteID);
        $enrollmentCheckpoint = $checkpoint->load();
        
        if ($enrollmentCheckpoint && $enrollmentCheckpoint['section'] === 'enrollments') {
            error_log("RESUMING: Found enrollment checkpoint at offset {$enrollmentCheckpoint['offset']}");
            $offset = $enrollmentCheckpoint['offset'];
            $processedCount = $enrollmentCheckpoint['processedCount'];
        } else {
            $offset = 0;
            $processedCount = 0;
        }

        $limit = 1000;
        $deleteRecords = ($offset == 0); // Only delete if starting from beginning
        $staffDbColumns = "INSERT INTO Abre_StaffSchedules
                      (StaffID, SchoolCode, CourseCode, SectionCode, TermCode, Period,
                        CourseName, TeacherName, siteID, school_year_id)
                      VALUES ";
        $studentDbColumns = "INSERT INTO Abre_StudentSchedules
                        (StudentID, FirstName, LastName, SchoolCode, CourseCode, SectionCode,
                          CourseName, StaffId, TeacherName, TermCode, Period, siteID, school_year_id)
                        VALUES ";

        // Initial memory monitoring
        logMemoryUsage("before enrollments processing");

        $batchSize = 500; // Process in smaller chunks
        $enrollmentBatch = [];
        $checkpointInterval = 5000; // Save checkpoint every 5000 records

        $maxIterations = calculateMaxIterations('enrollments', $limit);
        $iteration = 0;
        $consecutiveEmpty = 0;
        
        do {
            if (++$iteration > $maxIterations) {
                error_log("WARNING: Enrollments pagination reached maximum iterations ($maxIterations). This may indicate all data was processed.");
                break;
            }
            
            $enrollments = $api->getWithRetry("ims/oneroster/v1p1/enrollments", [
                "offset" => $offset,
                "limit" => $limit
            ], 5); // Use retry logic with 5 attempts

            // DIAGNOSTIC LOGGING - Add logging to validate pagination and token refresh
            if (isset($enrollments["enrollments"])) {
                error_log("DIAGNOSTIC: Enrollments API returned " . count($enrollments["enrollments"]) . " enrollments at offset $offset (consecutive failures: " . $api->getConsecutiveFailures() . ")");
                if (isset($enrollments["meta"])) {
                    error_log("DIAGNOSTIC: Enrollments API meta data: " . json_encode($enrollments["meta"]));
                }
            } else {
                error_log("DIAGNOSTIC: Enrollments API returned no enrollments array at offset $offset - this could indicate auth failure (consecutive failures: " . $api->getConsecutiveFailures() . ")");
            }

            if (isset($enrollments["enrollments"]) && count($enrollments["enrollments"])) {
                $consecutiveEmpty = 0; // Reset counter on successful data
                if ($deleteRecords) {
                    // Start transaction for staff/student schedule deletion and insertion
                    $db->autocommit(false);
                    try {
                        $deleteSql = "DELETE FROM Abre_StaffSchedules WHERE siteID = ? AND school_year_id = ?";
                        $deleteStmt = $db->stmt_init();
                        $deleteStmt->prepare($deleteSql);
                        $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                        $deleteStmt->execute();
                        $deleteStmt->close();

                        $deleteSql = "DELETE FROM Abre_StudentSchedules WHERE siteID = ? AND school_year_id = ?";
                        $deleteStmt = $db->stmt_init();
                        $deleteStmt->prepare($deleteSql);
                        $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
                        $deleteStmt->execute();
                        $deleteStmt->close();

                        // Commit the deletions
                        $db->commit();
                        $db->autocommit(true);
                        $deleteRecords = false;
                    } catch (Exception $e) {
                        // Rollback on failure
                        $db->rollback();
                        $db->autocommit(true);
                        error_log("TRANSACTION ERROR: Staff/Student schedule deletion failed - " . $e->getMessage());
                        throw $e;
                    }
                }

                foreach ($enrollments["enrollments"] as $enrollmentRecord) {
                    $enrollmentBatch[] = $enrollmentRecord;
                    $processedCount++;

                    // Process in smaller batches to reduce memory usage
                    if (count($enrollmentBatch) >= $batchSize) {
                        logMemoryUsage("before processing batch of $batchSize enrollments");
                        processEnrollmentBatch($db, $enrollmentBatch, $schoolsCache, $usersCache, $classCache,
                                             $classUniqueArray, $gradingPeriods, $siteID, $schoolYearID,
                                             $staffDbColumns, $studentDbColumns, $api, $streamingUploader);

                        // Memory cleanup after each batch
                        $enrollmentBatch = [];
                        unset($enrollmentBatch);
                        $enrollmentBatch = [];
                        gc_collect_cycles();

                        logMemoryUsage("after processing batch of $batchSize enrollments");
                    }
                    
                    // Save checkpoint periodically
                    if ($processedCount % $checkpointInterval == 0) {
                        // Safe database queries with error handling
                        try {
                            $staffResult = $db->query("SELECT COUNT(*) as count FROM Abre_StaffSchedules WHERE siteID = $siteID AND school_year_id = $schoolYearID");
                            $studentResult = $db->query("SELECT COUNT(*) as count FROM Abre_StudentSchedules WHERE siteID = $siteID AND school_year_id = $schoolYearID");
                            
                            $staffCount = $staffResult ? $staffResult->fetch_assoc()['count'] : 0;
                            $studentCount = $studentResult ? $studentResult->fetch_assoc()['count'] : 0;
                            
                            $checkpoint->save('enrollments', $offset, $processedCount, [
                                'staffSchedulesCount' => $staffCount,
                                'studentSchedulesCount' => $studentCount
                            ]);
                        } catch (Exception $e) {
                            error_log("WARNING: Failed to save checkpoint: " . $e->getMessage());
                        }
                    }

                    // Enrollments are now streamed immediately in processEnrollmentBatch function
                }
                $offset += $limit;
            } else {
                $consecutiveEmpty++;
                error_log("DIAGNOSTIC: No enrollments returned at offset $offset (consecutiveEmpty=$consecutiveEmpty)");
                
                if ($consecutiveEmpty >= 5) { // Increased from 3 to 5 for better resilience
                    error_log("Enrollments API returned empty results 5 times consecutively. Assuming end of data.");
                    break;
                }
                
                // Reinitialize API connection after consecutive failures to reset SSL state
                if ($consecutiveEmpty >= 2) {
                    error_log("DIAGNOSTIC: Reinitializing API connection after $consecutiveEmpty consecutive failures");
                    try {
                        $api = new OneRosterAPIWithTokenRotation(
                            OneRosterAPI::init(
                                $config->oneRoster->service,
                                $config->oneRoster->clientID,
                                $config->oneRoster->clientSecret,
                                $config->oneRoster->baseUri,
                                $config->oneRoster->authUri
                            ),
                            $config
                        );
                        error_log("DIAGNOSTIC: API connection reinitialized successfully");
                    } catch (Exception $e) {
                        error_log("WARNING: Failed to reinitialize API connection: " . $e->getMessage());
                    }
                }
                
                // Add a small delay before retrying to help with server issues
                if ($consecutiveEmpty > 1) {
                    sleep(3); // Increased delay after reinitializing
                }
                
                $offset += $limit;
            }
        } while (isset($enrollments["enrollments"]) && count($enrollments["enrollments"]));

        // Process any remaining enrollments in the final batch
        if (!empty($enrollmentBatch)) {
            logMemoryUsage("before processing final batch of " . count($enrollmentBatch) . " enrollments");
            processEnrollmentBatch($db, $enrollmentBatch, $schoolsCache, $usersCache, $classCache,
                                 $classUniqueArray, $gradingPeriods, $siteID, $schoolYearID,
                                 $staffDbColumns, $studentDbColumns, $api, $streamingUploader);
            logMemoryUsage("after processing final batch");
        }
        
        // Log final counts for verification
        $staffCount = $db->query("SELECT COUNT(*) as count FROM Abre_StaffSchedules WHERE siteID = $siteID AND school_year_id = $schoolYearID")->fetch_assoc()['count'];
        $studentCount = $db->query("SELECT COUNT(*) as count FROM Abre_StudentSchedules WHERE siteID = $siteID AND school_year_id = $schoolYearID")->fetch_assoc()['count'];
        
        // Get streaming upload counts for comparison
        $enrollmentStreamCount = $streamingUploader->getRecordCount('enrollments');
        
        error_log("DIAGNOSTIC: Final database counts - Staff Schedules: $staffCount, Student Schedules: $studentCount");
        error_log("DIAGNOSTIC: Total enrollments processed via streaming: $enrollmentStreamCount");
        error_log("DIAGNOSTIC: API consecutive failures during enrollment processing: " . $api->getConsecutiveFailures());
        error_log("DIAGNOSTIC: Total enrollment iterations completed: $iteration of max $maxIterations");
        
        // Clear checkpoint on successful completion
        $checkpoint->clear();
        
        // Finalize streaming upload for enrollments
        $streamingUploader->finalize('enrollments');

        // Memory cleanup after enrollments section
        $enrollments = null;
        unset($valuesToImport);
        unset($staffDbColumns);
        unset($studentDbColumns);
        unset($deleteRecords);
        unset($userSourcedId);
        unset($classSourcedId);
        unset($userID);
        unset($firstName);
        unset($lastName);
        unset($fullName);
        unset($courseSourcedId);
        unset($terms);
        unset($periods);
        unset($sectionCode);
        unset($courseName);
        unset($courseCode);
        unset($schoolCode);
        unset($staffID);
        unset($teacherName);
        unset($escapedPeriod);
        unset($cachedTerms);
        unset($escapedTermCode);
        unset($escapedTerm);
        unset($classRecord);
        unset($courseRecord);
        gc_collect_cycles();
        error_log("Memory cleanup completed after enrollments section");
        error_log("done with enrollments");
        
        } // End of standard processing else block

        // Final comprehensive memory cleanup with PHP context release
        error_log("Starting final memory cleanup");
        logMemoryUsage("before_final_cleanup");
        
        // Break large cache references explicitly
        $schoolsCache = null;
        $gradingPeriods = null; 
        $usersCache = null;
        $teacherCache = null;
        $classUniqueArray = null;
        $classCache = null;
        $courseCache = null;
        
        // Clean up objects
        $storage = null;
        $bucket = null;
        $streamingUploader = null;
        $baseApi = null;
        $api = null;
        
        // Force PHP garbage collection
        gc_collect_cycles();
        
        logMemoryUsage("after_final_cleanup");
        error_log("Final comprehensive memory cleanup completed");
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
