<?php

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

function runJob($db, $siteID, $config)
{

    $cronName = 'Abre Notion Sync';

    try {

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        // Notion API token
        $token = $config->notion->key;

        // Notion Database ID
        $databaseId = 'b56b684658db440882d26e7218a4cf3e';

        // Create a new page in the database
        function createPage($token, $databaseId, $pageTitle, $pageDomain, $pageCategory, $pageSiteId, $pageSalesforceId, $pageActiveStatus)
        {
            $data = [
                'parent' => [
                    'database_id' => $databaseId
                ],
                'properties' => [
                    'District Name' => [
                        'title' => [
                            [
                                'text' => [
                                    'content' => $pageTitle
                                ]
                            ]
                        ]
                    ],
                    'Domain' => [
                        'url' => $pageDomain
                    ],
                    'Category' => [
                        'rich_text' => [
                            [
                                'text' => [
                                    'content' => $pageCategory
                                ]
                            ]
                        ]
                    ],
                    'Site ID' => [
                        'number' => $pageSiteId
                    ],
                    'Salesforce ID' => [
                        'rich_text' => [
                            [
                                'text' => [
                                    'content' => $pageSalesforceId
                                ]
                            ]
                        ]
                    ],
                    'Active Status' => [
                        'number' => $pageActiveStatus
                    ]
                ]
            ];
            $jsonData = json_encode($data);
            $ch = curl_init("https://api.notion.com/v1/pages");
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $token,
                'Content-Type: application/json',
                'Notion-Version: 2022-06-28'
            ]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            $response = curl_exec($ch);
            curl_close($ch);
            return json_decode($response, true);
        }

        // Get all pages from the database
        function getNotionPages($databaseId, $notionApiKey)
        {
            $url = "https://api.notion.com/v1/databases/$databaseId/query";

            $headers = [
                "Authorization: Bearer $notionApiKey",
                "Content-Type: application/json",
                "Notion-Version: 2022-06-28"
            ];

            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                'page_size' => 50
            ]));

            $response = curl_exec($ch);

            $responseData = json_decode($response, true);
            $pages = $responseData['results'];

            while (isset($responseData['next_cursor'])) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
                    'page_size' => 50,
                    'start_cursor' => $responseData['next_cursor']
                ]));

                $response = curl_exec($ch);

                $responseData = json_decode($response, true);
                $pages = array_merge($pages, $responseData['results']);
            }

            $domains = [];
            foreach ($pages as $page) {
                $domainUrl = $page['properties']['Domain']['url'];
                $domains[] = [
                    'id' => $page['id'],
                    'url' => $domainUrl
                ];
            }
            curl_close($ch);

            return $domains;
        }

        // Get all pages from the database
        $pagesDomains = getNotionPages($databaseId, $token);

        function updateNotionPage($pageId, $notionApiKey, $databaseId, $pageTitle, $pageDomain, $pageCategory, $pageSiteId, $pageSalesforceId, $pageActiveStatus)
        {
            $url = "https://api.notion.com/v1/pages/$pageId";

            $headers = [
                "Authorization: Bearer $notionApiKey",
                "Content-Type: application/json",
                "Notion-Version: 2022-06-28"
            ];

            $data = [
                'parent' => [
                    'database_id' => $databaseId
                ],
                'properties' => [
                    'District Name' => [
                        'title' => [
                            [
                                'text' => [
                                    'content' => $pageTitle
                                ]
                            ]
                        ]
                    ],
                    'Domain' => [
                        'url' => $pageDomain
                    ],
                    'Category' => [
                        'rich_text' => [
                            [
                                'text' => [
                                    'content' => $pageCategory
                                ]
                            ]
                        ]
                    ],
                    'Site ID' => [
                        'number' => $pageSiteId
                    ],
                    'Salesforce ID' => [
                        'rich_text' => [
                            [
                                'text' => [
                                    'content' => $pageSalesforceId
                                ]
                            ]
                        ]
                    ],
                    'Active Status' => [
                        'number' => $pageActiveStatus
                    ]
                ]
            ];

            $ch = curl_init();

            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                echo 'Error:' . curl_error($ch);
            } else {
                $result = json_decode($response, true);
                return $result;
            }
            curl_close($ch);
        }

        // Get all pages from the database
        $pagesDomains = getNotionPages($databaseId, $token);

        // Query DB to get all sites
        $query = "SELECT * FROM abre_tenant_map";
        $result = $db->query($query);
        while ($row = $result->fetch_assoc()) {
            // Access the data from each row
            $name = $row['district_name'];
            $domain = $row['domain'];
            $category = $row['category'];
            $siteIdInsert = intval($row['siteID']);
            $salesforceId = $row['salesforce_id'];
            $active = intval($row['active']);

            //Add new page if it doesn't exist
            if (!in_array($domain, array_column($pagesDomains, 'url'))) {
                createPage($token, $databaseId, $name, $domain, $category, $siteIdInsert, $salesforceId, $active);
            } else {

                // Find the page id that matches the domain
                $matchingPage = array_filter($pagesDomains, function ($page) use ($domain) {
                    return $page['url'] === $domain;
                });

                if (!empty($matchingPage)) {
                    $pageId = reset($matchingPage)['id'];
                    updateNotionPage($pageId, $token, $databaseId, $name, $domain, $category, $siteIdInsert, $salesforceId, $active);
                }
            }
        }
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
