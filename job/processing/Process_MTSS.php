<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../utils/Mtss.php'); 

function runJob($db, $siteId, $config){
$cronName = 'Processing MTSS';
    try{
		$uuid = Logger::logCronStart($db, $siteId, $cronName);

		$mtss = new Mtss();
		$schoolYearId = getCurrentSchoolYearID($db);

		//get indicator data arrays from DB
		$indicatorConfig = $mtss->getMtssIndicatorConfig($siteId,$db); 
	
		//set indicator vairables (these are based on indicatorConfig array indexes)
		$lowPerformingCoursesSql = $indicatorConfig[0];
		$assessmentConfig = $indicatorConfig[1];
		$iReadyActive = $assessmentConfig['iready'] == 1 ? 1 : 0; 
		$mapActive = $assessmentConfig['map'] == 1 ? 1 : 0; 
		$iReadyData = [];
		$mapData = [];

		//get positive behaviors for SQL;
		$postiveBehaviorsSql = $mtss->getPositveBehaviorOffenses($siteId, $schoolYearId, $db); 

		// get all students, hours missed, behavior incidents, and thereshholds for a given site
		$studentSql = "SELECT DISTINCT
						abs.StudentId,
						abs.SSID,
						mcc.wellness,
						mcc.behavior,
						mcc.academic,
						mcc.attendance,
						mic.attendance_value,
						total_hours_missed,
						total_days_missed,
						CASE
							WHEN mcc.attendance = 1 THEN
										CASE
											WHEN total_hours_missed < t.attendance_medium_risk THEN 'Low Risk'
											WHEN total_hours_missed BETWEEN t.attendance_medium_risk AND t.attendance_high_risk THEN 'Medium Risk'
											WHEN total_hours_missed > t.attendance_high_risk THEN 'High Risk'
										END
						END AS attendanceRisk,
						CASE
							WHEN mcc.attendance = 1 THEN
										CASE
											WHEN total_days_missed < t.attendance_medium_risk_days THEN 'Low Risk'
											WHEN total_days_missed BETWEEN t.attendance_medium_risk_days AND t.attendance_high_risk THEN 'Medium Risk'
											WHEN total_days_missed > t.attendance_high_risk_days THEN 'High Risk'
										END
						END AS attendanceRiskDays,
						COALESCE(office_discipline_count, 0) AS office_discipline_count,
						COALESCE(low_performing_courses, 0) AS low_performing_courses,
						COALESCE(average_wellness_response, 0) AS average_wellness_response,
						CASE
							WHEN mcc.behavior = 1 THEN /* check if behavior is active, then run case statement */
								CASE
									WHEN office_discipline_count < t.behavior_medium_risk THEN 'Low Risk'
									WHEN office_discipline_count BETWEEN t.behavior_medium_risk AND t.behavior_high_risk THEN 'Medium Risk'
									WHEN office_discipline_count >= t.behavior_high_risk THEN 'High Risk'
									ELSE 'Low Risk'
								END
							ELSE ''
						END AS behaviorRisk,
						CASE
							WHEN mcc.academic = 1 THEN /* check if academic is active, then dertermine risk level for failing grades, NOTE: assessment risk levels are determined in abstacted function further below */
								CASE
									WHEN low_performing_courses < t.academic_medium_risk THEN 'Low Risk'
									WHEN low_performing_courses BETWEEN t.academic_medium_risk AND t.academic_high_risk THEN 'Medium Risk'
									WHEN low_performing_courses >= t.academic_high_risk THEN 'High Risk'
									ELSE 'Low Risk'
									
								END
							ELSE ''
						END AS academicRisk,
						CASE
							WHEN mcc.wellness = 1 THEN /* check if wellness is active, then run case statement */
								CASE
									WHEN average_wellness_response > t.wellness_medium_risk THEN 'Low Risk'
									WHEN average_wellness_response BETWEEN t.wellness_medium_risk AND t.wellness_high_risk THEN 'Medium Risk'
									WHEN average_wellness_response <= t.wellness_high_risk THEN 'High Risk'
									ELSE 'Low Risk'
								END
							ELSE ''
						END AS wellnessRisk,
						mcc.attendance + mcc.behavior + mcc.academic + mcc.wellness AS total_categories /* determine current total category count, assessment category count is determined furhter below */
						FROM Abre_Students AS abs
						LEFT JOIN Abre_AD AS ad ON ad.StudentID = abs.StudentId
							AND ad.school_year_id = abs.school_year_id
							AND ad.siteID = abs.siteID
						LEFT JOIN (
							SELECT
								StudentID,
								siteID,
								school_year_id,
								COALESCE(SUM(HoursMissed), 0) AS total_hours_missed,
								COALESCE(COUNT(*), 0) AS total_days_missed
							FROM 
								Abre_Attendance 
							WHERE 
								siteID = ? AND school_year_id = ?
							GROUP BY StudentID, siteID, school_year_id
						) AS aa ON aa.StudentID = abs.StudentID
						LEFT JOIN (
						SELECT
							StudentID,
							siteID,
							school_year_id,
							COUNT(*) AS office_discipline_count
						FROM conduct_discipline
						WHERE TYPE = 'Office' AND Archived = 0 AND Served = 1 AND siteID = ? AND school_year_id = ?
								$postiveBehaviorsSql
						GROUP BY StudentID, siteID, school_year_id
						) AS cd ON cd.StudentID = abs.StudentId
						LEFT JOIN (
							SELECT
								student_id,
								site_id,
								school_year_id,
								COALESCE(COUNT(*), 0) AS low_performing_courses
							FROM
								abre_grades 
							WHERE
								site_id = ? AND school_year_id = ?
								AND letter_grade LIKE '%F%'
								$lowPerformingCoursesSql
						GROUP BY student_id, site_id, school_year_id
						) AS ag ON ag.student_id = abs.StudentId
						LEFT JOIN MTSS_indicator_config AS mic ON mic.site_id = abs.siteID
						LEFT JOIN (
						SELECT
							student_id,
							site_id,
							school_year_id,
							ROUND(COALESCE(AVG(q2), 5), 2) AS average_wellness_response
						FROM abre_daily_checkin_results
						WHERE site_id = ? AND school_year_id = ?
						GROUP BY student_id, site_id, school_year_id
						) AS wr ON wr.student_id = abs.StudentId
						LEFT JOIN MTSS_category_configuration AS mcc ON mcc.site_id = abs.siteID
						LEFT JOIN MTSS_thresholds AS t ON t.site_id = abs.siteID
						WHERE abs.siteID = ? AND abs.school_year_id = ?
							AND ad.siteID = ? AND ad.school_year_id = ?";
		$studentStmt = $db->stmt_init();
		$studentStmt->prepare($studentSql);
		$studentStmt->bind_param("iiiiiiiiiiii", $siteId, $schoolYearId, $siteId, $schoolYearId, $siteId, $schoolYearId, $siteId, $schoolYearId, $siteId, $schoolYearId, $siteId, $schoolYearId);
		$studentStmt->execute();
			$studentStmt->bind_result($studentId, $ssId, $wellnssActive, $behaviorActive, $academicActive, $attendanceActive, $attendanceValue, $totalHoursMissed, $totalDaysMissed, $attendanceRiskHours, $attendanceRiskDays, 
									$behaviorIncidents, $lowPerformingCourses, $averageWellnessResponse, $behaviorRisk, $academicRisk, $wellnessRisk, $totalCategories);
		$students = [];

		while($studentStmt->fetch()){
			$students [] = [
			"student_id" => $studentId,
			"ss_id" => $ssId,
			"wellness_active" => $wellnssActive,
			"behavior_active" => $behaviorActive,
			"academic_active" => $academicActive,
			"attendance_active" => $attendanceActive,
			"attendance_value" => $attendanceValue,
			"total_hours_missed" => $totalHoursMissed,
			"total_days_missed" => $totalDaysMissed,
			"attendance_risk_hours" => $attendanceRiskHours,
			"attendance_risk_days" => $attendanceRiskDays,
			"behavior_incidents" => $behaviorIncidents,
			"low_performing_courses" => $lowPerformingCourses,
			"average_wellness_response"=> $averageWellnessResponse,
			"behavior_risk" => $behaviorRisk,
			"academic_risk" => $academicRisk,
			"wellness_risk" => $wellnessRisk,
			"total_categories" => $totalCategories
			];
		}

		$studentStmt->close();
		$valuesToImport = [];

		//set active categories
		$wellnessActive = $students[0]['wellness_active'] ? 1 : 0;
		$behaviorActive = $students[0]['behavior_active'] ? 1 : 0;
		$academicActive = $students[0]['academic_active'] ? 1 : 0;
		$attendanceActive = $students[0]['attendance_active'] ? 1 : 0;

		//set arrays based on if assessments are configured to siteId
		$iReadyData = $iReadyActive ? $mtss->getIreadyData($siteId,$schoolYearId,$db) : [];
		$mapData = $mapActive ? $mtss->getMapData($siteId,$db) : [];

		// series of checks to ensure data is valid
		if ($iReadyActive && !$iReadyData)
		{
			$errorMessage = "Iready active-> ". print_r($iReadyActive) . " Invalid iReady data-> " . print_r($iReadyData,true);
			throw new Exception($errorMessage);
		}

		if ($mapActive && !$mapData) 
		{
			$errorMessage = "MAP active -> ". print_r($mapActive) . " Invalid Map data-> " . print_r($mapData,true);
			throw new Exception($errorMessage);
		}


		//loop thorugh students and dermine risk scores and values for import
		foreach($students as $student){

			//set initials values for import
			$studentID = $student["student_id"];
			$totalCategories = $student['total_categories'];
			$ssID = $student["ss_id"];
			$behaviorIncidents = $student["behavior_incidents"] && $behaviorActive ? intval($student["behavior_incidents"]) : ($behaviorActive ? 0 : "NULL") ;
			$behaviorRisk = $student["behavior_risk"] && $behaviorActive ? strval($student["behavior_risk"]) : ($behaviorActive ? "Low Risk" : "") ;
			$lowPerformingCourses = $student["low_performing_courses"] && $academicActive ? intval($student["low_performing_courses"]) : ($academicActive ? 0 : "NULL");
			$academicRisk = $student["academic_risk"] && $academicActive ? strval($student["academic_risk"]) : ($academicActive ? "Low Risk" : "");

			//set wellness varaibles based on activation and wellness values
			$averageWellnessResponse = $student["average_wellness_response"] && $wellnessActive ? round($student["average_wellness_response"],2) : ($wellnessActive ? 0 : "NULL");
			
			//if no wellness data exists for student, set wellnessActive to 0 and reduce totalCategories by 1
			if($wellnessActive && $averageWellnessResponse == 0){
				$averageWellnessResponse = "NULL";
				$wellnessActive = 0;
				$totalCategories--;
			}
			
			$wellnessRisk = $student["wellness_risk"] && $wellnessActive ? $student["wellness_risk"] : ($wellnessActive ? "Low Risk" : "");

			//set attendance varaibles based on activation and attendance values
			$attendanceValue = $attendanceActive && $student['attendance_value']  ? strval($student['attendance_value']) : "" ;

			//check if iReady data exist for student for studentID or stateID, if not deactive assessment active status 
			$iReadyDataKey = "";
			$studentiReadyActive = $iReadyActive ? 1 : 0;

			if($iReadyActive){
				if (array_key_exists($studentID, $iReadyData)) {
					$iReadyDataKey = $studentID;
				}
				else if (array_key_exists($ssID, $iReadyData)) {
					$iReadyDataKey = $ssID;
				} else {
					$studentiReadyActive = 0;
				}
			}

			//check if MAP data exist for student, if not deactive assessment active status 
			$mapDataKey = "";
			$studentMapActive = $mapActive ? 1 : 0;

			if($mapActive){
				if(array_key_exists($studentID, $mapData)){
					$mapDataKey = $studentID;
				} else if(array_key_exists($ssID, $mapData)){
					$mapDataKey = $ssID;
				} else {
					$studentMapActive = 0;
				}
			}
			
			//set iReady & Map percentiles if confuration is active
			$iReadyElaPercentile = $studentiReadyActive && $iReadyData[$iReadyDataKey]['iready_ela_percentile'] ? $iReadyData[$iReadyDataKey]['iready_ela_percentile'] : 'NULL';
			$iReadyMathPercentile = $studentiReadyActive && $iReadyData[$iReadyDataKey]['iready_math_percentile'] ? $iReadyData[$iReadyDataKey]['iready_math_percentile'] : 'NULL';
			$iReadyElaRisk = $studentiReadyActive && $iReadyData[$iReadyDataKey]['iready_ela_risk'] ? $iReadyData[$iReadyDataKey]['iready_ela_risk'] : '';
			$iReadyMathRisk = $studentiReadyActive &&  $iReadyData[$iReadyDataKey]['iready_math_risk'] ? $iReadyData[$iReadyDataKey]['iready_math_risk'] : '';
			$mapElaPercentile = $studentMapActive && $mapData[$mapDataKey]['map_ela_percentile'] ? $mapData[$mapDataKey]['map_ela_percentile']: 'NULL';
			$mapMathPercentile = $studentMapActive && $mapData[$mapDataKey]['map_math_percentile'] ? $mapData[$mapDataKey]['map_math_percentile'] : 'NULL';
			$mapElaRisk = $studentMapActive && $mapData[$mapDataKey]['map_ela_risk'] ? $mapData[$mapDataKey]['map_ela_risk'] : '';
			$mapMathRisk = $studentMapActive && $mapData[$mapDataKey]['map_math_risk'] ? $mapData[$mapDataKey]['map_math_risk'] : '';

			//calculate total assessment risk categories based on assessments taken
			$assessmentRiskCategories = 0;
			if($studentMapActive){
				$assessmentRiskCategories = $mapMathPercentile ? ++$assessmentRiskCategories : $assessmentRiskCategories;
				$assessmentRiskCategories = $mapElaPercentile ? ++$assessmentRiskCategories : $assessmentRiskCategories;
			}
			
			if($studentiReadyActive){
				$assessmentRiskCategories = $iReadyMathPercentile ? ++$assessmentRiskCategories : $assessmentRiskCategories;
				$assessmentRiskCategories = $iReadyMathPercentile ? ++$assessmentRiskCategories : $assessmentRiskCategories;
			}

			//determine attendance risk and instruction time missed if attendance category is active
			switch (true) {
				case ($attendanceValue == "hours" && $attendanceActive):
					$totalInstructionMissed = $student['total_hours_missed'] ? $student['total_hours_missed'] : 0;
					$attendanceRisk = $student['attendance_risk_hours'] ? $student['attendance_risk_hours'] : "Low Risk";
					break;
				case ($attendanceValue == "days" && $attendanceActive):
					$totalInstructionMissed = $student['total_days_missed'] ? $student['total_days_missed'] : 0;
					$attendanceRisk = $student['attendance_risk_days'] ? $student['attendance_risk_days'] : "Low Risk";
					break; // Add this break statement
				default:
					$totalInstructionMissed = "NULL";
					$attendanceRisk = "";
			}

			//create student risk level array by merging risk categories
			$studentRiskLevels = [$attendanceRisk, $behaviorRisk, $academicRisk, $wellnessRisk];

			//add iready and/or MAP risk scores to studentRiskLevels array if active
			$studentRiskLevels = $iReadyActive ? array_merge($studentRiskLevels, [$iReadyElaRisk, $iReadyMathRisk]): $studentRiskLevels;
			$studentRiskLevels = $mapActive ? array_merge($studentRiskLevels, [$mapElaRisk, $mapMathRisk]): $studentRiskLevels;

			//set risk scount, overallscore and total category values
			$maxRiskCount = 3;
			$overalRiskScore = 0;
			$totalCategories = $totalCategories + $assessmentRiskCategories; //total catogories + assessments

			foreach ($studentRiskLevels as $studentRiskLevel) {
				switch ($studentRiskLevel) {
					case 'Low Risk':
						$overalRiskScore += 1;
						break;
					case 'Medium Risk':
						$overalRiskScore += 2;
						break;
					case 'High Risk':
						$overalRiskScore += 3;
						break;
					default:
				}
			}

			// Calculate the final risk score from 1 to 10 based on the number of risk categories
			$finalRiskScore = (($overalRiskScore / ($maxRiskCount * $totalCategories)) * 10);

			// Ensure the final risk score is between 1 and 10
			$finalRiskScore = round(max(1, min(10, $finalRiskScore)),2);

			//add student values to valuesToImport array
			array_push($valuesToImport, "('$studentID', $totalInstructionMissed, '$attendanceValue', '$attendanceRisk', $behaviorIncidents, '$behaviorRisk', 
				$lowPerformingCourses, '$academicRisk', $averageWellnessResponse, '$wellnessRisk', $iReadyElaPercentile, '$iReadyElaRisk', $iReadyMathPercentile,
				'$iReadyMathRisk', $mapElaPercentile, '$mapElaRisk', $mapMathPercentile, '$mapMathRisk', $schoolYearId,  $siteId, $finalRiskScore)");
		}

		$dbColumns = "INSERT INTO MTSS_calculations
					(student_id, total_instruction_missed, attendance_value, attendance_risk,	
					behavior_incidents, behavior_risk, low_performing_courses, 
					academic_risk, average_wellness_response, wellness_risk, iready_ela_percentile,
					iready_ela_risk, iready_math_percentile, iready_math_risk, map_ela_percentile,
					map_ela_risk, map_math_percentile, map_math_risk, school_year_id, site_id, overall_risk)
					VALUES ";

            if(count($valuesToImport)){
                insertRows($db, $dbColumns, $valuesToImport);
            }

        }catch(Exception $ex){
            $error = $ex->getMessage();
        }

        $details = [];

        if(isset($error) && !is_null($error)){
            $details["error"] = $error;
            $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;

        }else{
            $details = [
                "Process" => "Complete"
            ];
            $status = CRON_SUCCESS;
        }
    	Logger::logCronFinish($db, $siteId, $cronName, $status, $details, $uuid);
    }
?>

