<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

function runJob($db, $siteID, $config)
{
    $cronName = 'Processing Class360';
    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //Get Variables
    $currentSchoolYearID = getCurrentSchoolYearID($db);

    //Delete Existing Data
    $db->query("DELETE FROM processed_class360 WHERE school_year_id = $currentSchoolYearID");

    //Schedule Alignments
    $db->query("INSERT INTO processed_class360 (student_id, staff_id, type, name, term_code, period, site_id, school_year_id)
                  SELECT StudentID, StaffId, 'Schedule' AS Type, CourseName, TermCode, Period, siteID, school_year_id
                  FROM Abre_StudentSchedules
                  WHERE school_year_id = $currentSchoolYearID AND StudentID != '' AND StaffId != '' AND TermCode != ''");

    //Counselor Alignments
    $db->query("INSERT INTO processed_class360 (student_id, staff_id, type, name, term_code, site_id, school_year_id)
                  SELECT StudentId, CounselorStaffId, 'Counselor' AS Type, 'Counselor Roster' AS Name, 'Year' AS TermCode, siteID, school_year_id
                  FROM Abre_Student_Counselors WHERE school_year_id = $currentSchoolYearID AND StudentId != '' AND CounselorStaffId != ''");

    //Custom Group Alignments
    $db->query("INSERT INTO processed_class360 (student_id, staff_id, type, name, term_code, site_id, school_year_id)
                  SELECT sgs.Student_ID, sgs.StaffId, 'Group' AS Type, sg.Name, 'Year' AS TermCode, sg.siteID, '$currentSchoolYearID' AS SchoolYearId
                  FROM students_groups sg
                  LEFT JOIN students_groups_students sgs
                  ON sg.ID = sgs.Group_ID WHERE sgs.Student_ID != '' AND sgs.StaffId != ''");

    //Wellness Alignments
    $db->query("INSERT INTO processed_class360 (student_id, staff_id, type, name, term_code, site_id, school_year_id)
                  SELECT awr.student_id, abs.StaffID, 'Wellness' AS Type, 'Wellness Roster' AS Name, 'Year' AS TermCode, awr.site_id, abs.school_year_id
                  FROM abre_wellness_respondents awr LEFT JOIN Abre_Staff abs ON awr.email = abs.Email1
                  WHERE awr.site_id = abs.siteID AND abs.school_year_id = $currentSchoolYearID AND awr.end_time IS NOT NULL
                  AND awr.student_id != '' AND abs.StaffID != '' AND awr.role = 'staff'");

  }catch(Exception $ex){
        $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "Process" => "Complete"
    ];
    $status = CRON_SUCCESS;
  }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
