<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

function runJob($db, $siteID, $config)
{
    $cronName = 'Josh Stored Procedures';
    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //<PERSON>
    $sql = "CALL proc_daas_early_warning(1)";
    $stmt = $db->prepare($sql);
    $stmt->execute();

    //Wylie ISD
    $sql = "CALL proc_daas_early_warning(2)";
    $stmt = $db->prepare($sql);
    $stmt->execute();

  }catch(Exception $ex){
        $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "Process" => "Complete"
    ];
    $status = CRON_SUCCESS;
  }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
