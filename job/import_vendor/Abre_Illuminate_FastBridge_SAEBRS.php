<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre FastBridge SAEBRS';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "[SAEBRS Student]";

    //Define
    define("SAFE_COLUMN_COUNT", 112);
    define("MAX_IMPORT_LIMIT", 25);

    $error = null;
    $skip = null;
    $separator = "\r\n";
    $currentSchoolYearID = getCurrentSchoolYearID($db);

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO `abre_fastbridge_SAEBRS`
                  (`Assessment`,`Assessment_Language`,`State`,`District`,`School`,
                  `Local_ID`,`State_ID`,`FAST_ID`,`First_Name`,
                  `Last_Name`,`Gender`,`DOB`,`Race`,`Special_Ed_Status`,`Grade`,
                  `Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Total_Items`,`Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Items_Correct`,
                  `Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Social_Total_Items`,`Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Social_Items_Correct`,
                  `Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Academic_Total_Items`,`Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Academic_Items_Correct`,
                  `Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Emotional_Total_Items`,`Quarter_1_SAEBRS_Student_SEBA_SAEBRS_Emotional_Items_Correct`,
                  `Quarter_1_SAEBRS_Student_Percentile_at_School`,`Quarter_1_SAEBRS_Student_Percentile_at_LEA`,
                  `Quarter_1_SAEBRS_Student_Percentile_at_Nation`,`Quarter_1_SAEBRS_Student_Risk_Level`,
                  `Quarter_1_SAEBRS_Student_Final_Date`,`Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Total_Items`,
                  `Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Items_Correct`,`Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Social_Total_Items`,
                  `Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Social_Items_Correct`,`Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Academic_Total_Items`,
                  `Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Academic_Items_Correct`,`Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Emotional_Total_Items`,
                  `Quarter_1_SAEBRS_Teacher_SEBA_SAEBRS_Emotional_Items_Correct`,`Quarter_1_SAEBRS_Teacher_Percentile_at_School`,
                  `Quarter_1_SAEBRS_Teacher_Percentile_at_LEA`,`Quarter_1_SAEBRS_Teacher_Percentile_at_Nation`,
                  `Quarter_1_SAEBRS_Teacher_Risk_Level`,`Quarter_1_SAEBRS_Teacher_Final_Date`,
                  `Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Total_Items`,`Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Items_Correct`,
                  `Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Social_Total_Items`,`Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Social_Items_Correct`,
                  `Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Academic_Total_Items`,`Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Academic_Items_Correct`,
                  `Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Emotional_Total_Items`,`Quarter_2_SAEBRS_Student_SEBA_SAEBRS_Emotional_Items_Correct`,
                  `Quarter_2_SAEBRS_Student_Percentile_at_School`,`Quarter_2_SAEBRS_Student_Percentile_at_LEA`,
                  `Quarter_2_SAEBRS_Student_Percentile_at_Nation`,`Quarter_2_SAEBRS_Student_Risk_Level`,
                  `Quarter_2_SAEBRS_Student_Final_Date`,`Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Total_Items`,
                  `Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Items_Correct`,`Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Social_Total_Items`,
                  `Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Social_Items_Correct`,`Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Academic_Total_Items`,
                  `Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Academic_Items_Correct`,`Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Emotional_Total_Items`,
                  `Quarter_2_SAEBRS_Teacher_SEBA_SAEBRS_Emotional_Items_Correct`,`Quarter_2_SAEBRS_Teacher_Percentile_at_School`,
                  `Quarter_2_SAEBRS_Teacher_Percentile_at_LEA`,`Quarter_2_SAEBRS_Teacher_Percentile_at_Nation`,
                  `Quarter_2_SAEBRS_Teacher_Risk_Level`,`Quarter_2_SAEBRS_Teacher_Final_Date`,
                  `Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Total_Items`,`Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Items_Correct`,
                  `Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Social_Total_Items`,`Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Social_Items_Correct`,
                  `Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Academic_Total_Items`,`Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Academic_Items_Correct`,
                  `Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Emotional_Total_Items`,`Quarter_3_SAEBRS_Student_SEBA_SAEBRS_Emotional_Items_Correct`,
                  `Quarter_3_SAEBRS_Student_Percentile_at_School` ,`Quarter_3_SAEBRS_Student_Percentile_at_LEA` ,
                  `Quarter_3_SAEBRS_Student_Percentile_at_Nation`,`Quarter_3_SAEBRS_Student_Risk_Level`,
                  `Quarter_3_SAEBRS_Student_Final_Date`,`Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Total_Items`,
                  `Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Items_Correct`,`Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Social_Total_Items`,
                  `Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Social_Items_Correct`,`Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Academic_Total_Items`,
                  `Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Academic_Items_Correct`,`Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Emotional_Total_Items`,
                  `Quarter_3_SAEBRS_Teacher_SEBA_SAEBRS_Emotional_Items_Correct`,`Quarter_3_SAEBRS_Teacher_Percentile_at_School` ,
                  `Quarter_3_SAEBRS_Teacher_Percentile_at_LEA`, `Quarter_3_SAEBRS_Teacher_Percentile_at_Nation`,
                  `Quarter_3_SAEBRS_Teacher_Risk_Level`,`Quarter_3_SAEBRS_Teacher_Final_Date`,
                  `Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Total_Items`,`Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Items_Correct`,
                  `Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Social_Total_Items`,`Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Social_Items_Correct`,
                  `Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Academic_Total_Items`,`Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Academic_Items_Correct`,
                  `Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Emotional_Total_Items`,`Quarter_4_SAEBRS_Student_SEBA_SAEBRS_Emotional_Items_Correct`,
                  `Quarter_4_SAEBRS_Student_Percentile_at_School`,`Quarter_4_SAEBRS_Student_Percentile_at_LEA`,
                  `Quarter_4_SAEBRS_Student_Percentile_at_Nation`,
                  `Quarter_4_SAEBRS_Student_Risk_Level`, `Quarter_4_SAEBRS_Student_Final_Date`,`Growth_Score_from_Quarter_1_to_Quarter_4`,
                  `School_Growth_Percentile_from_Quarter_1_to_Quarter_4`, `District_Growth_Percentile_from_Quarter_1_to_Quarter_4`,
                  `school_year_id`, `site_id`)
                  VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM abre_fastbridge_SAEBRS WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
  	do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }
  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$assessment = $data[0] ? trim($db->escape_string($data[0])) : "";
        $assessmentLanguage = $data[1] ? trim($db->escape_string($data[1])) : "";
        $state = $data[2] ? trim($db->escape_string($data[2])) : "";
        $district = $data[3] ? trim($db->escape_string($data[3])) : "";
        $school = $data[4] ? trim($db->escape_string($data[4])) : "";
        $localID = $data[5] ? trim($db->escape_string($data[5])) : "";
        $stateID = $data[6] ? trim($db->escape_string($data[6])) : "";
        $fastID = $data[7] ? trim($db->escape_string($data[7])) : "" ;
        $firstName = $data[8] ? trim($db->escape_string($data[8])) : "";
        $lastName = $data[9] ? trim($db->escape_string($data[9])) : "";
        $gender = $data[10] ? trim($db->escape_string($data[10])) : "";

        //format date type for insert
        if($data[11] !== null){
          $dobRaw = $data[11];
          $dateTimeObject = new DateTime($dobRaw, new DateTimeZone("UTC"));   
          $dobFormatted = $dateTimeObject->format("Y-m-d");
          $dob = "'".$dobFormatted."'";
        } else {
          $dob = "";
        }
        $race = $data[12] ? trim($db->escape_string($data[12])) : "";
        $specialEdStatus = $data[13] ? trim($db->escape_string($data[13])) : "";
        $grade = $data[14] ? trim($db->escape_string($data[14])) : "";

        //NOTE: $data[15] is an empty cell hence why it is skipped

        //QUARTER 1 STUDENT
        $q1StudentTotalItems = is_numeric($data[16]) ? $data[16] : "NULL";
        $q1StudentItemsCorrect = is_numeric($data[17]) ? $data[17] : "NULL";
        $q1StudentSocialTotalItems = is_numeric($data[18]) ? $data[18] : "NULL";
        $q1StudentSocialItemsCorrect = is_numeric($data[19]) ? $data[19] : "NULL";
        $q1StudentAcademicTotalItems = is_numeric($data[20]) ? $data[20] : "NULL";
        $q1StudentAcademicItemsCorrect = is_numeric($data[21]) ? $data[21] : "NULL";
        $q1StudentEmotionalTotalItems = is_numeric($data[22]) ? $data[22] : "NULL";
        $q1StudentEmotionalItemsCorrect = is_numeric($data[23]) ? $data[23] : "NULL";
        $q1StudentPercentileAtSchool = is_numeric($data[24]) ? $data[24] : "NULL";
        $q1StudentPercentileAtLEA = is_numeric($data[25]) ? $data[25] : "NULL";
        $q1StudentPercentileAtNation = is_numeric($data[26]) ? $data[26] : "NULL";
        $q1StudentRiskLevel = $data[27] ? $data[27] : "NULL";

        if($data[28]){  //format date type for insert
          $q1StudentFinalDate = $data[28];
          $q1StudentFinalDate = date('Y-m-d', strtotime($q1StudentFinalDate));
          $q1StudentFinalDate = "'".$q1StudentFinalDate."'";
        } else {
          $q1StudentFinalDate = "NULL";
        }

        //QUARTER 1 TEACHER
        $q1TeacherTotalItems = is_numeric($data[29]) ? $data[29] : "NULL";
        $q1TeacherItemsCorrect = is_numeric($data[30]) ? $data[30] : "NULL";
        $q1TeacherSocialTotalItems = is_numeric($data[31]) ? $data[31] : "NULL";
        $q1TeacherSocialItemsCorrect = is_numeric($data[32]) ? $data[32] : "NULL";
        $q1TeacherAcademicTotalItems = is_numeric($data[33]) ? $data[33] : "NULL";
        $q1TeacherAcademicItemsCorrect = is_numeric($data[34]) ? $data[34] : "NULL";
        $q1TeacherEmotionalTotalItems = is_numeric($data[35]) ? $data[35] : "NULL";
        $q1TeacherEmotionalItemsCorrect = is_numeric($data[36]) ? $data[36] : "NULL";
        $q1TeacherPercentileAtSchool = is_numeric($data[37]) ? $data[37] : "NULL";
        $q1TeacherPercentileAtLEA = is_numeric($data[38]) ? $data[38] : "NULL";
        $q1TeacherPercentileAtNation = is_numeric($data[39]) ? $data[39] : "NULL";
        $q1TeacherRiskLevel = $data[40] ? $data[40] : "NULL";

        if($data[41]){  //format date type for insert
          $q1TeacherFinalDate = $data[41];
          $q1TeacherFinalDate = date('Y-m-d', strtotime($q1TeacherFinalDate));
          $q1TeacherFinalDate = "'".$q1TeacherFinalDate."'";
        } else {
          $q1TeacherFinalDate = "NULL";
        }

        //QUARTER 2 STUDENT
        $q2StudentTotalItems = is_numeric($data[42]) ? $data[42] : "NULL";
        $q2StudentItemsCorrect = is_numeric($data[43]) ? $data[43] : "NULL";
        $q2StudentSocialTotalItems = is_numeric($data[44]) ? $data[44] : "NULL";
        $q2StudentSocialItemsCorrect = is_numeric($data[45]) ? $data[45] : "NULL";
        $q2StudentAcademicTotalItems = is_numeric($data[46]) ? $data[46] : "NULL";
        $q2StudentAcademicItemsCorrect = is_numeric($data[47]) ? $data[47] : "NULL";
        $q2StudentEmotionalTotalItems = is_numeric($data[48]) ? $data[48] : "NULL";
        $q2StudentEmotionalItemsCorrect = is_numeric($data[49]) ? $data[49] : "NULL";
        $q2StudentPercentileAtSchool = is_numeric($data[50]) ? $data[50] : "NULL";
        $q2StudentPercentileAtLEA = is_numeric($data[51]) ? $data[51] : "NULL";
        $q2StudentPercentileAtNation = is_numeric($data[52]) ? $data[52] : "NULL";
        $q2StudentRiskLevel = $data[53] ? $data[53] : "NULL";

        if($data[54]){  //format date type for insert
          $q2StudentFinalDate = $data[54];
          $q2StudentFinalDate = date('Y-m-d', strtotime($q2StudentFinalDate));
          $q2StudentFinalDate = "'".$q2StudentFinalDate."'";
        } else {
          $q2StudentFinalDate = "NULL";
        }

        //QUARTER 2 TEACHER
        $q2TeacherTotalItems = is_numeric($data[55]) ? $data[55] : "NULL";
        $q2TeacherItemsCorrect = is_numeric($data[56]) ? $data[56] : "NULL";
        $q2TeacherSocialTotalItems = is_numeric($data[57]) ? $data[57] : "NULL";
        $q2TeacherSocialItemsCorrect = is_numeric($data[58]) ? $data[58] : "NULL";
        $q2TeacherAcademicTotalItems = is_numeric($data[59]) ? $data[59] : "NULL";
        $q2TeacherAcademicItemsCorrect = is_numeric($data[60]) ? $data[60] : "NULL";
        $q2TeacherEmotionalTotalItems = is_numeric($data[61]) ? $data[61] : "NULL";
        $q2TeacherEmotionalItemsCorrect = is_numeric($data[62]) ? $data[62] : "NULL";
        $q2TeacherPercentileAtSchool = is_numeric($data[63]) ? $data[63] : "NULL";
        $q2TeacherPercentileAtLEA = is_numeric($data[64]) ? $data[64] : "NULL";
        $q2TeacherPercentileAtNation = is_numeric($data[65]) ? $data[65] : "NULL";
        $q2TeacherRiskLevel = $data[66] ? $data[66] : "NULL";

        if($data[67]){  //format date type for insert
          $q2TeacherFinalDate = $data[67];
          $q2TeacherFinalDate = date('Y-m-d', strtotime($q2TeacherFinalDate));
          $q2TeacherFinalDate = "'".$q2TeacherFinalDate."'";
        } else {
          $q2TeacherFinalDate = "NULL";
        }

        //QUARTER 3 STUDENT
        $q3StudentTotalItems = is_numeric($data[68]) ? $data[68] : "NULL";
        $q3StudentItemsCorrect = is_numeric($data[69]) ? $data[69] : "NULL";
        $q3StudentSocialTotalItems = is_numeric($data[70]) ? $data[70] : "NULL";
        $q3StudentSocialItemsCorrect = is_numeric($data[71]) ? $data[71] : "NULL";
        $q3StudentAcademicTotalItems = is_numeric($data[72]) ? $data[72] : "NULL";
        $q3StudentAcademicItemsCorrect = is_numeric($data[73]) ? $data[73] : "NULL";
        $q3StudentEmotionalTotalItems = is_numeric($data[74]) ? $data[74] : "NULL";
        $q3StudentEmotionalItemsCorrect = is_numeric($data[75]) ? $data[75] : "NULL";
        $q3StudentPercentileAtSchool = is_numeric($data[76]) ? $data[76] : "NULL";
        $q3StudentPercentileAtLEA = is_numeric($data[77]) ? $data[77] : "NULL";
        $q3StudentPercentileAtNation = is_numeric($data[78]) ? $data[78] : "NULL";
        $q3StudentRiskLevel = $data[79] ? $data[79] : "NULL";

        if($data[80]){  //format date type for insert
          $q3StudentFinalDate = $data[80];
          $q3StudentFinalDate = date('Y-m-d', strtotime($q3StudentFinalDate));
          $q3StudentFinalDate = "'".$q3StudentFinalDate."'";
        } else {
          $q3StudentFinalDate = "NULL";
        }

        //QUARTER 3 TEACHER
        $q3TeacherTotalItems = is_numeric($data[81]) ? $data[81] : "NULL";
        $q3TeacherItemsCorrect = is_numeric($data[82]) ? $data[82] : "NULL";
        $q3TeacherSocialTotalItems = is_numeric($data[83]) ? $data[83] : "NULL";
        $q3TeacherSocialItemsCorrect = is_numeric($data[84]) ? $data[84] : "NULL";
        $q3TeacherAcademicTotalItems = is_numeric($data[85]) ? $data[85] : "NULL";
        $q3TeacherAcademicItemsCorrect = is_numeric($data[86]) ? $data[86] : "NULL";
        $q3TeacherEmotionalTotalItems = is_numeric($data[87]) ? $data[87] : "NULL";
        $q3TeacherEmotionalItemsCorrect = is_numeric($data[88]) ? $data[88] : "NULL";
        $q3TeacherPercentileAtSchool = is_numeric($data[89]) ? $data[89] : "NULL";
        $q3TeacherPercentileAtLEA = is_numeric($data[90]) ? $data[90] : "NULL";
        $q3TeacherPercentileAtNation = is_numeric($data[91]) ? $data[91] : "NULL";
        $q3TeacherRiskLevel = $data[92] ? $data[92] : "NULL";

        if($data[93]){  //format date type for insert
          $q3TeacherFinalDate = $data[93];
          $q3TeacherFinalDate = date('Y-m-d', strtotime($q3TeacherFinalDate));
          $q3TeacherFinalDate = "'".$q3TeacherFinalDate."'";
        } else {
          $q3TeacherFinalDate = "NULL";
        }

        //QUARTER 4 STUDENT
        $q4StudentTotalItems = is_numeric($data[94] )? $data[94] : "NULL";
        $q4StudentItemsCorrect = is_numeric($data[95] )? $data[95] : "NULL";
        $q4StudentSocialTotalItems = is_numeric($data[96] )? $data[96] : "NULL";
        $q4StudentSocialItemsCorrect = is_numeric($data[97] )? $data[97] : "NULL";
        $q4StudentAcademicTotalItems = is_numeric($data[98] )? $data[98] : "NULL";
        $q4StudentAcademicItemsCorrect = is_numeric($data[99] )? $data[99] : "NULL";
        $q4StudentEmotionalTotalItems = is_numeric($data[100]) ? $data[100] : "NULL";
        $q4StudentEmotionalItemsCorrect = is_numeric($data[101]) ? $data[101] : "NULL";
        $q4StudentPercentileAtSchool = is_numeric($data[102]) ? $data[102] : "NULL";
        $q4StudentPercentileAtLEA = is_numeric($data[103]) ? $data[103] : "NULL";
        $q4StudentPercentileAtNation = is_numeric($data[104]) ? $data[104] : "NULL";
        $q4StudentRiskLevel = $data[105] ? $data[105] : "NULL";

        if($data[106]){  //format date type for insert
          $q4StudentFinalDate = $data[106];
          $q4StudentFinalDate = date('Y-m-d', strtotime($q4StudentFinalDate));
          $q4StudentFinalDate = "'".$q4StudentFinalDate."'";
        } else {
          $q4StudentFinalDate = "NULL";
        }

        //Q1 to Q4 GRWOTH METRICS
        $growthScoreFromQ1ToQ4 = $data[107] ? $data[107] : "NULL";
        $schoolGrowthPercentileFromQ1ToQ4 = $data[108] ? $data[108] : "NULL";
        $districtGrowthPercentileFromQ1ToQ4 = $data[109] ? $data[109] : "NULL";

        $valuesToImport [] = "('$assessment', '$assessmentLanguage', '$state', '$district', '$school',
                            '$localID', '$stateID', '$fastID', '$firstName', '$lastName',  '$gender',
                              $dob, '$race', '$specialEdStatus', '$grade', $q1StudentTotalItems,
                              $q1StudentItemsCorrect, $q1StudentSocialTotalItems, $q1StudentSocialItemsCorrect,
                              $q1StudentAcademicTotalItems, $q1StudentAcademicItemsCorrect, $q1StudentEmotionalTotalItems,
                              $q1StudentEmotionalItemsCorrect, $q1StudentPercentileAtSchool, $q1StudentPercentileAtLEA,
                              $q1StudentPercentileAtNation, '$q1StudentRiskLevel', $q1StudentFinalDate,
                              $q1TeacherTotalItems, $q1TeacherItemsCorrect, $q1TeacherSocialTotalItems, $q1TeacherSocialItemsCorrect,
                              $q1TeacherAcademicTotalItems, $q1TeacherAcademicItemsCorrect, $q1TeacherEmotionalTotalItems,
                              $q1TeacherEmotionalItemsCorrect, $q1TeacherPercentileAtSchool, $q1TeacherPercentileAtLEA, $q1TeacherPercentileAtNation,
                              '$q1TeacherRiskLevel', $q1StudentFinalDate, $q2StudentTotalItems, $q2StudentItemsCorrect,
                              $q2StudentSocialTotalItems, $q2StudentSocialItemsCorrect, $q2StudentAcademicTotalItems, $q2StudentAcademicItemsCorrect,
                              $q2StudentEmotionalTotalItems, $q2StudentEmotionalItemsCorrect, $q2StudentPercentileAtSchool,
                              $q2StudentPercentileAtLEA, $q2StudentPercentileAtNation,'$q2StudentRiskLevel',
                              $q2StudentFinalDate, $q2TeacherTotalItems, $q2TeacherItemsCorrect, $q2TeacherSocialTotalItems,
                              $q2TeacherSocialItemsCorrect, $q2TeacherAcademicTotalItems, $q2TeacherAcademicItemsCorrect, $q2TeacherEmotionalTotalItems,
                              $q2TeacherEmotionalItemsCorrect, $q2TeacherPercentileAtSchool, $q2TeacherPercentileAtLEA, $q2TeacherPercentileAtNation,
                              '$q2TeacherRiskLevel', $q2StudentFinalDate, $q3StudentTotalItems, $q3StudentItemsCorrect, $q3StudentSocialTotalItems,
                              $q3StudentSocialItemsCorrect, $q3StudentAcademicTotalItems, $q3StudentAcademicItemsCorrect, $q3StudentEmotionalTotalItems,
                              $q3StudentEmotionalItemsCorrect, $q3StudentPercentileAtSchool, $q3StudentPercentileAtLEA, $q3StudentPercentileAtNation,
                              '$q3StudentRiskLevel', $q3StudentFinalDate, $q3TeacherTotalItems, $q3TeacherItemsCorrect, $q3TeacherSocialTotalItems,
                              $q3TeacherSocialItemsCorrect, $q3TeacherAcademicTotalItems, $q3TeacherAcademicItemsCorrect, $q3TeacherEmotionalTotalItems,
                              $q3TeacherEmotionalItemsCorrect, $q3TeacherPercentileAtSchool, $q3TeacherPercentileAtLEA, $q3TeacherPercentileAtNation,
                              '$q3TeacherRiskLevel', $q3StudentFinalDate, $q4StudentTotalItems, $q4StudentItemsCorrect, $q4StudentSocialTotalItems,
                              $q4StudentSocialItemsCorrect, $q4StudentAcademicTotalItems, $q4StudentAcademicItemsCorrect, $q4StudentEmotionalTotalItems,
                              $q4StudentEmotionalItemsCorrect, $q4StudentPercentileAtSchool, $q4StudentPercentileAtLEA, $q4StudentPercentileAtNation,
                              '$q4StudentRiskLevel', $q4StudentFinalDate, $growthScoreFromQ1ToQ4, $schoolGrowthPercentileFromQ1ToQ4,
                              $districtGrowthPercentileFromQ1ToQ4, $currentSchoolYearID, $siteID)";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}
  		$line = strtok($separator);

    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }
  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
