<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use LearnositySdk\Request\DataApi;
use Google\Cloud\Storage\StorageClient;

// Helper function for GCS upload
function _uploadToGCS($data, $bucket, $currentDate, $siteID) {
    try {
        if (empty($data)) {
            error_log("No data to upload");
            return;
        }

        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log("Failed to encode JSON");
            return;
        }

        // Upload to site-id folder
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);
        
        $fileName = "Abre_Learnosity.json";
        $bucket->upload($tempFile1, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);
        fclose($tempFile1);

        // Upload to filename folder
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);
        
        $folderName = "Abre_Learnosity";
        $modifiedFile = "{$folderName}-{$siteID}.json";
        $bucket->upload($tempFile2, [
            'name' => "$currentDate/filename/$folderName/$modifiedFile"
        ]);
        fclose($tempFile2);

        error_log("Successfully uploaded data to GCS");
    } catch (Exception $e) {
        error_log("Error uploading data to GCS: " . $e->getMessage());
    }
}

function runJob($db, $siteID, $config){

  $cronName = 'Learnosity';

  try{

      $uuid = Logger::logCronStart($db, $siteID, $cronName);

      // Initialize Google Cloud Storage
      $storage = new StorageClient(['projectId' => "abre-production"]);
      $bucketName = "prd-landing-zone";
      $bucket = $storage->bucket($bucketName);
      $currentDate = date("Ymd");

      // Initialize array to store data for GCS upload
      $learnosityData = [];

      $consumerKey = $config->learnosity->consumerKey;
      $consumerSecret = $config->learnosity->consumerSecret;

      //Endpoint and security setup (common for both operations)
      $itembank_uri = 'https://data.learnosity.com/v2023.1.LTS/sessions/scores';
      $security_packet = [
         'consumer_key'   => $consumerKey,
         'domain'         => 'abre.io',
      ];

      //Initializing API
      $data_api = new DataApi();

      /*
      GET ALL ASSESSMENT SCORES
      RETURNS THE OVERALL SCORE OF AN ABRE ASSESSMENT BY STUDENT/USER
      */

      //Lookup last record date in database
      $sql = "SELECT dt_saved FROM vendor_learnosity_sessions_responses_scores ORDER BY dt_saved DESC LIMIT 1";
      $sqlResult = $db->query($sql);
      if($sqlResult && $sqlResult->num_rows > 0){
        $row = $sqlResult->fetch_assoc();
        $dt_saved = $row['dt_saved'];
        $lastResultDay = new DateTime($dt_saved);
      }else{
        $lastResultDay = "2018-01-01";
      }

      // First do the database operation
      $nextValueExists = 1;
      $nextValue = "";
      while($nextValueExists == 1){
        //Request Setup for database
        if($nextValue != ""){
          $data_request = ['mintime' => $lastResultDay, 'limit' => 50, 'next' => $nextValue];
        }else{
          $data_request = ['mintime' => $lastResultDay, 'limit' => 50];
        }

        //Get Result
        $result = $data_api->request(
           $itembank_uri,
           $security_packet,
           $consumerSecret,
           $data_request,
           'get'
        );

        //Get Response
        $response = $result->json();

        //Check if zero records
        if(isset($response['meta']['records'])){
          $numberOfRecords = $response['meta']['records'];
          if($numberOfRecords == 0){ break; }
        }

        //Check if the 'meta' key exists in the array
        if(isset($response['meta']['next'])){
          $nextValue = $response['meta']['next'];
          $nextValueExists = 1;
        }else{
          $nextValue = "";
          $nextValueExists = 0;
        }

        //Save to Database
        if(isset($response['data']) && is_array($response['data'])){
          foreach($response['data'] as $item){
            $stmt = $db->stmt_init();
            if(isset($item['user_id'])){
              $user_id = $item['user_id'];
              $activity_id = $item['activity_id'];
              $session_id = $item['session_id'];
              $score = $item['score'];
              $max_score = $item['max_score'];
              $max_score_of_attempted = $item['max_score_of_attempted'];
              $max_score_of_unmarked = $item['max_score_of_unmarked'];
              $status = $item['status'];
              $dt_saved = $item['dt_saved'];

              $sql = "INSERT INTO vendor_learnosity_sessions_responses_scores (
                        user_id, activity_id, session_id, score, max_score,
                        max_score_of_attempted, max_score_of_unmarked, status, dt_saved
                      )
                      VALUES
                        (?,?,?,?,?,?,?,?,?)";
              $stmt->prepare($sql);
              $stmt->bind_param("sssiiiiss", $user_id, $activity_id, $session_id,
                                      $score, $max_score, $max_score_of_attempted, $max_score_of_unmarked,
                                      $status, $dt_saved);
              $stmt->execute();
            }
            $stmt->close();
          }
        }
      }

      // Now do the GCS operation separately
      error_log("Starting GCS data collection from 2018-01-01");
      $nextValueExists = 1;
      $nextValue = "";
      while($nextValueExists == 1){
        if($nextValue != ""){
          $gcs_request = ['mintime' => '2018-01-01', 'limit' => 50, 'next' => $nextValue];
        }else{
          $gcs_request = ['mintime' => '2018-01-01', 'limit' => 50];
        }

        $gcs_result = $data_api->request(
           $itembank_uri,
           $security_packet,
           $consumerSecret,
           $gcs_request,
           'get'
        );

        $gcs_response = $gcs_result->json();

        // Check if zero records first
        if(isset($gcs_response['meta']['records'])){
          $numberOfRecords = $gcs_response['meta']['records'];
          error_log("GCS records in current batch: $numberOfRecords");
          if($numberOfRecords == 0){ 
            error_log("No more records to collect for GCS, finishing collection.");
            break; 
          }
        }

        if(isset($gcs_response['meta']['next'])){
          $nextValue = $gcs_response['meta']['next'];
          $nextValueExists = 1;
        }else{
          $nextValue = "";
          $nextValueExists = 0;
        }

        if(isset($gcs_response['data']) && is_array($gcs_response['data'])){
          $currentBatchSize = count($gcs_response['data']);
          if($currentBatchSize > 0) {
            $learnosityData = array_merge($learnosityData, $gcs_response['data']);
            $totalCollected = count($learnosityData);
            error_log("Added batch of $currentBatchSize records to GCS. Total records collected: $totalCollected");
          } else {
            error_log("Received empty batch, finishing collection.");
            break;
          }
        }else{
          error_log("No valid data in response, finishing collection.");
          break;
        }
      }

      // After collecting all batches, upload the complete dataset to GCS
      if (!empty($learnosityData)) {
        $finalCount = count($learnosityData);
        error_log("Uploading complete dataset to GCS. Total records: $finalCount");
        _uploadToGCS($learnosityData, $bucket, $currentDate, $siteID);
      }

  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
