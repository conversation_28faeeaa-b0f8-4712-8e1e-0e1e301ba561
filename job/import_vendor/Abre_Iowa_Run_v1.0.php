<?php

require(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require(dirname(__FILE__) . '/../../vendor/autoload.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Iowa Import v1';

  try {

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
      throw new Exception("Login to SFTP failed.");
    }
    $localTempFile = tempnam(sys_get_temp_dir(), 'iowa');
    $sftp->get('Abre_Iowa_v1.0.txt', $localTempFile);
    importIowaTestData($db, $siteID, $localTempFile);
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];

  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

function importIowaTestData($db, $siteID, $tempFile) {
  try {
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($tempFile, $db, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  } finally {
    if (isset($temporaryTableName)) {
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db) {
  $tableCreated = false;
  $tries = 0;
  while (!$tableCreated && $tries < 3) {
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName} (
      student_id VARCHAR(32),
      test_date DATE,
      reading_raw_score SMALLINT,
      vocabulary_raw_score SMALLINT,
      spelling_raw_score SMALLINT,
      capitalization_raw_score SMALLINT,
      punctuation_raw_score SMALLINT,
      language_written_expression_raw_score SMALLINT,
      conventions_of_writing_raw_score SMALLINT,
      english_language_arts_total_raw_score SMALLINT,
      mathematics_raw_score SMALLINT,
      computation_raw_score SMALLINT,
      math_total_raw_score SMALLINT,
      social_studies_raw_score SMALLINT,
      science_raw_score SMALLINT,

      reading_standard_score SMALLINT,
      vocabulary_standard_score SMALLINT,
      spelling_standard_score SMALLINT,
      capitalization_standard_score SMALLINT,
      punctuation_standard_score SMALLINT,
      language_written_expression_standard_score SMALLINT,
      conventions_of_writing_standard_score SMALLINT,
      english_language_arts_total_standard_score SMALLINT,
      mathematics_standard_score SMALLINT,
      computation_standard_score SMALLINT,
      math_total_standard_score SMALLINT,
      social_studies_standard_score SMALLINT,
      science_standard_score SMALLINT,

      reading_grade_equivalent VARCHAR(4),
      vocabulary_grade_equivalent VARCHAR(4),
      spelling_grade_equivalent VARCHAR(4),
      capitalization_grade_equivalent VARCHAR(4),
      punctuation_grade_equivalent VARCHAR(4),
      language_written_expression_grade_equivalent VARCHAR(4),
      conventions_of_writing_grade_equivalent VARCHAR(4),
      english_language_arts_total_grade_equivalent VARCHAR(4),
      mathematics_grade_equivalent VARCHAR(4),
      computation_grade_equivalent VARCHAR(4),
      math_total_grade_equivalent VARCHAR(4),
      social_studies_grade_equivalent VARCHAR(4),
      science_grade_equivalent VARCHAR(4),

      reading_national_percentile_rank SMALLINT,
      vocabulary_national_percentile_rank SMALLINT,
      spelling_national_percentile_rank SMALLINT,
      capitalization_national_percentile_rank SMALLINT,
      punctuation_national_percentile_rank SMALLINT,
      language_written_expression_national_percentile_rank SMALLINT,
      conventions_of_writing_national_percentile_rank SMALLINT,
      english_language_arts_total_national_percentile_rank SMALLINT,
      mathematics_national_percentile_rank SMALLINT,
      computation_national_percentile_rank SMALLINT,
      math_total_national_percentile_rank SMALLINT,
      social_studies_national_percentile_rank SMALLINT,
      science_national_percentile_rank SMALLINT
    )";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($fileName, $db, $temporaryTableName) {
  $temporaryTableInsertSQL = "
    INSERT INTO {$temporaryTableName}
    (
      student_id,
    	test_date,
    	reading_raw_score,
      vocabulary_raw_score,
      spelling_raw_score,
      capitalization_raw_score,
      punctuation_raw_score,
      language_written_expression_raw_score,
      conventions_of_writing_raw_score,
      english_language_arts_total_raw_score,
      mathematics_raw_score,
      computation_raw_score,
      math_total_raw_score,
      social_studies_raw_score,
      science_raw_score,

      reading_standard_score,
      vocabulary_standard_score,
      spelling_standard_score,
      capitalization_standard_score,
      punctuation_standard_score,
      language_written_expression_standard_score,
      conventions_of_writing_standard_score,
      english_language_arts_total_standard_score,
      mathematics_standard_score,
      computation_standard_score,
      math_total_standard_score,
      social_studies_standard_score,
      science_standard_score,

      reading_grade_equivalent,
      vocabulary_grade_equivalent,
      spelling_grade_equivalent,
      capitalization_grade_equivalent,
      punctuation_grade_equivalent,
      language_written_expression_grade_equivalent,
      conventions_of_writing_grade_equivalent,
      english_language_arts_total_grade_equivalent,
      mathematics_grade_equivalent,
      computation_grade_equivalent,
      math_total_grade_equivalent,
      social_studies_grade_equivalent,
      science_grade_equivalent,

  	  reading_national_percentile_rank,
      vocabulary_national_percentile_rank,
      spelling_national_percentile_rank,
      capitalization_national_percentile_rank,
      punctuation_national_percentile_rank,
      language_written_expression_national_percentile_rank,
      conventions_of_writing_national_percentile_rank,
      english_language_arts_total_national_percentile_rank,
      mathematics_national_percentile_rank,
      computation_national_percentile_rank,
      math_total_national_percentile_rank,
      social_studies_national_percentile_rank,
      science_national_percentile_rank
    )
    VALUES";

    $handle = fopen($fileName, "r");
    if ($handle === false) {
      throw new Exception("Cannot read CSV file");
    }
    if (fgets($handle) === false) { // header line
      throw new Exception("file is empty");
    }

    //concatenate all records
    $records = array();
    while (($line = fgets($handle)) !== false) {
      $lineArray = str_getcsv($line, "\t");
      array_push($records, buildInsertLine($lineArray));
    }
    $temporaryTableInsertSQL .= implode(",", $records) . ";";

    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
}

function buildInsertLine($line) {
  $studentID = trim($line[16]);

  if (trim($line[30]) == "") {
    $testDate = "NULL";
  } else {
    $testDate = trim($line[30]);
    $month = substr($testDate, 0, 2);
    $day = substr($testDate, 2, 2);
    $year = substr($testDate, 4, 4);
    $testDate = "{$year}-{$month}-{$day}";
  }

  $readingRawScore = nullCheck($line[150]);
  $vocabularyRawScore = nullCheck($line[151]);
  $spellingRawScore = nullCheck($line[152]);
  $capitalizationRawScore = nullCheck($line[153]);
  $punctuationRawScore = nullCheck($line[154]);
  $writtenExpressionRawScore = nullCheck($line[155]);
  $conventionsOfWritingRawScore = nullCheck($line[156]);
  $englishLanguageArtsTotalRawScore = nullCheck($line[157]);
  $mathematicsRawScore = nullCheck($line[161]);
  $computationRawScore = nullCheck($line[162]);
  $mathTotalRawScore = nullCheck($line[163]);
  $socialStudiesRawScore = nullCheck($line[168]);
  $scienceRawScore = nullCheck($line[169]);

  $readingStandardScore = nullCheck($line[180]);
  $vocabularyStandardScore = nullCheck($line[181]);
  $spellingStandardScore = nullCheck($line[182]);
  $capitalizationStandardScore = nullCheck($line[183]);
  $punctuationStandardScore = nullCheck($line[184]);
  $writtenExpressionStandardScore = nullCheck($line[185]);
  $conventionsOfWritingStandardScore = nullCheck($line[186]);
  $englishLanguageArtsTotalStandardScore = nullCheck($line[187]);
  $mathematicsStandardScore = nullCheck($line[191]);
  $computationStandardScore = nullCheck($line[192]);
  $mathTotalStandardScore = nullCheck($line[193]);
  $socialStudiesStandardScore = nullCheck($line[199]);
  $scienceStandardScore = nullCheck($line[200]);

  $readingGradeEquivalent = nullCheckWithQuotes($line[210]);
  $vocabularyGradeEquivalent = nullCheckWithQuotes($line[211]);
  $spellingGradeEquivalent = nullCheckWithQuotes($line[212]);
  $capitalizationGradeEquivalent = nullCheckWithQuotes($line[213]);
  $punctuationGradeEquivalent = nullCheckWithQuotes($line[214]);
  $writtenExpressionGradeEquivalent = nullCheckWithQuotes($line[215]);
  $conventionsOfWritingGradeEquivalent = nullCheckWithQuotes($line[216]);
  $englishLanguageArtsTotalGradeEquivalent = nullCheckWithQuotes($line[217]);
  $mathematicsGradeEquivalent = nullCheckWithQuotes($line[221]);
  $computationGradeEquivalent = nullCheckWithQuotes($line[222]);
  $mathTotalGradeEquivalent = nullCheckWithQuotes($line[223]);
  $socialStudiesGradeEquivalent = nullCheckWithQuotes($line[228]);
  $scienceGradeEquivalent = nullCheckWithQuotes($line[229]);

  $readingNationalPercentileRank = nullCheck($line[240]);
  $vocabularyNationalPercentileRank = nullCheck($line[241]);
  $spellingNationalPercentileRank = nullCheck($line[242]);
  $capitalizationNationalPercentileRank = nullCheck($line[243]);
  $punctuationNationalPercentileRank = nullCheck($line[244]);
  $writtenExpressionNationalPercentileRank = nullCheck($line[245]);
  $conventionsOfWritingNationalPercentileRank = nullCheck($line[246]);
  $englishLanguageArtsTotalNationalPercentileRank = nullCheck($line[247]);
  $mathematicsNationalPercentileRank = nullCheck($line[251]);
  $computationNationalPercentileRank = nullCheck($line[252]);
  $mathTotalNationalPercentileRank = nullCheck($line[253]);
  $socialStudiesNationalPercentileRank = nullCheck($line[258]);
  $scienceNationalPercentileRank = nullCheck($line[259]);

  $sql = "
    (
      '$studentID',
      '$testDate',

      $readingRawScore,
      $vocabularyRawScore,
      $spellingRawScore,
      $capitalizationRawScore,
      $punctuationRawScore,
      $writtenExpressionRawScore,
      $conventionsOfWritingRawScore,
      $englishLanguageArtsTotalRawScore,
      $mathematicsRawScore,
      $computationRawScore,
      $mathTotalRawScore,
      $socialStudiesRawScore,
      $scienceRawScore,

      $readingStandardScore,
      $vocabularyStandardScore,
      $spellingStandardScore,
      $capitalizationStandardScore,
      $punctuationStandardScore,
      $writtenExpressionStandardScore,
      $conventionsOfWritingStandardScore,
      $englishLanguageArtsTotalStandardScore,
      $mathematicsStandardScore,
      $computationStandardScore,
      $mathTotalStandardScore,
      $socialStudiesStandardScore,
      $scienceStandardScore,

      $readingGradeEquivalent,
      $vocabularyGradeEquivalent,
      $spellingGradeEquivalent,
      $capitalizationGradeEquivalent,
      $punctuationGradeEquivalent,
      $writtenExpressionGradeEquivalent,
      $conventionsOfWritingGradeEquivalent,
      $englishLanguageArtsTotalGradeEquivalent,
      $mathematicsGradeEquivalent,
      $computationGradeEquivalent,
      $mathTotalGradeEquivalent,
      $socialStudiesGradeEquivalent,
      $scienceGradeEquivalent,

      $readingNationalPercentileRank,
      $vocabularyNationalPercentileRank,
      $spellingNationalPercentileRank,
      $capitalizationNationalPercentileRank,
      $punctuationNationalPercentileRank,
      $writtenExpressionNationalPercentileRank,
      $conventionsOfWritingNationalPercentileRank,
      $englishLanguageArtsTotalNationalPercentileRank,
      $mathematicsNationalPercentileRank,
      $computationNationalPercentileRank,
      $mathTotalNationalPercentileRank,
      $socialStudiesNationalPercentileRank,
      $scienceNationalPercentileRank
    )";
  return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName) {
  $replaceIntoSQL = "
    REPLACE INTO abre_iowa
    (
      site_id,
      student_id,
      test_date,

      reading_raw_score,
      vocabulary_raw_score,
      spelling_raw_score,
      capitalization_raw_score,
      punctuation_raw_score,
      language_written_expression_raw_score,
      conventions_of_writing_raw_score,
      english_language_arts_total_raw_score,
      mathematics_raw_score,
      computation_raw_score,
      math_total_raw_score,
      social_studies_raw_score,
      science_raw_score,

      reading_standard_score,
      vocabulary_standard_score,
      spelling_standard_score,
      capitalization_standard_score,
      punctuation_standard_score,
      language_written_expression_standard_score,
      conventions_of_writing_standard_score,
      english_language_arts_total_standard_score,
      mathematics_standard_score,
      computation_standard_score,
      math_total_standard_score,
      social_studies_standard_score,
      science_standard_score,

      reading_grade_equivalent,
      vocabulary_grade_equivalent,
      spelling_grade_equivalent,
      capitalization_grade_equivalent,
      punctuation_grade_equivalent,
      language_written_expression_grade_equivalent,
      conventions_of_writing_grade_equivalent,
      english_language_arts_total_grade_equivalent,
      mathematics_grade_equivalent,
      computation_grade_equivalent,
      math_total_grade_equivalent,
      social_studies_grade_equivalent,
      science_grade_equivalent,

      reading_national_percentile_rank,
      vocabulary_national_percentile_rank,
      spelling_national_percentile_rank,
      capitalization_national_percentile_rank,
      punctuation_national_percentile_rank,
      language_written_expression_national_percentile_rank,
      conventions_of_writing_national_percentile_rank,
      english_language_arts_total_national_percentile_rank,
      mathematics_national_percentile_rank,
      computation_national_percentile_rank,
      math_total_national_percentile_rank,
      social_studies_national_percentile_rank,
      science_national_percentile_rank
    )
    SELECT
      ?,
      student_id,
      test_date,

      reading_raw_score,
      vocabulary_raw_score,
      spelling_raw_score,
      capitalization_raw_score,
      punctuation_raw_score,
      language_written_expression_raw_score,
      conventions_of_writing_raw_score,
      english_language_arts_total_raw_score,
      mathematics_raw_score,
      computation_raw_score,
      math_total_raw_score,
      social_studies_raw_score,
      science_raw_score,

      reading_standard_score,
      vocabulary_standard_score,
      spelling_standard_score,
      capitalization_standard_score,
      punctuation_standard_score,
      language_written_expression_standard_score,
      conventions_of_writing_standard_score,
      english_language_arts_total_standard_score,
      mathematics_standard_score,
      computation_standard_score,
      math_total_standard_score,
      social_studies_standard_score,
      science_standard_score,

      reading_grade_equivalent,
      vocabulary_grade_equivalent,
      spelling_grade_equivalent,
      capitalization_grade_equivalent,
      punctuation_grade_equivalent,
      language_written_expression_grade_equivalent,
      conventions_of_writing_grade_equivalent,
      english_language_arts_total_grade_equivalent,
      mathematics_grade_equivalent,
      computation_grade_equivalent,
      math_total_grade_equivalent,
      social_studies_grade_equivalent,
      science_grade_equivalent,

      reading_national_percentile_rank,
      vocabulary_national_percentile_rank,
      spelling_national_percentile_rank,
      capitalization_national_percentile_rank,
      punctuation_national_percentile_rank,
      language_written_expression_national_percentile_rank,
      conventions_of_writing_national_percentile_rank,
      english_language_arts_total_national_percentile_rank,
      mathematics_national_percentile_rank,
      computation_national_percentile_rank,
      math_total_national_percentile_rank,
      social_studies_national_percentile_rank,
      science_national_percentile_rank
    FROM {$temporaryTableName}
  ";
  $stmt = $db->stmt_init();
  $stmt->prepare($replaceIntoSQL);
  $stmt->bind_param('i', $siteID);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName) {
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
 ?>
