<?php
require(dirname(__FILE__) . '/../../utils/functions.php');
require_once(dirname(__FILE__) . '/../../utils/logging.php');
require(dirname(__FILE__) . '/../../../vendor/autoload.php');
use phpseclib\Net\SFTP;

  /**
 * @deprecated
 * Deprecated, does not appear to be in use.
 */
function runJob($db, $siteID, $config){

  $cronName = 'Abre iReady ELA v2';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'iready_ela');
    $sftp->get('Abre_iReady_ELA_v2.0.csv', $localTempFile);
    importiReadyData($db, $siteID, $localTempFile);
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];


  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

function importiReadyData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($tempFile, $db, $siteID, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTempTableSQL =  "CREATE TABLE {$temporaryTableName} (
      `student_id` varchar(32) NOT NULL,
      `student_grade` varchar(2) DEFAULT NULL,
      `academic_year` varchar(10) NOT NULL,
      `number_of_diagnostics_completed` tinyint(1) NOT NULL,
      `lexile_measure` varchar(15) DEFAULT NULL,
      `lexile_range` varchar(15) DEFAULT NULL,
      `start_date` date DEFAULT NULL,
      `completion_date` date DEFAULT NULL,
      `time_on_task` smallint(3) DEFAULT NULL,
      `rush_flag` varchar(45) DEFAULT NULL,
      `overall_scale_score` smallint(3) DEFAULT NULL,
      `overall_placement` varchar(10) DEFAULT NULL,
      `percentile` tinyint(2) DEFAULT NULL,
      `overall_relative_placement` varchar(25) DEFAULT NULL,
      `tier` varchar(20) DEFAULT NULL,
      `phonological_awareness_scale_score` smallint(3) DEFAULT NULL,
      `phonological_awareness_placement` varchar(10) DEFAULT NULL,
      `phonological_awareness_relative_placement` varchar(25) DEFAULT NULL,
      `phonics_scale_score` smallint(3) DEFAULT NULL,
      `phonics_placement` varchar(10) DEFAULT NULL,
      `phonics_relative_placement` varchar(25) DEFAULT NULL,
      `high_frequency_words_scale_score` smallint(3) DEFAULT NULL,
      `high_frequency_words_placement` varchar(10) DEFAULT NULL,
      `high_frequency_words_relative_placement` varchar(25) DEFAULT NULL,
      `vocabulary_scale_score` smallint(3) DEFAULT NULL,
      `vocabulary_placement` varchar(10) DEFAULT NULL,
      `vocabulary_relative_placement` varchar(25) DEFAULT NULL,
      `reading_comprehension_literature_scale_score` smallint(3) DEFAULT NULL,
      `reading_comprehension_literature_placement` varchar(10) DEFAULT NULL,
      `reading_comprehension_literature_relative_placement` varchar(25) DEFAULT NULL,
      `reading_comprehension_informational_text_scale_score` smallint(3) DEFAULT NULL,
      `reading_comprehension_informational_text_placement` varchar(10) DEFAULT NULL,
      `reading_comprehension_informational_text_relative_placement` varchar(25) DEFAULT NULL,
      `overall_lessons_passed` smallint(3) DEFAULT NULL,
      `overall_lessons_failed` smallint(3) DEFAULT NULL,
      `overall_lessons_completed` smallint(3) DEFAULT NULL,
      `overall_pass_rate` tinyint(3) DEFAULT NULL,
      `overall_time_on_task` decimal(6,1) DEFAULT NULL,
      `phonological_awareness_lessons_passed` smallint(3) DEFAULT NULL,
      `phonological_awareness_lessons_failed` smallint(3) DEFAULT NULL,
      `phonological_awareness_lessons_completed` smallint(3) DEFAULT NULL,
      `phonological_awareness_pass_rate` tinyint(3) DEFAULT NULL,
      `phonological_awareness_time_on_task` decimal(6,1) DEFAULT NULL,
      `phonics_lessons_passed` smallint(3) DEFAULT NULL,
      `phonics_lessons_failed` smallint(3) DEFAULT NULL,
      `phonics_lessons_completed` smallint(3) DEFAULT NULL,
      `phonics_pass_rate` tinyint(3) DEFAULT NULL,
      `phonics_time_on_task` decimal(6,1) DEFAULT NULL,
      `high_frequency_words_lessons_passed` smallint(3) DEFAULT NULL,
      `high_frequency_words_lessons_failed` smallint(3) DEFAULT NULL,
      `high_frequency_words_lessons_completed` smallint(3) DEFAULT NULL,
      `high_frequency_words_pass_rate` tinyint(3) DEFAULT NULL,
      `high_frequency_words_time_on_task` decimal(6,1) DEFAULT NULL,
      `vocabulary_lessons_passed` smallint(3) DEFAULT NULL,
      `vocabulary_lessons_failed` smallint(3) DEFAULT NULL,
      `vocabulary_lessons_completed` smallint(3) DEFAULT NULL,
      `vocabulary_lessons_pass_rate` tinyint(3) DEFAULT NULL,
      `vocabulary_lessons_time_on_task` decimal(6,1) DEFAULT NULL,
      `reading_comprehension_lessons_passed` smallint(3) DEFAULT NULL,
      `reading_comprehension_lessons_failed` smallint(3) DEFAULT NULL,
      `reading_comprehension_lessons_completed` smallint(3) DEFAULT NULL,
      `reading_comprehension_lessons_pass_rate` tinyint(3) DEFAULT NULL,
      `reading_comprehension_lessons_time_on_task` decimal(6,1) DEFAULT NULL,
      `reading_comprehension_close_reading_lessons_passed` smallint(3) DEFAULT NULL,
      `reading_comprehension_close_reading_lessons_failed` smallint(3) DEFAULT NULL,
      `reading_comprehension_close_reading_lessons_completed` smallint(3) DEFAULT NULL,
      `reading_comprehension_close_reading_lessons_pass_rate` tinyint(3) DEFAULT NULL,
      `reading_comprehension_close_reading_lessons_time_on_task` decimal(6,1) DEFAULT NULL
    )";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTempTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($fileName, $db, $siteId, $temporaryTableName){
  $fieldNames = "
    INSERT INTO {$temporaryTableName}
    (
      student_id,
      student_grade,
      academic_year,
      number_of_diagnostics_completed,
      lexile_measure,
      lexile_range,
      start_date,
      completion_date,
      time_on_task,
      rush_flag,
      overall_scale_score,
      overall_placement,
      percentile,
      overall_relative_placement,
      tier,
      phonological_awareness_scale_score,
      phonological_awareness_placement,
      phonological_awareness_relative_placement,
      phonics_scale_score,
      phonics_placement,
      phonics_relative_placement,
      high_frequency_words_scale_score,
      high_frequency_words_placement,
      high_frequency_words_relative_placement,
      vocabulary_scale_score,
      vocabulary_placement,
      vocabulary_relative_placement,
      reading_comprehension_literature_scale_score,
      reading_comprehension_literature_placement,
      reading_comprehension_literature_relative_placement,
      reading_comprehension_informational_text_scale_score,
      reading_comprehension_informational_text_placement,
      reading_comprehension_informational_text_relative_placement,
      overall_lessons_passed,
      overall_lessons_failed,
      overall_lessons_completed,
      overall_pass_rate,
      overall_time_on_task,
      phonological_awareness_lessons_passed,
      phonological_awareness_lessons_failed,
      phonological_awareness_lessons_completed,
      phonological_awareness_pass_rate,
      phonological_awareness_time_on_task,
      phonics_lessons_passed,
      phonics_lessons_failed,
      phonics_lessons_completed,
      phonics_pass_rate,
      phonics_time_on_task,
      high_frequency_words_lessons_passed,
      high_frequency_words_lessons_failed,
      high_frequency_words_lessons_completed,
      high_frequency_words_pass_rate,
      high_frequency_words_time_on_task,
      vocabulary_lessons_passed,
      vocabulary_lessons_failed,
      vocabulary_lessons_completed,
      vocabulary_lessons_pass_rate,
      vocabulary_lessons_time_on_task,
      reading_comprehension_lessons_passed,
      reading_comprehension_lessons_failed,
      reading_comprehension_lessons_completed,
      reading_comprehension_lessons_pass_rate,
      reading_comprehension_lessons_time_on_task,
      reading_comprehension_close_reading_lessons_passed,
      reading_comprehension_close_reading_lessons_failed,
      reading_comprehension_close_reading_lessons_completed,
      reading_comprehension_close_reading_lessons_pass_rate,
      reading_comprehension_close_reading_lessons_time_on_task
    )
    VALUES";

  $handle = fopen($fileName, "r");

  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgetcsv($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  $recordsAdded = 0;
  define("IMPORT_SIZE", 100);
  while(($line = fgetcsv($handle)) !== false){
    $studentID = trim($line[2]);
    $academicYear = trim($line[5]);
    $numberOfDiagnosticsCompleted = trim($line[19]);

    if($studentID != ""){
      $numberOfDiagnosticsStored = getNumberOfDiagnosticsStored($db, $siteId, $studentID, $academicYear);
      while($numberOfDiagnosticsStored < $numberOfDiagnosticsCompleted - 1){
        $numberOfDiagnosticsStored++;
        $recordsAdded++;
        switch($numberOfDiagnosticsStored){
          case 1:
            $startingIndex = 53;
            break;
          case 2:
            $startingIndex = 80;
            break;
          case 3:
            $startingIndex = 107;
            break;
          case 4:
            $startingIndex = 134;
            break;
          case 5:
            $startingIndex = 161;
            break;
        }
        array_push($records, buildInsertLine($line, $startingIndex, $numberOfDiagnosticsStored));

        if($recordsAdded > IMPORT_SIZE){
          $temporaryTableInsertSQL = $fieldNames . implode(",", $records) . ";";

          $stmt = $db->stmt_init();
          $stmt->prepare($temporaryTableInsertSQL);
          $stmt->execute();
          $stmt->close();

          $recordsAdded = 0;
          $records = [];
          $temporaryTableInsertSQL = "";
        }
      }
      array_push($records, buildInsertLine($line, 23, $numberOfDiagnosticsCompleted, true));
    }
  }
  $temporaryTableInsertSQL = $fieldNames . implode(",", $records) . ";";

  $stmt = $db->stmt_init();
  $stmt->prepare($temporaryTableInsertSQL);
  $stmt->execute();
  $stmt->close();
}

function getNumberOfDiagnosticsStored($db, $siteID, $studentID, $academicYear){
  $stmt = $db->stmt_init();
	$query = "SELECT COUNT(*)
						FROM abre_iready_ela
						WHERE student_id = ? AND site_id = ? AND academic_year = ?";
	$stmt->prepare($query);
	$stmt->bind_param("sis", $studentID, $siteID, $academicYear);
	$stmt->execute();
	$stmt->bind_result($numberOfDiagnosticsStored);

  $stmt->fetch();
  $stmt->close();
  return $numberOfDiagnosticsStored;
}

function buildInsertLine($line, $startingIndex, $diagnosticNumber, $mostRecent = false){
  $studentID = trim($line[2]);
  $studentGrade = trim($line[4]);
  $academicYear = trim($line[5]);
  $numberOfDiagnosticsCompleted = $diagnosticNumber;

  $lexileMeasure = nullCheckWithQuotes($line[32]);
  $lexileRange = nullCheckWithQuotes($line[33]);

  if(trim($line[$startingIndex]) == ""){
    $startDate = "NULL";
  }else{
    $startDate = str_pad(trim($line[$startingIndex]), 10, "0", STR_PAD_LEFT);
    $month = substr($startDate, 0, 2);
    $day = substr($startDate, 3, 2);
    $year = substr($startDate, 6, 4);
    $startDate = "{$year}-{$month}-{$day}";
  }

  if(trim($line[$startingIndex + 1]) == ""){
    $completionDate = "NULL";
  }else{
    $completionDate = str_pad(trim($line[$startingIndex + 1]), 10, "0", STR_PAD_LEFT);
    $month = substr($completionDate, 0, 2);
    $day = substr($completionDate, 3, 2);
    $year = substr($completionDate, 6, 4);
    $completionDate = "{$year}-{$month}-{$day}";
  }

  $timeOnTask = nullCheck($line[$startingIndex + 2]);
  $rushFlag = nullCheckWithQuotes($line[$startingIndex + 3]);

  $overallScaleScore = nullCheck($line[$startingIndex + 4]);
  $overallPlacement = nullCheckWithQuotes($line[$startingIndex + 5]);
  $percentile = nullCheck($line[$startingIndex + 6]);
  $overallRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 7]);
  $tier = nullCheckWithQuotes($line[$startingIndex + 8]);

  //most recent assessments have lexile information inline with the diagnostic results
  if(!$mostRecent){
    $phonologicalAwarenessScaleScore = nullCheck($line[$startingIndex + 9]);
    $phonologicalAwarenessPlacement = nullCheckWithQuotes($line[$startingIndex + 10]);
    $phonologicalAwarenessRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 11]);

    $phonicsScaleScore = nullCheck($line[$startingIndex + 12]);
    $phonicsPlacement = nullCheckWithQuotes($line[$startingIndex + 13]);
    $phonicsRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 14]);

    $highFrequencyWordsScaleScore = nullCheck($line[$startingIndex + 15]);
    $highFrequencyWordsPlacement = nullCheckWithQuotes($line[$startingIndex + 16]);
    $highFrequencyWordsRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 17]);

    $vocabularyScaleScore = nullCheck($line[$startingIndex + 18]);
    $vocabularyPlacement = nullCheckWithQuotes($line[$startingIndex + 19]);
    $vocabularyRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 20]);

    $readingComprehensionLiteratureScaleScore = nullCheck($line[$startingIndex + 21]);
    $readingComprehensionLiteraturePlacement = nullCheckWithQuotes($line[$startingIndex + 22]);
    $readingComprehensionLiteratureRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 23]);

    $readingComprehensionInformationalTextScaleScore = nullCheck($line[$startingIndex + 24]);
    $readingComprehensionInformationalTextPlacement = nullCheckWithQuotes($line[$startingIndex + 25]);
    $readingComprehensionInformationalTextRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 26]);
  }else{
    $phonologicalAwarenessScaleScore = nullCheck($line[$startingIndex + 12]);
    $phonologicalAwarenessPlacement = nullCheckWithQuotes($line[$startingIndex + 13]);
    $phonologicalAwarenessRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 14]);

    $phonicsScaleScore = nullCheck($line[$startingIndex + 15]);
    $phonicsPlacement = nullCheckWithQuotes($line[$startingIndex + 16]);
    $phonicsRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 17]);

    $highFrequencyWordsScaleScore = nullCheck($line[$startingIndex + 18]);
    $highFrequencyWordsPlacement = nullCheckWithQuotes($line[$startingIndex + 19]);
    $highFrequencyWordsRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 20]);

    $vocabularyScaleScore = nullCheck($line[$startingIndex + 21]);
    $vocabularyPlacement = nullCheckWithQuotes($line[$startingIndex + 22]);
    $vocabularyRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 23]);

    $readingComprehensionLiteratureScaleScore = nullCheck($line[$startingIndex + 24]);
    $readingComprehensionLiteraturePlacement = nullCheckWithQuotes($line[$startingIndex + 25]);
    $readingComprehensionLiteratureRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 26]);

    $readingComprehensionInformationalTextScaleScore = nullCheck($line[$startingIndex + 27]);
    $readingComprehensionInformationalTextPlacement = nullCheckWithQuotes($line[$startingIndex + 28]);
    $readingComprehensionInformationalTextRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 29]);
  }

  $overallLessonsPassed = nullCheck($line[188]);
  $overallLessonsFailed = nullCheck($line[189]);
  $overallLessonsCompleted = nullCheck($line[190]);
  $overallPassRate = nullCheck($line[191]);
  $overallTimeOnTask = nullCheck($line[192]);

  $phonologicalAwarenessLessonsPassed = nullCheck($line[193]);
  $phonologicalAwarenessLessonsFailed = nullCheck($line[194]);
  $phonologicalAwarenessLessonsCompleted = nullCheck($line[195]);
  $phonologicalAwarenessPassRate = nullCheck($line[196]);
  $phonologicalAwarenessTimeOnTask = nullCheck($line[197]);

  $phonicsLessonsPassed = nullCheck($line[198]);
  $phonicsLessonsFailed = nullCheck($line[199]);
  $phonicsLessonsCompleted = nullCheck($line[200]);
  $phonicsPassRate = nullCheck($line[201]);
  $phonicsTimeOnTask = nullCheck($line[202]);

  $highFrequencyWordsLessonsPassed = nullCheck($line[203]);
  $highFrequencyWordsLessonsFailed = nullCheck($line[204]);
  $highFrequencyWordsLessonsCompleted = nullCheck($line[205]);
  $highFrequencyWordsPassRate = nullCheck($line[206]);
  $highFrequencyWordsTimeOnTask = nullCheck($line[207]);

  $vocabularyLessonsPassed = nullCheck($line[208]);
  $vocabularyLessonsFailed = nullCheck($line[209]);
  $vocabularyLessonsCompleted = nullCheck($line[210]);
  $vocabularyPassRate = nullCheck($line[211]);
  $vocabularyTimeOnTask = nullCheck($line[212]);

  $readingComprehensionLessonsPassed = nullCheck($line[213]);
  $readingComprehensionLessonsFailed = nullCheck($line[214]);
  $readingComprehensionLessonsCompleted = nullCheck($line[215]);
  $readingComprehensionPassRate = nullCheck($line[216]);
  $readingComprehensionTimeOnTask = nullCheck($line[217]);

  $readingComprehensionCloseReadingLessonsPassed = nullCheck($line[218]);
  $readingComprehensionCloseReadingLessonsFailed = nullCheck($line[219]);
  $readingComprehensionCloseReadingLessonsCompleted = nullCheck($line[220]);
  $readingComprehensionCloseReadingPassRate = nullCheck($line[221]);
  $readingComprehensionCloseReadingTimeOnTask = nullCheck($line[222]);

  $sql = "
    (
      '$studentID',
      '$studentGrade',
      '$academicYear',
      $numberOfDiagnosticsCompleted,

      $lexileMeasure,
      $lexileRange,

      '$startDate',
      '$completionDate',
      $timeOnTask,
      $rushFlag,

      $overallScaleScore,
      $overallPlacement,
      $percentile,
      $overallRelativePlacement,
      $tier,

      $phonologicalAwarenessScaleScore,
      $phonologicalAwarenessPlacement,
      $phonologicalAwarenessRelativePlacement,

      $phonicsScaleScore,
      $phonicsPlacement,
      $phonicsRelativePlacement,

      $highFrequencyWordsScaleScore,
      $highFrequencyWordsPlacement,
      $highFrequencyWordsRelativePlacement,

      $vocabularyScaleScore,
      $vocabularyPlacement,
      $vocabularyRelativePlacement,

      $readingComprehensionLiteratureScaleScore,
      $readingComprehensionLiteraturePlacement,
      $readingComprehensionLiteratureRelativePlacement,

      $readingComprehensionInformationalTextScaleScore,
      $readingComprehensionInformationalTextPlacement,
      $readingComprehensionInformationalTextRelativePlacement,

      $overallLessonsPassed,
      $overallLessonsFailed,
      $overallLessonsCompleted,
      $overallPassRate,
      $overallTimeOnTask,

      $phonologicalAwarenessLessonsPassed,
      $phonologicalAwarenessLessonsFailed,
      $phonologicalAwarenessLessonsCompleted,
      $phonologicalAwarenessPassRate,
      $phonologicalAwarenessTimeOnTask,

      $phonicsLessonsPassed,
      $phonicsLessonsFailed,
      $phonicsLessonsCompleted,
      $phonicsPassRate,
      $phonicsTimeOnTask,

      $highFrequencyWordsLessonsPassed,
      $highFrequencyWordsLessonsFailed,
      $highFrequencyWordsLessonsCompleted,
      $highFrequencyWordsPassRate,
      $highFrequencyWordsTimeOnTask,

      $vocabularyLessonsPassed,
      $vocabularyLessonsFailed,
      $vocabularyLessonsCompleted,
      $vocabularyPassRate,
      $vocabularyTimeOnTask,

      $readingComprehensionLessonsPassed,
      $readingComprehensionLessonsFailed,
      $readingComprehensionLessonsCompleted,
      $readingComprehensionPassRate,
      $readingComprehensionTimeOnTask,

      $readingComprehensionCloseReadingLessonsPassed,
      $readingComprehensionCloseReadingLessonsFailed,
      $readingComprehensionCloseReadingLessonsCompleted,
      $readingComprehensionCloseReadingPassRate,
      $readingComprehensionCloseReadingTimeOnTask
    )";
  return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $replaceIntoSQL = "
    REPLACE INTO abre_iready_ela (
      site_id,
      student_id,
      student_grade,
      academic_year,
      number_of_diagnostics_completed,
      lexile_measure,
      lexile_range,
      start_date,
      completion_date,
      time_on_task,
      rush_flag,
      overall_scale_score,
      overall_placement,
      percentile,
      overall_relative_placement,
      tier,
      phonological_awareness_scale_score,
      phonological_awareness_placement,
      phonological_awareness_relative_placement,
      phonics_scale_score,
      phonics_placement,
      phonics_relative_placement,
      high_frequency_words_scale_score,
      high_frequency_words_placement,
      high_frequency_words_relative_placement,
      vocabulary_scale_score,
      vocabulary_placement,
      vocabulary_relative_placement,
      reading_comprehension_literature_scale_score,
      reading_comprehension_literature_placement,
      reading_comprehension_literature_relative_placement,
      reading_comprehension_informational_text_scale_score,
      reading_comprehension_informational_text_placement,
      reading_comprehension_informational_text_relative_placement,
      overall_lessons_passed,
      overall_lessons_failed,
      overall_lessons_completed,
      overall_pass_rate,
      overall_time_on_task,
      phonological_awareness_lessons_passed,
      phonological_awareness_lessons_failed,
      phonological_awareness_lessons_completed,
      phonological_awareness_pass_rate,
      phonological_awareness_time_on_task,
      phonics_lessons_passed,
      phonics_lessons_failed,
      phonics_lessons_completed,
      phonics_pass_rate,
      phonics_time_on_task,
      high_frequency_words_lessons_passed,
      high_frequency_words_lessons_failed,
      high_frequency_words_lessons_completed,
      high_frequency_words_pass_rate,
      high_frequency_words_time_on_task,
      vocabulary_lessons_passed,
      vocabulary_lessons_failed,
      vocabulary_lessons_completed,
      vocabulary_lessons_pass_rate,
      vocabulary_lessons_time_on_task,
      reading_comprehension_lessons_passed,
      reading_comprehension_lessons_failed,
      reading_comprehension_lessons_completed,
      reading_comprehension_lessons_pass_rate,
      reading_comprehension_lessons_time_on_task,
      reading_comprehension_close_reading_lessons_passed,
      reading_comprehension_close_reading_lessons_failed,
      reading_comprehension_close_reading_lessons_completed,
      reading_comprehension_close_reading_lessons_pass_rate,
      reading_comprehension_close_reading_lessons_time_on_task
    )
    SELECT
      ?,
      student_id,
      student_grade,
      academic_year,
      number_of_diagnostics_completed,
      lexile_measure,
      lexile_range,
      start_date,
      completion_date,
      time_on_task,
      rush_flag,
      overall_scale_score,
      overall_placement,
      percentile,
      overall_relative_placement,
      tier,
      phonological_awareness_scale_score,
      phonological_awareness_placement,
      phonological_awareness_relative_placement,
      phonics_scale_score,
      phonics_placement,
      phonics_relative_placement,
      high_frequency_words_scale_score,
      high_frequency_words_placement,
      high_frequency_words_relative_placement,
      vocabulary_scale_score,
      vocabulary_placement,
      vocabulary_relative_placement,
      reading_comprehension_literature_scale_score,
      reading_comprehension_literature_placement,
      reading_comprehension_literature_relative_placement,
      reading_comprehension_informational_text_scale_score,
      reading_comprehension_informational_text_placement,
      reading_comprehension_informational_text_relative_placement,
      overall_lessons_passed,
      overall_lessons_failed,
      overall_lessons_completed,
      overall_pass_rate,
      overall_time_on_task,
      phonological_awareness_lessons_passed,
      phonological_awareness_lessons_failed,
      phonological_awareness_lessons_completed,
      phonological_awareness_pass_rate,
      phonological_awareness_time_on_task,
      phonics_lessons_passed,
      phonics_lessons_failed,
      phonics_lessons_completed,
      phonics_pass_rate,
      phonics_time_on_task,
      high_frequency_words_lessons_passed,
      high_frequency_words_lessons_failed,
      high_frequency_words_lessons_completed,
      high_frequency_words_pass_rate,
      high_frequency_words_time_on_task,
      vocabulary_lessons_passed,
      vocabulary_lessons_failed,
      vocabulary_lessons_completed,
      vocabulary_lessons_pass_rate,
      vocabulary_lessons_time_on_task,
      reading_comprehension_lessons_passed,
      reading_comprehension_lessons_failed,
      reading_comprehension_lessons_completed,
      reading_comprehension_lessons_pass_rate,
      reading_comprehension_lessons_time_on_task,
      reading_comprehension_close_reading_lessons_passed,
      reading_comprehension_close_reading_lessons_failed,
      reading_comprehension_close_reading_lessons_completed,
      reading_comprehension_close_reading_lessons_pass_rate,
      reading_comprehension_close_reading_lessons_time_on_task
    FROM {$temporaryTableName}
  ";
  $stmt = $db->stmt_init();
  $stmt->prepare($replaceIntoSQL);
  $stmt->bind_param('i', $siteID);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>