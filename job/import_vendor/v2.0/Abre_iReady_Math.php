<?php
require(dirname(__FILE__) . '/../../utils/functions.php');
require_once(dirname(__FILE__) . '/../../utils/logging.php');
require(dirname(__FILE__) . '/../../../vendor/autoload.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try{
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }
    $localTempFile = tempnam(sys_get_temp_dir(), 'iready_math');
    $sftp->get('Abre_iReady_Math_v2.0.csv', $localTempFile);
    importiReadyData($db, $siteID, $localTempFile);
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];

  $cronName = 'Abre iReady Math v2';
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}

function importiReadyData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($tempFile, $db, $siteID, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTempTableSQL =  "CREATE TABLE {$temporaryTableName} (
      `student_id` varchar(32) NOT NULL,
      `student_grade` varchar(2) DEFAULT NULL,
      `academic_year` varchar(10) NOT NULL,
      `number_of_diagnostics_completed` tinyint(1) NOT NULL,
      `quantile_measure` varchar(15) DEFAULT NULL,
      `quantile_range` varchar(15) DEFAULT NULL,
      `start_date` date DEFAULT NULL,
      `completion_date` date DEFAULT NULL,
      `time_on_task` smallint(3) DEFAULT NULL,
      `rush_flag` varchar(45) DEFAULT NULL,
      `overall_scale_score` smallint(3) DEFAULT NULL,
      `overall_placement` varchar(10) DEFAULT NULL,
      `percentile` tinyint(2) DEFAULT NULL,
      `overall_relative_placement` varchar(25) DEFAULT NULL,
      `tier` varchar(20) DEFAULT NULL,
      `number_and_operations_scale_score` smallint(3) DEFAULT NULL,
      `number_and_operations_placement` varchar(10) DEFAULT NULL,
      `number_and_operations_relative_placement` varchar(25) DEFAULT NULL,
      `algebra_and_algebraic_thinking_scale_score` smallint(3) DEFAULT NULL,
      `algebra_and_algebraic_thinking_placement` varchar(10) DEFAULT NULL,
      `algebra_and_algebraic_thinking_relative_placement` varchar(25) DEFAULT NULL,
      `measurement_and_data_scale_score` smallint(3) DEFAULT NULL,
      `measurement_and_data_placement` varchar(10) DEFAULT NULL,
      `measurement_and_data_relative_placement` varchar(25) DEFAULT NULL,
      `geometry_scale_score` smallint(3) DEFAULT NULL,
      `geometry_placement` varchar(10) DEFAULT NULL,
      `geometry_relative_placement` varchar(25) DEFAULT NULL,
      `overall_lessons_passed` smallint(3) DEFAULT NULL,
      `overall_lessons_failed` smallint(3) DEFAULT NULL,
      `overall_lessons_completed` smallint(3) DEFAULT NULL,
      `overall_pass_rate` tinyint(3) DEFAULT NULL,
      `overall_time_on_task` decimal(6,1) DEFAULT NULL,
      `number_and_operations_lessons_passed` smallint(3) DEFAULT NULL,
      `number_and_operations_lessons_failed` smallint(3) DEFAULT NULL,
      `number_and_operations_lessons_completed` smallint(3) DEFAULT NULL,
      `number_and_operations_pass_rate` tinyint(3) DEFAULT NULL,
      `number_and_operations_time_on_task` decimal(6,1) DEFAULT NULL,
      `algebra_and_algebraic_thinking_lessons_passed` smallint(3) DEFAULT NULL,
      `algebra_and_algebraic_thinking_lessons_failed` smallint(3) DEFAULT NULL,
      `algebra_and_algebraic_thinking_lessons_completed` smallint(3) DEFAULT NULL,
      `algebra_and_algebraic_thinking_pass_rate` tinyint(3) DEFAULT NULL,
      `algebra_and_algebraic_thinking_time_on_task` decimal(6,1) DEFAULT NULL,
      `measurement_and_data_lessons_passed` smallint(3) DEFAULT NULL,
      `measurement_and_data_lessons_failed` smallint(3) DEFAULT NULL,
      `measurement_and_data_lessons_completed` smallint(3) DEFAULT NULL,
      `measurement_and_data_pass_rate` tinyint(3) DEFAULT NULL,
      `measurement_and_data_time_on_task` decimal(6,1) DEFAULT NULL,
      `geometry_lessons_passed` smallint(3) DEFAULT NULL,
      `geometry_lessons_failed` smallint(3) DEFAULT NULL,
      `geometry_lessons_completed` smallint(3) DEFAULT NULL,
      `geometry_lessons_pass_rate` tinyint(3) DEFAULT NULL,
      `geometry_lessons_time_on_task` decimal(6,1) DEFAULT NULL
    )";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTempTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($fileName, $db, $siteId, $temporaryTableName){
  $fieldNames = "
    INSERT INTO {$temporaryTableName}
    (
      student_id,
      student_grade,
      academic_year,
      number_of_diagnostics_completed,
      quantile_measure,
      quantile_range,
      start_date,
      completion_date,
      time_on_task,
      rush_flag,
      overall_scale_score,
      overall_placement,
      percentile,
      overall_relative_placement,
      tier,
      number_and_operations_scale_score,
      number_and_operations_placement,
      number_and_operations_relative_placement,
      algebra_and_algebraic_thinking_scale_score,
      algebra_and_algebraic_thinking_placement,
      algebra_and_algebraic_thinking_relative_placement,
      measurement_and_data_scale_score,
      measurement_and_data_placement,
      measurement_and_data_relative_placement,
      geometry_scale_score,
      geometry_placement,
      geometry_relative_placement,
      overall_lessons_passed,
      overall_lessons_failed,
      overall_lessons_completed,
      overall_pass_rate,
      overall_time_on_task,
      number_and_operations_lessons_passed,
      number_and_operations_lessons_failed,
      number_and_operations_lessons_completed,
      number_and_operations_pass_rate,
      number_and_operations_time_on_task,
      algebra_and_algebraic_thinking_lessons_passed,
      algebra_and_algebraic_thinking_lessons_failed,
      algebra_and_algebraic_thinking_lessons_completed,
      algebra_and_algebraic_thinking_pass_rate,
      algebra_and_algebraic_thinking_time_on_task,
      measurement_and_data_lessons_passed,
      measurement_and_data_lessons_failed,
      measurement_and_data_lessons_completed,
      measurement_and_data_pass_rate,
      measurement_and_data_time_on_task,
      geometry_lessons_passed,
      geometry_lessons_failed,
      geometry_lessons_completed,
      geometry_lessons_pass_rate,
      geometry_lessons_time_on_task
    )
    VALUES";

  $handle = fopen($fileName, "r");

  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgetcsv($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  $recordsAdded = 0;
  define("IMPORT_SIZE", 100);
  while(($line = fgetcsv($handle)) !== false){
    $studentID = trim($line[2]);
    $academicYear = trim($line[5]);
    $numberOfDiagnosticsCompleted = trim($line[19]);

    if($studentID != ""){
      $numberOfDiagnosticsStored = getNumberOfDiagnosticsStored($db, $siteId, $studentID, $academicYear);
      while($numberOfDiagnosticsStored < $numberOfDiagnosticsCompleted - 1){
        $numberOfDiagnosticsStored++;
        $recordsAdded++;
        switch($numberOfDiagnosticsStored){
          case 1:
            $startingIndex = 47;
            break;
          case 2:
            $startingIndex = 68;
            break;
          case 3:
            $startingIndex = 89;
            break;
          case 4:
            $startingIndex = 110;
            break;
          case 5:
            $startingIndex = 131;
            break;
        }
        array_push($records, buildInsertLine($line, $startingIndex, $numberOfDiagnosticsStored));

        if($recordsAdded > IMPORT_SIZE){
          $temporaryTableInsertSQL = $fieldNames . implode(",", $records) . ";";

          $stmt = $db->stmt_init();
          $stmt->prepare($temporaryTableInsertSQL);
          $stmt->execute();
          $stmt->close();

          $recordsAdded = 0;
          $records = [];
          $temporaryTableInsertSQL = "";
        }
      }
      array_push($records, buildInsertLine($line, 23, $numberOfDiagnosticsCompleted, true));
    }
  }
  $temporaryTableInsertSQL = $fieldNames . implode(",", $records) . ";";

  $stmt = $db->stmt_init();
  $stmt->prepare($temporaryTableInsertSQL);
  $stmt->execute();
  $stmt->close();
}

function getNumberOfDiagnosticsStored($db, $siteID, $studentID, $academicYear){
  $stmt = $db->stmt_init();
	$query = "SELECT COUNT(*)
						FROM abre_iready_math
						WHERE student_id = ? AND site_id = ? AND academic_year = ?";
	$stmt->prepare($query);
	$stmt->bind_param("sis", $studentID, $siteID, $academicYear);
	$stmt->execute();
	$stmt->bind_result($numberOfDiagnosticsStored);

  $stmt->fetch();
  $stmt->close();
  return $numberOfDiagnosticsStored;
}

function buildInsertLine($line, $startingIndex, $diagnosticNumber, $mostRecent = false){
  $studentID = trim($line[2]);
  $studentGrade = trim($line[4]);
  $academicYear = trim($line[5]);
  $numberOfDiagnosticsCompleted = $diagnosticNumber;

  $quantileMeasure = nullCheckWithQuotes($line[32]);
  $quantileRange = nullCheckWithQuotes($line[33]);

  if(trim($line[$startingIndex]) == ""){
    $startDate = "NULL";
  }else{
    $startDate = str_pad(trim($line[$startingIndex]), 10, "0", STR_PAD_LEFT);
    $month = substr($startDate, 0, 2);
    $day = substr($startDate, 3, 2);
    $year = substr($startDate, 6, 4);
    $startDate = "{$year}-{$month}-{$day}";
  }

  if(trim($line[$startingIndex + 1]) == ""){
    $completionDate = "NULL";
  }else{
    $completionDate = str_pad(trim($line[$startingIndex + 1]), 10, "0", STR_PAD_LEFT);
    $month = substr($completionDate, 0, 2);
    $day = substr($completionDate, 3, 2);
    $year = substr($completionDate, 6, 4);
    $completionDate = "{$year}-{$month}-{$day}";
  }

  $timeOnTask = nullCheck($line[$startingIndex + 2]);
  $rushFlag = nullCheckWithQuotes($line[$startingIndex + 3]);

  $overallScaleScore = nullCheck($line[$startingIndex + 4]);
  $overallPlacement = nullCheckWithQuotes($line[$startingIndex + 5]);
  $percentile = nullCheck($line[$startingIndex + 6]);
  $overallRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 7]);
  $tier = nullCheckWithQuotes($line[$startingIndex + 8]);

  //most recent assessments have quantile information inline with the diagnostic results
  if(!$mostRecent){
    $numberAndOperationsScaleScore = nullCheck($line[$startingIndex + 9]);
    $numberAndOperationsPlacement = nullCheckWithQuotes($line[$startingIndex + 10]);
    $numberAndOperationsRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 11]);

    $algebraAndAlgebraicThinkingScaleScore = nullCheck($line[$startingIndex + 12]);
    $algebraAndAlgebraicThinkingPlacement = nullCheckWithQuotes($line[$startingIndex + 13]);
    $algebraAndAlgebraicThinkingRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 14]);

    $measurementAndDataScaleScore = nullCheck($line[$startingIndex + 15]);
    $measurementAndDataPlacement = nullCheckWithQuotes($line[$startingIndex + 16]);
    $measurementAndDataRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 17]);

    $geometryScaleScore = nullCheck($line[$startingIndex + 18]);
    $geometryPlacement = nullCheckWithQuotes($line[$startingIndex + 19]);
    $geometryRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 20]);
  }else{
    $numberAndOperationsScaleScore = nullCheck($line[$startingIndex + 12]);
    $numberAndOperationsPlacement = nullCheckWithQuotes($line[$startingIndex + 13]);
    $numberAndOperationsRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 14]);

    $algebraAndAlgebraicThinkingScaleScore = nullCheck($line[$startingIndex + 15]);
    $algebraAndAlgebraicThinkingPlacement = nullCheckWithQuotes($line[$startingIndex + 16]);
    $algebraAndAlgebraicThinkingRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 17]);

    $measurementAndDataScaleScore = nullCheck($line[$startingIndex + 18]);
    $measurementAndDataPlacement = nullCheckWithQuotes($line[$startingIndex + 19]);
    $measurementAndDataRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 20]);

    $geometryScaleScore = nullCheck($line[$startingIndex + 21]);
    $geometryPlacement = nullCheckWithQuotes($line[$startingIndex + 22]);
    $geometryRelativePlacement = nullCheckWithQuotes($line[$startingIndex + 23]);
  }

  $overallLessonsPassed = nullCheck($line[152]);
  $overallLessonsFailed = nullCheck($line[153]);
  $overallLessonsCompleted = nullCheck($line[154]);
  $overallPassRate = nullCheck($line[155]);
  $overallTimeOnTask = nullCheck($line[156]);

  $numberAndOperationsLessonsPassed = nullCheck($line[157]);
  $numberAndOperationsLessonsFailed = nullCheck($line[158]);
  $numberAndOperationsLessonsCompleted = nullCheck($line[159]);
  $numberAndOperationsPassRate = nullCheck($line[160]);
  $numberAndOperationsTimeOnTask = nullCheck($line[161]);

  $algebraAndAlgebraicThinkingLessonsPassed = nullCheck($line[162]);
  $algebraAndAlgebraicThinkingLessonsFailed = nullCheck($line[163]);
  $algebraAndAlgebraicThinkingLessonsCompleted = nullCheck($line[164]);
  $algebraAndAlgebraicThinkingPassRate = nullCheck($line[165]);
  $algebraAndAlgebraicThinkingTimeOnTask  = nullCheck($line[166]);

  $measurementAndDataLessonsPassed = nullCheck($line[167]);
  $measurementAndDataLessonsFailed = nullCheck($line[168]);
  $measurementAndDataLessonsCompleted = nullCheck($line[169]);
  $measurementAndDataPassRate = nullCheck($line[170]);
  $measurementAndDataTimeOnTask = nullCheck($line[171]);

  $geometryLessonsPassed = nullCheck($line[172]);
  $geometryLessonsFailed = nullCheck($line[173]);
  $geometryLessonsCompleted = nullCheck($line[174]);
  $geometryPassRate = nullCheck($line[175]);
  $geometryTimeOnTask = nullCheck($line[176]);

  $sql = "
    (
      '$studentID',
      '$studentGrade',
      '$academicYear',
      $numberOfDiagnosticsCompleted,

      $quantileMeasure,
      $quantileRange,

      '$startDate',
      '$completionDate',
      $timeOnTask,
      $rushFlag,

      $overallScaleScore,
      $overallPlacement,
      $percentile,
      $overallRelativePlacement,
      $tier,

      $numberAndOperationsScaleScore,
      $numberAndOperationsPlacement,
      $numberAndOperationsRelativePlacement,

      $algebraAndAlgebraicThinkingScaleScore,
      $algebraAndAlgebraicThinkingPlacement,
      $algebraAndAlgebraicThinkingRelativePlacement,

      $measurementAndDataScaleScore,
      $measurementAndDataPlacement,
      $measurementAndDataRelativePlacement,

      $geometryScaleScore,
      $geometryPlacement,
      $geometryRelativePlacement,

      $overallLessonsPassed,
      $overallLessonsFailed,
      $overallLessonsCompleted,
      $overallPassRate,
      $overallTimeOnTask,

      $numberAndOperationsLessonsPassed,
      $numberAndOperationsLessonsFailed,
      $numberAndOperationsLessonsCompleted,
      $numberAndOperationsPassRate,
      $numberAndOperationsTimeOnTask,

      $algebraAndAlgebraicThinkingLessonsPassed,
      $algebraAndAlgebraicThinkingLessonsFailed,
      $algebraAndAlgebraicThinkingLessonsCompleted,
      $algebraAndAlgebraicThinkingPassRate,
      $algebraAndAlgebraicThinkingTimeOnTask,

      $measurementAndDataLessonsPassed,
      $measurementAndDataLessonsFailed,
      $measurementAndDataLessonsCompleted,
      $measurementAndDataPassRate,
      $measurementAndDataTimeOnTask,

      $geometryLessonsPassed,
      $geometryLessonsFailed,
      $geometryLessonsCompleted,
      $geometryPassRate,
      $geometryTimeOnTask
    )";
  return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $replaceIntoSQL = "
    REPLACE INTO abre_iready_math (
      site_id,
      student_id,
      student_grade,
      academic_year,
      number_of_diagnostics_completed,
      quantile_measure,
      quantile_range,
      start_date,
      completion_date,
      time_on_task,
      rush_flag,
      overall_scale_score,
      overall_placement,
      percentile,
      overall_relative_placement,
      tier,
      number_and_operations_scale_score,
      number_and_operations_placement,
      number_and_operations_relative_placement,
      algebra_and_algebraic_thinking_scale_score,
      algebra_and_algebraic_thinking_placement,
      algebra_and_algebraic_thinking_relative_placement,
      measurement_and_data_scale_score,
      measurement_and_data_placement,
      measurement_and_data_relative_placement,
      geometry_scale_score,
      geometry_placement,
      geometry_relative_placement,
      overall_lessons_passed,
      overall_lessons_failed,
      overall_lessons_completed,
      overall_pass_rate,
      overall_time_on_task,
      number_and_operations_lessons_passed,
      number_and_operations_lessons_failed,
      number_and_operations_lessons_completed,
      number_and_operations_pass_rate,
      number_and_operations_time_on_task,
      algebra_and_algebraic_thinking_lessons_passed,
      algebra_and_algebraic_thinking_lessons_failed,
      algebra_and_algebraic_thinking_lessons_completed,
      algebra_and_algebraic_thinking_pass_rate,
      algebra_and_algebraic_thinking_time_on_task,
      measurement_and_data_lessons_passed,
      measurement_and_data_lessons_failed,
      measurement_and_data_lessons_completed,
      measurement_and_data_pass_rate,
      measurement_and_data_time_on_task,
      geometry_lessons_passed,
      geometry_lessons_failed,
      geometry_lessons_completed,
      geometry_lessons_pass_rate,
      geometry_lessons_time_on_task
    )
    SELECT
      ?,
      student_id,
      student_grade,
      academic_year,
      number_of_diagnostics_completed,
      quantile_measure,
      quantile_range,
      start_date,
      completion_date,
      time_on_task,
      rush_flag,
      overall_scale_score,
      overall_placement,
      percentile,
      overall_relative_placement,
      tier,
      number_and_operations_scale_score,
      number_and_operations_placement,
      number_and_operations_relative_placement,
      algebra_and_algebraic_thinking_scale_score,
      algebra_and_algebraic_thinking_placement,
      algebra_and_algebraic_thinking_relative_placement,
      measurement_and_data_scale_score,
      measurement_and_data_placement,
      measurement_and_data_relative_placement,
      geometry_scale_score,
      geometry_placement,
      geometry_relative_placement,
      overall_lessons_passed,
      overall_lessons_failed,
      overall_lessons_completed,
      overall_pass_rate,
      overall_time_on_task,
      number_and_operations_lessons_passed,
      number_and_operations_lessons_failed,
      number_and_operations_lessons_completed,
      number_and_operations_pass_rate,
      number_and_operations_time_on_task,
      algebra_and_algebraic_thinking_lessons_passed,
      algebra_and_algebraic_thinking_lessons_failed,
      algebra_and_algebraic_thinking_lessons_completed,
      algebra_and_algebraic_thinking_pass_rate,
      algebra_and_algebraic_thinking_time_on_task,
      measurement_and_data_lessons_passed,
      measurement_and_data_lessons_failed,
      measurement_and_data_lessons_completed,
      measurement_and_data_pass_rate,
      measurement_and_data_time_on_task,
      geometry_lessons_passed,
      geometry_lessons_failed,
      geometry_lessons_completed,
      geometry_lessons_pass_rate,
      geometry_lessons_time_on_task
    FROM {$temporaryTableName}
  ";
  $stmt = $db->stmt_init();
  $stmt->prepare($replaceIntoSQL);
  $stmt->bind_param('i', $siteID);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
 ?>
