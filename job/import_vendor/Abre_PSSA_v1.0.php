<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config) {
$cronName = 'PSSA Import v1.0';

try {

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("MAX_IMPORT_LIMIT", 100);

    $error = null;
    $skip = null;

    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-pssa');
    $isFileLoaded = $sftp->get('Abre_PSSA_v1.0.csv', $localTempFile);

    if ($isFileLoaded) {
        // Upload SFTP File to Google Cloud Storage Bucket
        $currentDate = date("Ymd");
        $fileName = "Abre_PSSA_v1.0.csv";
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload(file_get_contents($localTempFile), [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);

        // Upload to the other folder location in landing zone bucket
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        $folderName = pathinfo($fileName, PATHINFO_FILENAME);
        $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
        $bucket->upload(file_get_contents($localTempFile), [
            'name' => "$currentDate/filename/$folderName/$modifiedFile"
        ]);

        importPSSAData($db, $siteID, $localTempFile);
    } else {
        $skip = true;
        throw new Exception("No file found.");
    }
} catch (Exception $ex) {
    $error = $ex->getMessage();
}

// gather results
$details = [];
if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
} else {
    $status = CRON_SUCCESS;
}

Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

function importPSSAData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE abre_pssa";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName){
  $temporaryTableColumnsSQL = "
    INSERT INTO {$temporaryTableName}
    (
      subject,
      drc_student_id,
      unique_matching_id,
      pa_secure_id,
      local_id,
      last_name,
      first_name,
      middle_initial,
      birthdate,
      grade,
      tested_year,
      is_math_tested,
      is_ela_tested,
      is_science_tested,
      pasa_record,
      keystone_record,
      lithocode,
      scale_score,
      performance_level_code,
      performance_level_name,
      tested_district_id,
      tested_school_code,
      tested_school_name,
      exclusion_codes,
      total_raw_score,
      mc_raw_score,
      open_ended_raw_score,
      ebsr_raw_score,
      category1_raw_score,
      anchor_1_1,
      anchor_1_2,
      anchor_1_3,
      category2_raw_score,
      anchor_2_1,
      anchor_2_2,
      anchor_2_3,
      anchor_2_4,
      category3_raw_score,
      anchor_3_1,
      anchor_3_2,
      anchor_3_3,
      category4_raw_score,
      anchor_4_1,
      anchor_4_2,
      anchor_4_3,
      category5_raw_score,
      anchor_5_1,
      anchor_5_2,
      anchor_5_3,
      category6_raw_score,
      anchor_6_1,
      anchor_6_2,
      anchor_6_3,
      anchor_6_4,
      category7_raw_score,
      anchor_7_1,
      anchor_7_2,
      anchor_7_3,
      anchor_7_4,
      category8_raw_score,
      anchor_8_1,
      anchor_8_2,
      anchor_8_3,
      anchor_8_4,
      category9_raw_score,
      anchor_9_1,
      anchor_9_2,
      anchor_9_3,
      anchor_9_4,
      category10_raw_score,
      anchor_10_1,
      anchor_10_2,
      anchor_10_3,
      category1_strength_profile,
      category2_strength_profile,
      category3_strength_profile,
      category4_strength_profile,
      category5_strength_profile,
      category6_strength_profile,
      category7_strength_profile,
      category8_strength_profile,
      category9_strength_profile,
      category10_strength_profile,
      code_of_conduct,
      mode,
      braille_format,
      large_print_format,
      computer_assistive_technology,
      some_convention_read_aloud_services,
      all_convention_read_aloud_services,
      some_test_read_aloud,
      all_test_read_aloud,
      test_items_signed,
      test_items_interpreted_el,
      text_dependent_prompts_signed,
      text_dependent_prompts_interpreted_el,
      amplification_device,
      magnification_device,
      color_overlay,
      other_test_accomodation,
      spanish_version,
      audio,
      video_sign_language,
      refreshable_braille,
      color_chooser,
      contrasting_text_chooser,
      reverse_contrast,
      mixed_mode,
      hospital_home_setting,
      one_on_one_setting,
      small_group_setting,
      other_accomodation,
      coordinator_marks_test,
      coordinator_scribed_OE,
      coordinator_transcribed,
      interpreter_translated_signed,
      interpreter_translated_el,
      keyboard_wp_computer_used,
      brailler_note_taker_used,
      augmented_communication_device_used,
      computer_assistive_technology_used,
      translated_dictionary,
      mixed_mode_response,
      other_tool_used,
      extended_time,
      frequent_breaks,
      changed_test_schedule,
      other_accommodations,
      home_school_student,
      optional_field1,
      optional_field2,
      optional_field3,
      optional_field4,
      site_id
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, ",");
    $studentID = trim($lineArray[8]);
    if($studentID != ""){
      array_push($records, buildInsertLine($db, $siteID, $studentID, $lineArray));
    }

    if(count($records) == MAX_IMPORT_LIMIT){
      $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
      $stmt = $db->stmt_init();
      $stmt->prepare($temporaryTableInsertSQL);
      $stmt->execute();
      $stmt->close();

      $records = [];
    }
  }

  if(count($records)){
    $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }
}

function buildInsertLine($db, $siteID, $studentID, $line){
  $subject = $line[0];
  $drc_student_id = $line[5];
  $unique_matching_id = $line[6];
  $pa_secure_id = $line[7];
  $local_id = $line[8];
  $last_name = $db->escape_string($line[9]);
  $first_name = $db->escape_string($line[10]);
  $middle_initial = nullCheckWithQuotes($line[11]);

  //dates do not contain separators. If it is less 12 characters in length
  //add a leading 0 or it will not parse correctly.
  if(strlen($line[12]) != 8 && substr($line[12], 0, 1) != "0"){
    $birthdateRaw = str_pad($line[12], 8, "0", STR_PAD_LEFT);
  }else{
    $birthdateRaw = $line[12];
  }
  $birthDateTime = date_create_from_format("ndY", $birthdateRaw);
  if($birthDateTime !== false){
    $birthdate = "'".$birthDateTime->format("Y-m-d")."'";
  }else{
    $birthdate = 'NULL';
  }

  $grade = nullCheck($line[13]);
  $tested_year = $line[14];
  $is_math_tested = parseFlagString($line[15]);
  $is_ela_tested = parseFlagString($line[16]);
  $is_science_tested = parseFlagString($line[17]);
  $pasa_record = parseFlagString($line[19]);
  $keystone_record = $line[20];
  $lithocode = $line[21];
  $scale_score = nullCheck($line[54]);
  $performance_level_code = nullCheck($line[55]);
  $performance_level_name = nullCheckWithQuotes($line[56]);
  $tested_district_id = nullCheckWithQuotes($line[58]);
  $tested_school_code = nullCheckWithQuotes($line[59]);
  $tested_school_name = nullCheckWithQuotes($line[60]);
  $exclusion_codes = nullCheckWithQuotes($line[66]);
  $total_raw_score = nullCheck($line[67]);
  $mc_raw_score = nullCheck($line[68]);
  $open_ended_raw_score = nullCheck($line[69]);
  $ebsr_raw_score = nullCheck($line[70]);
  $category1_raw_score = nullCheckWithQuotes($line[71]);
  $anchor_1_1 = nullCheck($line[72]);
  $anchor_1_2 = nullCheck($line[73]);
  $anchor_1_3 = nullCheck($line[74]);
  $category2_raw_score = nullCheckWithQuotes($line[75]);
  $anchor_2_1 = nullCheck($line[76]);
  $anchor_2_2 = nullCheck($line[77]);
  $anchor_2_3 = nullCheck($line[78]);
  $anchor_2_4 = nullCheck($line[79]);
  $category3_raw_score = nullCheckWithQuotes($line[80]);
  $anchor_3_1 = nullCheck($line[81]);
  $anchor_3_2 = nullCheck($line[82]);
  $anchor_3_3 = nullCheck($line[83]);
  $category4_raw_score = nullCheckWithQuotes($line[84]);
  $anchor_4_1 = nullCheck($line[85]);
  $anchor_4_2 = nullCheck($line[86]);
  $anchor_4_3 = nullCheck($line[87]);
  $category5_raw_score = nullCheckWithQuotes($line[88]);
  $anchor_5_1 = nullCheck($line[89]);
  $anchor_5_2 = nullCheck($line[90]);
  $anchor_5_3 = nullCheck($line[91]);
  $category6_raw_score = nullCheckWithQuotes($line[92]);
  $anchor_6_1 = nullCheck($line[93]);
  $anchor_6_2 = nullCheck($line[94]);
  $anchor_6_3 = nullCheck($line[95]);
  $anchor_6_4 = nullCheck($line[96]);
  $category7_raw_score = nullCheckWithQuotes($line[97]);
  $anchor_7_1 = nullCheck($line[98]);
  $anchor_7_2 = nullCheck($line[99]);
  $anchor_7_3 = nullCheck($line[100]);
  $anchor_7_4 = nullCheck($line[101]);
  $category8_raw_score = nullCheckWithQuotes($line[102]);
  $anchor_8_1 = nullCheck($line[103]);
  $anchor_8_2 = nullCheck($line[104]);
  $anchor_8_3 = nullCheck($line[105]);
  $anchor_8_4 = nullCheck($line[106]);
  $category9_raw_score = nullCheckWithQuotes($line[107]);
  $anchor_9_1 = nullCheck($line[108]);
  $anchor_9_2 = nullCheck($line[109]);
  $anchor_9_3 = nullCheck($line[110]);
  $anchor_9_4 = nullCheck($line[111]);
  $category10_raw_score = nullCheckWithQuotes($line[112]);
  $anchor_10_1 = nullCheck($line[113]);
  $anchor_10_2 = nullCheck($line[114]);
  $anchor_10_3 = nullCheck($line[115]);
  $category1_strength_profile = nullCheckWithQuotes($line[116]);
  $category2_strength_profile = nullCheckWithQuotes($line[117]);
  $category3_strength_profile = nullCheckWithQuotes($line[118]);
  $category4_strength_profile = nullCheckWithQuotes($line[119]);
  $category5_strength_profile = nullCheckWithQuotes($line[120]);
  $category6_strength_profile = nullCheckWithQuotes($line[121]);
  $category7_strength_profile = nullCheckWithQuotes($line[122]);
  $category8_strength_profile = nullCheckWithQuotes($line[123]);
  $category9_strength_profile = nullCheckWithQuotes($line[124]);
  $category10_strength_profile = nullCheckWithQuotes($line[125]);
  $code_of_conduct = parseFlagString($line[138]);
  $mode = nullCheckWithQuotes($line[139]);
  $braille_format = parseFlagString($line[140]);
  $large_print_format = parseFlagString($line[141]);
  $computer_assistive_technology = parseFlagString($line[142]);
  $some_convention_read_aloud_services = parseFlagString($line[143]);
  $all_convention_read_aloud_services = parseFlagString($line[144]);
  $some_test_read_aloud = parseFlagString($line[145]);
  $all_test_read_aloud = parseFlagString($line[146]);
  $test_items_signed = parseFlagString($line[147]);
  $test_items_interpreted_el = parseFlagString($line[148]);
  $text_dependent_prompts_signed = parseFlagString($line[149]);
  $text_dependent_prompts_interpreted_el = parseFlagString($line[150]);
  $amplification_device = parseFlagString($line[151]);
  $magnification_device = parseFlagString($line[152]);
  $color_overlay = parseFlagString($line[153]);
  $other_test_accomodation = parseFlagString($line[154]);
  $spanish_version = parseFlagString($line[155]);
  $audio = parseFlagString($line[156]);
  $video_sign_language = parseFlagString($line[157]);
  $refreshable_braille = parseFlagString($line[158]);
  $color_chooser = parseFlagString($line[159]);
  $contrasting_text_chooser = parseFlagString($line[160]);
  $reverse_contrast = parseFlagString($line[161]);
  $mixed_mode = parseFlagString($line[162]);
  $hospital_home_setting = parseFlagString($line[163]);
  $one_on_one_setting = parseFlagString($line[164]);
  $small_group_setting = parseFlagString($line[165]);
  $other_accomodation = parseFlagString($line[166]);
  $coordinator_marks_test = parseFlagString($line[167]);
  $coordinator_scribed_OE = parseFlagString($line[168]);
  $coordinator_transcribed = parseFlagString($line[169]);
  $interpreter_translated_signed = parseFlagString($line[170]);
  $interpreter_translated_el = parseFlagString($line[171]);
  $keyboard_wp_computer_used = parseFlagString($line[172]);
  $brailler_note_taker_used = parseFlagString($line[173]);
  $augmented_communication_device_used = parseFlagString($line[174]);
  $computer_assistive_technology_used = parseFlagString($line[175]);
  $translated_dictionary = parseFlagString($line[176]);
  $mixed_mode_response = parseFlagString($line[177]);
  $other_tool_used = parseFlagString($line[178]);
  $extended_time = parseFlagString($line[179]);
  $frequent_breaks = parseFlagString($line[180]);
  $changed_test_schedule = parseFlagString($line[181]);
  $other_accommodations = parseFlagString($line[182]);
  $home_school_student = parseFlagString($line[183]);
  $optional_field1 = parseFlagString($line[184]);
  $optional_field2 = parseFlagString($line[185]);
  $optional_field3 = parseFlagString($line[186]);
  $optional_field4 = parseFlagString($line[187]);

  $sql = "
    (
      '$subject',
      '$drc_student_id',
      '$unique_matching_id',
      '$pa_secure_id',
      '$local_id',
      '$last_name',
      '$first_name',
      $middle_initial,
      $birthdate,
      $grade,
      '$tested_year',
      $is_math_tested,
      $is_ela_tested,
      $is_science_tested,
      $pasa_record,
      $keystone_record,
      $lithocode,
      $scale_score,
      $performance_level_code,
      $performance_level_name,
      $tested_district_id,
      $tested_school_code,
      $tested_school_name,
      $exclusion_codes,
      $total_raw_score,
      $mc_raw_score,
      $open_ended_raw_score,
      $ebsr_raw_score,
      $category1_raw_score,
      $anchor_1_1,
      $anchor_1_2,
      $anchor_1_3,
      $category2_raw_score,
      $anchor_2_1,
      $anchor_2_2,
      $anchor_2_3,
      $anchor_2_4,
      $category3_raw_score,
      $anchor_3_1,
      $anchor_3_2,
      $anchor_3_3,
      $category4_raw_score,
      $anchor_4_1,
      $anchor_4_2,
      $anchor_4_3,
      $category5_raw_score,
      $anchor_5_1,
      $anchor_5_2,
      $anchor_5_3,
      $category6_raw_score,
      $anchor_6_1,
      $anchor_6_2,
      $anchor_6_3,
      $anchor_6_4,
      $category7_raw_score,
      $anchor_7_1,
      $anchor_7_2,
      $anchor_7_3,
      $anchor_7_4,
      $category8_raw_score,
      $anchor_8_1,
      $anchor_8_2,
      $anchor_8_3,
      $anchor_8_4,
      $category9_raw_score,
      $anchor_9_1,
      $anchor_9_2,
      $anchor_9_3,
      $anchor_9_4,
      $category10_raw_score,
      $anchor_10_1,
      $anchor_10_2,
      $anchor_10_3,
      $category1_strength_profile,
      $category2_strength_profile,
      $category3_strength_profile,
      $category4_strength_profile,
      $category5_strength_profile,
      $category6_strength_profile,
      $category7_strength_profile,
      $category8_strength_profile,
      $category9_strength_profile,
      $category10_strength_profile,
      $code_of_conduct,
      $mode,
      $braille_format,
      $large_print_format,
      $computer_assistive_technology,
      $some_convention_read_aloud_services,
      $all_convention_read_aloud_services,
      $some_test_read_aloud,
      $all_test_read_aloud,
      $test_items_signed,
      $test_items_interpreted_el,
      $text_dependent_prompts_signed,
      $text_dependent_prompts_interpreted_el,
      $amplification_device,
      $magnification_device,
      $color_overlay,
      $other_test_accomodation,
      $spanish_version,
      $audio,
      $video_sign_language,
      $refreshable_braille,
      $color_chooser,
      $contrasting_text_chooser,
      $reverse_contrast,
      $mixed_mode,
      $hospital_home_setting,
      $one_on_one_setting,
      $small_group_setting,
      $other_accomodation,
      $coordinator_marks_test,
      $coordinator_scribed_OE,
      $coordinator_transcribed,
      $interpreter_translated_signed,
      $interpreter_translated_el,
      $keyboard_wp_computer_used,
      $brailler_note_taker_used,
      $augmented_communication_device_used,
      $computer_assistive_technology_used,
      $translated_dictionary,
      $mixed_mode_response,
      $other_tool_used,
      $extended_time,
      $frequent_breaks,
      $changed_test_schedule,
      $other_accommodations,
      $home_school_student,
      $optional_field1,
      $optional_field2,
      $optional_field3,
      $optional_field4,
      $siteID
    )";
  return $sql;
}

function parseFlagString($str){
  if($str == "Y"){
    return 1;
  }
  if($str == "N"){
    return 0;
  }
  return 'NULL';
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $dropIDColumn = "ALTER TABLE {$temporaryTableName} DROP id";
  $dropIDStmt = $db->stmt_init();
  $dropIDStmt->prepare($dropIDColumn);
  $dropIDStmt->execute();
  $dropIDStmt->close();

  $insertSQL = "INSERT INTO abre_pssa
                  SELECT 0, t.* FROM {$temporaryTableName} t
                ON DUPLICATE KEY UPDATE
                `subject` = t.subject,
                drc_student_id = t.drc_student_id,
                unique_matching_id = t.unique_matching_id,
                pa_secure_id = t.pa_secure_id,
                last_name = t.last_name,
                first_name = t.first_name,
                middle_initial = t.middle_initial,
                birthdate = t.birthdate,
                grade = t.grade,
                is_math_tested = t.is_math_tested,
                is_ela_tested = t.is_ela_tested,
                is_science_tested = t.is_science_tested,
                pasa_record = t.pasa_record,
                keystone_record = t.keystone_record,
                lithocode = t.lithocode,
                scale_score = t.scale_score,
                performance_level_code = t.performance_level_code,
                performance_level_name = t.performance_level_name,
                tested_district_id = t.tested_district_id,
                tested_school_code = t.tested_school_code,
                tested_school_name = t.tested_school_name,
                exclusion_codes = t.exclusion_codes,
                total_raw_score = t.total_raw_score,
                mc_raw_score = t.mc_raw_score,
                open_ended_raw_score = t.open_ended_raw_score,
                ebsr_raw_score = t.ebsr_raw_score,
                category1_raw_score = t.category1_raw_score,
                anchor_1_1 = t.anchor_1_1,
                anchor_1_2 = t.anchor_1_2,
                anchor_1_3 = t.anchor_1_3,
                category2_raw_score = t.category2_raw_score,
                anchor_2_1 = t.anchor_2_1,
                anchor_2_2 = t.anchor_2_2,
                anchor_2_3 = t.anchor_2_3,
                anchor_2_4 = t.anchor_2_4,
                category3_raw_score = t.category3_raw_score,
                anchor_3_1 = t.anchor_3_1,
                anchor_3_2 = t.anchor_3_2,
                anchor_3_3 = t.anchor_3_3,
                category4_raw_score = t.category4_raw_score,
                anchor_4_1 = t.anchor_4_1,
                anchor_4_2 = t.anchor_4_2,
                anchor_4_3 = t.anchor_4_3,
                category5_raw_score = t.category5_raw_score,
                anchor_5_1 = t.anchor_5_1,
                anchor_5_2 = t.anchor_5_2,
                anchor_5_3 = t.anchor_5_3,
                category6_raw_score = t.category6_raw_score,
                anchor_6_1 = t.anchor_6_1,
                anchor_6_2 = t.anchor_6_2,
                anchor_6_3 = t.anchor_6_3,
                anchor_6_4 = t.anchor_6_4,
                category7_raw_score = t.category7_raw_score,
                anchor_7_1 = t.anchor_7_1,
                anchor_7_2 = t.anchor_7_2,
                anchor_7_3 = t.anchor_7_3,
                anchor_7_4 = t.anchor_7_4,
                category8_raw_score = t.category8_raw_score,
                anchor_8_1 = t.anchor_8_1,
                anchor_8_2 = t.anchor_8_2,
                anchor_8_3 = t.anchor_8_3,
                anchor_8_4 = t.anchor_8_4,
                category9_raw_score = t.category9_raw_score,
                anchor_9_1 = t.anchor_9_1,
                anchor_9_2 = t.anchor_9_2,
                anchor_9_3 = t.anchor_9_3,
                anchor_9_4 = t.anchor_9_4,
                category10_raw_score = t.category10_raw_score,
                anchor_10_1 = t.anchor_10_1,
                anchor_10_2 = t.anchor_10_2,
                anchor_10_3 = t.anchor_10_3,
                category1_strength_profile = t.category1_strength_profile,
                category2_strength_profile = t.category2_strength_profile,
                category3_strength_profile = t.category3_strength_profile,
                category4_strength_profile = t.category4_strength_profile,
                category5_strength_profile = t.category5_strength_profile,
                category6_strength_profile = t.category6_strength_profile,
                category7_strength_profile = t.category7_strength_profile,
                category8_strength_profile = t.category8_strength_profile,
                category9_strength_profile = t.category9_strength_profile,
                category10_strength_profile = t.category10_strength_profile,
                code_of_conduct = t.code_of_conduct,
                mode = t.mode,
                braille_format = t.braille_format,
                large_print_format = t.large_print_format,
                computer_assistive_technology = t.computer_assistive_technology,
                some_convention_read_aloud_services = t.some_convention_read_aloud_services,
                all_convention_read_aloud_services = t.all_convention_read_aloud_services,
                some_test_read_aloud = t.some_test_read_aloud,
                all_test_read_aloud = t.all_test_read_aloud,
                test_items_signed = t.test_items_signed,
                test_items_interpreted_el = t.test_items_interpreted_el,
                text_dependent_prompts_signed = t.text_dependent_prompts_signed,
                text_dependent_prompts_interpreted_el = t.text_dependent_prompts_interpreted_el,
                amplification_device = t.amplification_device,
                magnification_device = t.magnification_device,
                color_overlay = t.color_overlay,
                other_test_accomodation = t.other_test_accomodation,
                spanish_version = t.spanish_version,
                audio = t.audio,
                video_sign_language = t.video_sign_language,
                refreshable_braille = t.refreshable_braille,
                color_chooser = t.color_chooser,
                contrasting_text_chooser = t.contrasting_text_chooser,
                reverse_contrast = t.reverse_contrast,
                mixed_mode = t.mixed_mode,
                hospital_home_setting = t.hospital_home_setting,
                one_on_one_setting = t.one_on_one_setting,
                small_group_setting = t.small_group_setting,
                other_accomodation = t.other_accomodation,
                coordinator_marks_test = t.coordinator_marks_test,
                coordinator_scribed_OE = t.coordinator_scribed_OE,
                coordinator_transcribed = t.coordinator_transcribed,
                interpreter_translated_signed = t.interpreter_translated_signed,
                interpreter_translated_el = t.interpreter_translated_el,
                keyboard_wp_computer_used = t.keyboard_wp_computer_used,
                brailler_note_taker_used = t.brailler_note_taker_used,
                augmented_communication_device_used = t.augmented_communication_device_used,
                computer_assistive_technology_used = t.computer_assistive_technology_used,
                translated_dictionary = t.translated_dictionary,
                mixed_mode_response = t.mixed_mode_response,
                other_tool_used = t.other_tool_used,
                extended_time = t.extended_time,
                frequent_breaks = t.frequent_breaks,
                changed_test_schedule = t.changed_test_schedule,
                other_accommodations = t.other_accommodations,
                home_school_student = t.home_school_student,
                optional_field1 = t.optional_field1,
                optional_field2 = t.optional_field2,
                optional_field3 = t.optional_field3,
                optional_field4 = t.optional_field4";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>