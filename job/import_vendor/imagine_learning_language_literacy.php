<?php

/*
* Copyright Abre.io Inc.
*/

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use phpseclib\Crypt\RSA;
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

class ImagineLearningLanguageLiteracyImporter
{
    const SAFE_COLUMN_COUNT = 23;
    const MAX_IMPORT_LIMIT = 25;
    const MAX_DELETE_ATTEMPTS = 3;
    const DELETE_BATCH_SIZE = 500;
    const MAX_DELETE_BATCHES = 5000;
    const MAX_LOCK_RETRIES = 5;
    const MAX_TOTAL_LOCK_TIME = 1800;

    private $db;
    private $siteId;
    private $config;
    private $sftp;
    private $storage;

    private $cronName = 'Imagine Learning Language Literacy';
    private $fileName = 'Wkly_CA_PerrisElementarySD_ILL_Benchmark_PreK-6_'; 
    private $separator = "\r\n";
    private $columnSeparator = ',';
    private $currentSchoolYearId;
    private $uuid;
    private $error = null;
    private $skip = null;

    /**
     * Constructor
     * @param object $db Database connection
     * @param string $siteId
     * @param object $config Configuration object
     * @param StorageClient $storage Google Cloud Storage client instance
     */
    public function __construct($db, $siteId, $config, StorageClient $storage)
    {
        $this->db = $db;
        $this->siteId = $siteId;
        $this->config = $config;
        $this->currentSchoolYearId = getCurrentSchoolYearID($this->db);
        $this->storage = $storage;
    }

    /**
     * Run the import job
     */
    public function run()
    {
        $rowCount = 0;
        try {
            $this->_logStart();
            $this->_connectSftp();
            $fileContent = $this->_findAndDownloadFile();
            $this->_validateFileContent($fileContent);
            $this->_uploadToGCS($fileContent);
            $rowCount = $this->_processData($fileContent);
        } catch (Exception $ex) {
            $this->error = $ex->getMessage();
        } finally {
            $this->_logFinish($rowCount);
        }
    }

    /**
     * Log the start of the cron job
     */
    private function _logStart()
    {
        $this->uuid = Logger::logCronStart($this->db, $this->siteId, $this->cronName);
    }

    /**
     * Log the end of the cron job
     * @param int $rowCount Number of rows inserted
     */
    private function _logFinish($rowCount = 0)
    {
        $details = [];
        if (!is_null($this->error)) {
            $details['error'] = $this->error;
            $status = !is_null($this->skip) && $this->skip ? CRON_NOT_RUN : CRON_FAILURE;
        } else {
            $details = ['rowsInserted' => $rowCount];
            $status = CRON_SUCCESS;
        }
        Logger::logCronFinish($this->db, $this->siteId, $this->cronName, $status, $details, $this->uuid);
    }

    /**
     * Connect to the SFTP server
     * @throws Exception If connection or login fails
     */
    private function _connectSftp()
    {
        $this->sftp = new SFTP($this->config->imaginelearning->host);
        $key = new RSA();
        $keyPath = '/opt/cron-js/' . $this->config->imaginelearning->keyPath; 
        if (!file_exists($keyPath)) {
            throw new Exception('SFTP key file not found: ' . $keyPath);
        }
        $key->loadKey(file_get_contents($keyPath));
        if (!$this->sftp->login($this->config->imaginelearning->userName, $key)) {
            throw new Exception('Login to SFTP failed.');
        }
    }

    /**
     * Find the latest file on SFTP and download its content
     * @return string File content
     * @throws Exception If no file is found or content cannot be retrieved
     */
    private function _findAndDownloadFile()
    {
        $directory = $this->config->imaginelearning->directory ?? '/'; 
        $files = $this->sftp->nlist($directory);

        if ($files === false) {
             throw new Exception('Failed to list files in SFTP directory: ' . $directory);
        }

        $prefix = $this->fileName;
        $filteredFiles = array_filter($files, function ($file) use ($prefix) {
            return $file !== '.' && $file !== '..' && strpos($file, $prefix) === 0; 
        });

        rsort($filteredFiles);

        if (empty($filteredFiles)) {
            $this->skip = true;
            throw new Exception('No file found matching prefix: ' . $this->fileName); 
        }

        $latestFile = reset($filteredFiles);
        $filePath = rtrim($directory, '/') . '/' . $latestFile;
        $cronFile = $this->sftp->get($filePath);

        if ($cronFile === false) {
            $this->skip = true;
            throw new Exception('Could not retrieve file content from: ' . $filePath);
        }
        return $cronFile;
    }

    /**
     * Validate the structure and content of the downloaded file
     * @param string $fileContent
     * @throws Exception If file is empty, has only headers, or no valid data rows
     */
    private function _validateFileContent($fileContent)
    {
        $fileDetails = getFileStructure($fileContent, $this->separator, self::SAFE_COLUMN_COUNT, $this->columnSeparator); 
        if ($fileDetails['isEmpty']) {
            $this->skip = true;
            throw new Exception('File is empty.');
        } elseif ($fileDetails['hasHeaderRow'] && !$fileDetails['hasDataRow']) {
            $this->skip = true;
            throw new Exception('File only contains a header row.');
        } elseif (!$fileDetails['hasValidDataRow']) {
            throw new Exception('No valid data row found.');
        }
    }

    /**
     * Upload file content to Google Cloud Storage
     * @param string $fileContent
     */
    private function _uploadToGCS($fileContent)
    {
        $currentDate = date('Ymd');
        $bucketName = 'prd-landing-zone'; 
        $bucket = $this->storage->bucket($bucketName);

        $downloadedFileName = $this->_findLatestFileNameOnSftp();
        
        if ($downloadedFileName === null) {
             $gcsFileName = $this->fileName . date('YmdHis') . '.csv';
             error_log("Could not determine exact SFTP filename for site {$this->siteId} (Literacy), using fallback name: {$gcsFileName}");
        } else {
            $gcsFileName = $downloadedFileName;
        }

        $gcsPathSite = "$currentDate/site-id/{$this->siteId}/{$gcsFileName}";
        $bucket->upload($fileContent, ['name' => $gcsPathSite]);

        $folderName = $this->fileName; 
        $gcsPathFilename = "$currentDate/filename/{$folderName}/{$gcsFileName}";
        $bucket->upload($fileContent, ['name' => $gcsPathFilename]);
    }

    /**
     * Helper function to get the exact name of the latest file found on SFTP.
     * @return string|null The filename or null if not found/connected.
     */
    private function _findLatestFileNameOnSftp()
    {
        if (!$this->sftp || !$this->sftp->isConnected()) {
             error_log("SFTP not connected, cannot find latest file name.");
             return null;
        }
        try {
            $directory = $this->config->imaginelearning->directory ?? '/'; 
            $files = $this->sftp->nlist($directory);
            if ($files === false) return null;

            $prefix = $this->fileName;
            $filteredFiles = array_filter($files, function ($file) use ($prefix) {
                return $file !== '.' && $file !== '..' && strpos($file, $prefix) === 0;
            });
            if (empty($filteredFiles)) return null;

            rsort($filteredFiles);
            return reset($filteredFiles);
        } catch (Exception $e) {
            error_log("Error finding latest SFTP file name: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Process the data: delete existing records and insert new ones
     * @param string $fileContent
     * @throws Exception If deletion fails
     * @return int Number of rows processed
     */
    private function _processData($fileContent)
    {
        $this->_deleteExistingData();

        $rowCounter = 0;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO vendor_imagine_learning_language_literacy (
            district_name, school_name, first_name, last_name, grade, 
            numerical_grade, student_number, alternate_student_number, student_tag, username,
            embedded_literacy_benchmark_1_score, embedded_literacy_benchmark_2_score, embedded_literacy_benchmark_3_score,
            embedded_oral_vocabulary_benchmark_1_score, embedded_oral_vocabulary_benchmark_2_score, embedded_oral_vocabulary_benchmark_3_score,
            map_growth_fall_score, rla_boy_score, rla_boy_printscore, rla_moy_score, 
            rla_moy_printscore, rla_eoy_score, rla_eoy_printscore,
            site_id, school_year_id
        ) VALUES ";

        $lines = explode($this->separator, $fileContent);
        if (empty(end($lines))) {
            array_pop($lines);
        }

        $isFirstLine = true;

        foreach ($lines as $line) {
            if ($isFirstLine) {
                $isFirstLine = false;
                continue;
            }
            if (empty(trim($line))) continue;

            $data = str_getcsv($line, $this->columnSeparator);

            // Use SAFE_COLUMN_COUNT for validation
            if (count($data) >= self::SAFE_COLUMN_COUNT) { 
                $rowCounter++;
                $valuesToImport[] = $this->_prepareInsertValues($data);

                if (count($valuesToImport) >= self::MAX_IMPORT_LIMIT) {
                    $this->_insertBatch($dbColumns, $valuesToImport);
                    $valuesToImport = [];
                }
            } else {
                 error_log("Skipping row due to insufficient column count: " . count($data) . " vs expected >= " . self::SAFE_COLUMN_COUNT . " for site ID " . $this->siteId . " Line: " . $line);
            }
        }

        if (!empty($valuesToImport)) {
            $this->_insertBatch($dbColumns, $valuesToImport);
        }
        
        return $rowCounter;
    }

    /**
     * Prepare a single row's values for SQL insertion
     * @param array $data Row data from CSV
     * @return string Formatted values string for INSERT
     */
    private function _prepareInsertValues(array $data)
    {
        // Map CSV columns
        $district_name = trim($this->db->escape_string($data[0] ?? ''));
        $school_name = trim($this->db->escape_string($data[1] ?? ''));
        $first_name = trim($this->db->escape_string($data[2] ?? ''));
        $last_name = trim($this->db->escape_string($data[3] ?? ''));
        $grade = trim($this->db->escape_string($data[4] ?? ''));
        $numerical_grade = trim($this->db->escape_string($data[5] ?? ''));
        $student_number = trim($this->db->escape_string($data[6] ?? ''));
        $alternate_student_number = trim($this->db->escape_string($data[7] ?? ''));
        $student_tag = trim($this->db->escape_string($data[8] ?? ''));
        $username = trim($this->db->escape_string($data[9] ?? ''));
        
        // Helper function to safely convert to int or return 'NULL'
        $toIntOrNull = function($value) {
            $trimmedValue = trim($value);
            if ($trimmedValue === '' || !is_numeric($trimmedValue)) {
                return 'NULL';
            }
            return intval($trimmedValue);
        };

        $embedded_literacy_benchmark_1_score = $toIntOrNull($data[10] ?? null);
        $embedded_literacy_benchmark_2_score = $toIntOrNull($data[11] ?? null);
        $embedded_literacy_benchmark_3_score = $toIntOrNull($data[12] ?? null);
        $embedded_oral_vocabulary_benchmark_1_score = $toIntOrNull($data[13] ?? null);
        $embedded_oral_vocabulary_benchmark_2_score = $toIntOrNull($data[14] ?? null);
        $embedded_oral_vocabulary_benchmark_3_score = $toIntOrNull($data[15] ?? null);
        $map_growth_fall_score = $toIntOrNull($data[16] ?? null);
        $rla_boy_score = $toIntOrNull($data[17] ?? null);
        $rla_moy_score = $toIntOrNull($data[19] ?? null);
        $rla_eoy_score = $toIntOrNull($data[21] ?? null);

        // Text fields that might be scores but stored as text
        $rla_boy_printscore = trim($this->db->escape_string($data[18] ?? ''));
        $rla_moy_printscore = trim($this->db->escape_string($data[20] ?? ''));
        $rla_eoy_printscore = trim($this->db->escape_string($data[22] ?? ''));

        // Helper function to format integer or NULL values
        $formatSqlValue = function($value) {
             if ($value === 'NULL') {
                return 'NULL';
            } 
            elseif (is_int($value)) { 
                return $value; 
            } 
            else { 
                return "'" . $this->db->escape_string((string)$value) . "'";
            }
        };

        // Format scores for SQL insert
        $sql_embedded_literacy_benchmark_1_score = $formatSqlValue($embedded_literacy_benchmark_1_score);
        $sql_embedded_literacy_benchmark_2_score = $formatSqlValue($embedded_literacy_benchmark_2_score);
        $sql_embedded_literacy_benchmark_3_score = $formatSqlValue($embedded_literacy_benchmark_3_score);
        $sql_embedded_oral_vocabulary_benchmark_1_score = $formatSqlValue($embedded_oral_vocabulary_benchmark_1_score);
        $sql_embedded_oral_vocabulary_benchmark_2_score = $formatSqlValue($embedded_oral_vocabulary_benchmark_2_score);
        $sql_embedded_oral_vocabulary_benchmark_3_score = $formatSqlValue($embedded_oral_vocabulary_benchmark_3_score);
        $sql_map_growth_fall_score = $formatSqlValue($map_growth_fall_score);
        $sql_rla_boy_score = $formatSqlValue($rla_boy_score);
        $sql_rla_moy_score = $formatSqlValue($rla_moy_score);
        $sql_rla_eoy_score = $formatSqlValue($rla_eoy_score);
        
        // Format text fields
        $sql_rla_boy_printscore = "'" . $this->db->escape_string($rla_boy_printscore) . "'";
        $sql_rla_moy_printscore = "'" . $this->db->escape_string($rla_moy_printscore) . "'";
        $sql_rla_eoy_printscore = "'" . $this->db->escape_string($rla_eoy_printscore) . "'";


        return "(
            '$district_name', '$school_name', '$first_name', '$last_name', '$grade',
            '$numerical_grade', '$student_number', '$alternate_student_number', '$student_tag', '$username',
            {$sql_embedded_literacy_benchmark_1_score}, {$sql_embedded_literacy_benchmark_2_score}, {$sql_embedded_literacy_benchmark_3_score},
            {$sql_embedded_oral_vocabulary_benchmark_1_score}, {$sql_embedded_oral_vocabulary_benchmark_2_score}, {$sql_embedded_oral_vocabulary_benchmark_3_score},
            {$sql_map_growth_fall_score}, {$sql_rla_boy_score}, {$sql_rla_boy_printscore}, {$sql_rla_moy_score}, 
            {$sql_rla_moy_printscore}, {$sql_rla_eoy_score}, {$sql_rla_eoy_printscore},
            {$this->siteId}, {$this->currentSchoolYearId}
        )";
    }

    /**
     * Insert a batch of rows into the database
     * @param string $dbColumns INSERT INTO statement prefix
     * @param array $valuesToImport Array of value strings
     */
    private function _insertBatch($dbColumns, array $valuesToImport)
    {
        insertRows($this->db, $dbColumns, $valuesToImport);
    }

    /**
     * Delete existing data with retry logic
     * @throws Exception If deletion fails after multiple attempts
     * @return bool True if deletion was successful
     */
    private function _deleteExistingData()
    {
        for ($attempt = 1; $attempt <= self::MAX_DELETE_ATTEMPTS; $attempt++) {
            $deleteSuccess = $this->_attemptToDeleteExistingData();
            if ($deleteSuccess) {
                if ($this->_checkIfDataDeleted()) {
                    return true;
                } else {
                    error_log("Delete attempt {$attempt} reported success, but verification check failed for site {$this->siteId}. Data might remain.");
                }
            }
            error_log("Delete attempt {$attempt} failed for site {$this->siteId}. Retrying...");
            if ($attempt < self::MAX_DELETE_ATTEMPTS) {
                 sleep($this->_calculateBackoffDelaySeconds($attempt));
            }
        }
        throw new Exception('Could not delete existing data for site ' . $this->siteId . ' after ' . self::MAX_DELETE_ATTEMPTS . ' attempts.');
    }


    /**
     * Attempt to delete existing data in batches with lock handling
     * @throws Exception On non-recoverable database error
     * @return bool True if the delete process completed without hitting max lock time or non-recoverable errors (even if max batches was reached), false otherwise.
     */
    private function _attemptToDeleteExistingData()
    {
        $batchCount = 0;
        $deletedRows = 1;
        $totalLockWaitSeconds = 0;
        $maxTotalLockTimeSeconds = self::MAX_TOTAL_LOCK_TIME;

        do {
            $lockRetries = 0;
            $batchSuccess = false;
            
            while (!$batchSuccess && $lockRetries < self::MAX_LOCK_RETRIES) {
                if ($totalLockWaitSeconds >= $maxTotalLockTimeSeconds) {
                     error_log("Delete operation exceeded max total lock wait time ({$maxTotalLockTimeSeconds}s) for site {$this->siteId}.");
                     return false;
                }
                
                try {
                    // Small delay between batches only if not retrying
                    if ($batchCount > 0 && $lockRetries == 0) usleep(100000); 

                    $query = "DELETE FROM vendor_imagine_learning_language_literacy WHERE site_id = ? AND school_year_id = ? LIMIT ?";
                    $stmt = $this->db->prepare($query);
                    $limit = self::DELETE_BATCH_SIZE;
                    $stmt->bind_param('iii', $this->siteId, $this->currentSchoolYearId, $limit);
                    $result = $stmt->execute();
                    
                    if ($result) {
                        $batchSuccess = true;
                        $deletedRows = $stmt->affected_rows;
                        $stmt->close();
                    } else {
                        $dbError = $this->db->error;
                        if (strpos($dbError, 'Lock wait timeout exceeded') !== false || 
                            strpos($dbError, 'Deadlock found') !== false) 
                        {
                            $lockRetries++;
                            $delaySeconds = $this->_calculateBackoffDelaySeconds($lockRetries);
                            
                            error_log("Lock detected (delete batch {$batchCount}, attempt {$lockRetries}) for site {$this->siteId}. Waiting {$delaySeconds} seconds. Error: {$dbError}");

                            if (($totalLockWaitSeconds + $delaySeconds) >= $maxTotalLockTimeSeconds) {
                                 error_log("Next backoff delay ({$delaySeconds}s) would exceed max total lock wait time for site {$this->siteId}. Failing delete attempt.");
                                 return false;
                            }
                            sleep($delaySeconds);
                            $totalLockWaitSeconds += $delaySeconds; 
                        } else {
                            throw new Exception("Error deleting batch {$batchCount}: {$dbError}"); 
                        }
                    }
                } catch (Exception $e) {
                     error_log("Non-recoverable exception during delete batch {$batchCount} for site {$this->siteId}: " . $e->getMessage());
                     throw $e;
                }
            }
            
            if (!$batchSuccess) {
                // If loop finishes because MAX_LOCK_RETRIES reached without success
                error_log("Failed to delete batch {$batchCount} for site {$this->siteId} after {$lockRetries} attempts due to persistent locks.");
                return false;
            }
            
            $batchCount++;

            if ($batchCount >= self::MAX_DELETE_BATCHES && $deletedRows > 0) {
                error_log("Warning: Delete operation reached max batches (" . self::MAX_DELETE_BATCHES . ") for site {$this->siteId}, but data might still remain ({$deletedRows} affected in last batch).");
                return true;
            }

        } while ($deletedRows > 0);

        // Loop exited because $deletedRows is 0
        // Indicate successful completion
        return true;
    }


    /**
     * Check if all data for the site and school year has been deleted
     * @return bool True if no rows exist
     * @throws Exception On database query error
     */
    private function _checkIfDataDeleted()
    {
        $query = "SELECT 1 FROM vendor_imagine_learning_language_literacy WHERE site_id = ? AND school_year_id = ? LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bind_param('ii', $this->siteId, $this->currentSchoolYearId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
        
        return $result->num_rows == 0; 
    }

    /**
     * Calculate backoff delay in seconds (using exponential backoff with jitter)
     * @param int $retryAttempt Current retry attempt number (1-based)
     * @return int Delay in seconds
     */
    private function _calculateBackoffDelaySeconds($retryAttempt)
    {
        if ($retryAttempt <= 0) return 1;
        // Exponential backoff: 1, 2, 4, 8, 16 seconds, max 60 seconds
        $baseDelay = min(60, pow(2, $retryAttempt - 1)); 
        // Add jitter: +/- 20% of base delay, prevent backoff interference from other jobs
        $jitter = $baseDelay * (mt_rand(-200, 200) / 1000); 
        $delay = max(1, round($baseDelay + $jitter));
        return intval($delay);
    }

}

// Main execution: run the importer via the runJob function
function runJob($db, $siteId, $config)
{
    try {
        // Ensure StorageClient uses the correct project ID from config
        $projectId = $config->gcp->projectId ?? 'abre-production'; 
        $storageClient = new StorageClient(['projectId' => $projectId]);

        // Inject the StorageClient into the importer
        $importer = new ImagineLearningLanguageLiteracyImporter($db, $siteId, $config, $storageClient); 
        $importer->run();
    } catch (Throwable $e) {
        error_log(
            "Fatal error running Imagine Learning Language Literacy importer for site {$siteId}: " . 
            $e->getMessage() . "\nFile: " . $e->getFile() . "\nLine: " . $e->getLine() . "\nTrace: " . $e->getTraceAsString()
        );
        if (isset($config->env)) {
             error_log("Environment: " . $config->env);
        }
        // Optionally re-throw or handle based on runner's expectation
        // throw $e;
    }
}
 