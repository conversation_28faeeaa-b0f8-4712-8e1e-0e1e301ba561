<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Keystone Import v1.0';
  
  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("MAX_IMPORT_LIMIT", 100);

    $error = null;
    $skip = null;

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-pssa');
    $isFileLoaded = $sftp->get('Abre_Keystone_v1.0.csv', $localTempFile);

    if($isFileLoaded){
      importKeystoneData($db, $siteID, $localTempFile);
    }else{
      $skip = true;
      throw new Exception("No file found.");
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}


function importKeystoneData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE abre_keystone";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName){
  $temporaryTableColumnsSQL = "
    INSERT INTO {$temporaryTableName}
    (
      test_name,
      tested_district_id,
      tested_district_name,
      tested_school_code,
      tested_school_name,
      tested_district_instituional_code,
      drc_student_id,
      pa_secure_id,
      local_id,
      last_name,
      first_name,
      middle_initial,
      birth_date,
      grade,
      test_year,
      test_season,
      mod1_form_number,
      mod1_spanish_version,
      mod1_attempted,
      mod1_result,
      mod1_exclusion_code,
      mod1_mode,
      mod1_scale_score,
      mod1_raw_score,
      mod1_anchor1_raw_score,
      mod1_anchor1_cr_raw_score,
      mod1_anchor2_raw_score,
      mod1_anchor2_cr_raw_score,
      mod1_anchor3_raw_score,
      mod1_anchor3_cr_raw_score,
      mod1_anchor4_raw_score,
      mod1_anchor4_cr_raw_score,
      mod2_form_number,
      mod2_spanish_version,
      mod2_attempted,
      mod2_result,
      mod2_exclusion_code,
      mod2_mode,
      mod2_scale_score,
      mod2_raw_score,
      mod2_anchor1_raw_score,
      mod2_anchor1_cr_raw_score,
      mod2_anchor2_raw_score,
      mod2_anchor2_cr_raw_score,
      mod2_anchor3_raw_score,
      mod2_anchor3_cr_raw_score,
      mod2_anchor4_raw_score,
      mod2_anchor4_cr_raw_score,
      admin_scale_score,
      admin_scale_score_standard_error,
      admin_raw_score,
      admin_mc_total_raw_score,
      admin_cr_total_raw_score,
      admin_performance_level_code,
      admin_performance_level,
      combined_admin_score_flag,
      code_of_conduct,
      gender,
      ethnicity_code,
      iep,
      iep_exited,
      title_one,
      migratory_child,
      title_three,
      foreign_exchange_student,
      economically_disadvantaged,
      historically_underperforming_subgroup,
      enrolled_after_oct_school,
      enrolled_after_oct_district,
      pa_resident_after_oct,
      enrolled_after_oct_previous_school,
      enrolled_after_oct_previous_district,
      court_placed,
      home_school_student,
      residence_district,
      residence_school,
      braille_format,
      large_print_format,
      computer_assistive_technology,
      some_test_read_aloud,
      all_test_read_aloud,
      test_items_signed,
      test_items_interpreted_el,
      amplification_device,
      magnification_device,
      color_overlay,
      other_test_accomodation,
      audio,
      color_chooser,
      contrasting_text_chooser,
      reverse_contrast,
      video_sign_language,
      refreshable_braille,
      hospital_home_setting,
      one_on_one_setting,
      small_group_setting,
      other_accomodation,
      administrator_marks_test,
      administrator_scribed_OE,
      administrator_transcribed,
      interpreter_translated_signed,
      interpreter_translated_el,
      keyboard_wp_computer_used,
      brailler_note_taker_used,
      augmented_communication_device_used,
      computer_assistive_technology_used,
      translated_dictionary,
      other_tool_used,
      extended_time,
      frequent_breaks,
      changed_test_schedule,
      other_accommodations,
      optional_field1,
      optional_field2,
      optional_field3,
      optional_field4,
      federal_el_subgroup,
      federal_non_el_subgroup,
      military_family,
      homeless,
      foster,
      section504,
      combined_ethnicity,
      site_id
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, ",");
    $studentID = trim($lineArray[9]);
    if($studentID != ""){
      array_push($records, buildInsertLine($db, $siteID, $studentID, $lineArray));
    }

    if(count($records) == MAX_IMPORT_LIMIT){
      $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
      $stmt = $db->stmt_init();
      $stmt->prepare($temporaryTableInsertSQL);
      $stmt->execute();
      $stmt->close();

      $records = [];
    }
  }

  if(count($records)){
    $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }
}

function buildInsertLine($db, $siteID, $studentID, $line){

  $test_name = $line[0];
  $tested_district_id = nullCheckWithQuotes($line[1]);
  $tested_district_name = nullCheckWithQuotes($line[2]);
  $tested_school_code = nullCheckWithQuotes($line[3]);
  $tested_school_name = nullCheckWithQuotes($line[4]);
  $tested_district_instituional_code = nullCheck($line[5]);
  $drc_student_id = $line[6];
  $pa_secure_id = $line[8];
  $last_name = $db->escape_string($line[10]);
  $first_name = $db->escape_string($line[11]);
  $middle_initial = nullCheckWithQuotes($line[12]);

  //dates do not contain separators. If it is less 12 characters in length
  //add a leading 0 or it will not parse correctly.
  if(strlen($line[13]) != 8 && substr($line[13], 0, 1) != "0"){
    $birthdateRaw = str_pad($line[13], 8, "0", STR_PAD_LEFT);
  }else{
    $birthdateRaw = $line[13];
  }
  $birthDateTime = date_create_from_format("ndY", $birthdateRaw);
  if($birthDateTime !== false){
    $birthdate = "'".$birthDateTime->format("Y-m-d")."'";
  }else{
    $birthdate = 'NULL';
  }

  $grade = nullCheck($line[14]);
  $test_year = $line[15];
  $test_season = $line[16];
  $mod1_form_number = nullCheck($line[17]);
  $mod1_spanish_version = parseFlagString($line[18]);
  $mod1_attempted = parseFlagString($line[19]);
  $mod1_result = nullCheck($line[20]);
  $mod1_exclusion_code = nullCheckWithQuotes($line[21]);
  $mod1_mode = nullCheckWithQuotes($line[22]);
  $mod1_scale_score = nullCheck($line[23]);
  $mod1_raw_score = nullCheck($line[24]);
  $mod1_anchor1_raw_score = nullCheck($line[25]);
  $mod1_anchor1_cr_raw_score = nullCheck($line[26]);
  $mod1_anchor2_raw_score = nullCheck($line[27]);
  $mod1_anchor2_cr_raw_score = nullCheck($line[28]);
  $mod1_anchor3_raw_score = nullCheck($line[29]);
  $mod1_anchor3_cr_raw_score = nullCheck($line[30]);
  $mod1_anchor4_raw_score = nullCheck($line[31]);
  $mod1_anchor4_cr_raw_score = nullCheck($line[32]);
  $mod2_form_number = nullCheck($line[33]);
  $mod2_spanish_version = parseFlagString($line[34]);
  $mod2_attempted = parseFlagString($line[35]);
  $mod2_result = nullCheck($line[36]);
  $mod2_exclusion_code = nullCheckWithQuotes($line[37]);
  $mod2_mode = nullCheckWithQuotes($line[38]);
  $mod2_scale_score = nullCheck($line[39]);
  $mod2_raw_score = nullCheck($line[40]);
  $mod2_anchor1_raw_score = nullCheck($line[41]);
  $mod2_anchor1_cr_raw_score = nullCheck($line[42]);
  $mod2_anchor2_raw_score = nullCheck($line[43]);
  $mod2_anchor2_cr_raw_score = nullCheck($line[44]);
  $mod2_anchor3_raw_score = nullCheck($line[45]);
  $mod2_anchor3_cr_raw_score = nullCheck($line[46]);
  $mod2_anchor4_raw_score = nullCheck($line[47]);
  $mod2_anchor4_cr_raw_score = nullCheck($line[48]);
  $admin_scale_score = nullCheck($line[50]);
  $admin_scale_score_standard_error = nullCheck($line[51]);
  $admin_raw_score = nullCheck($line[52]);
  $admin_mc_total_raw_score = nullCheck($line[53]);
  $admin_cr_total_raw_score = nullCheck($line[54]);
  $admin_performance_level_code = nullCheck($line[55]);
  $admin_performance_level = nullCheckWithQuotes($line[56]);
  $combined_admin_score_flag = parseFlagString($line[57]);
  $code_of_conduct = parseFlagString($line[58]);
  $gender = nullCheckWithQuotes($line[100]);
  $ethnicity_code = nullCheck($line[101]);
  $iep = parseFlagString($line[102]);
  $iep_exited = parseFlagString($line[103]);
  $title_one = parseFlagString($line[104]);
  $migratory_child = parseFlagString($line[105]);
  $title_three = nullCheck($line[107]);
  $foreign_exchange_student = parseFlagString($line[108]);
  $economically_disadvantaged = parseFlagString($line[109]);
  $historically_underperforming_subgroup = parseFlagString($line[110]);
  $enrolled_after_oct_school = parseFlagString($line[111]);
  $enrolled_after_oct_district = parseFlagString($line[112]);
  $pa_resident_after_oct = parseFlagString($line[113]);
  $enrolled_after_oct_previous_school = parseFlagString($line[114]);
  $enrolled_after_oct_previous_district = parseFlagString($line[115]);
  $court_placed = parseFlagString($line[116]);
  $home_school_student = parseFlagString($line[117]);
  $residence_district = nullCheck($line[119]);
  $residence_school = nullCheck($line[120]);
  $braille_format = parseFlagString($line[121]);
  $large_print_format = parseFlagString($line[122]);
  $computer_assistive_technology = parseFlagString($line[123]);
  $some_test_read_aloud = parseFlagString($line[124]);
  $all_test_read_aloud = parseFlagString($line[125]);
  $test_items_signed = parseFlagString($line[126]);
  $test_items_interpreted_el = parseFlagString($line[127]);
  $amplification_device = parseFlagString($line[128]);
  $magnification_device = parseFlagString($line[129]);
  $color_overlay = parseFlagString($line[130]);
  $other_test_accomodation = parseFlagString($line[131]);
  $audio = parseFlagString($line[132]);
  $color_chooser = parseFlagString($line[133]);
  $contrasting_text_chooser = parseFlagString($line[134]);
  $reverse_contrast = parseFlagString($line[135]);
  $video_sign_language = parseFlagString($line[136]);
  $refreshable_braille = parseFlagString($line[137]);
  $hospital_home_setting = parseFlagString($line[138]);
  $one_on_one_setting = parseFlagString($line[139]);
  $small_group_setting = parseFlagString($line[140]);
  $other_accomodation = parseFlagString($line[141]);
  $administrator_marks_test = parseFlagString($line[142]);
  $administrator_scribed_OE = parseFlagString($line[143]);
  $administrator_transcribed = parseFlagString($line[144]);
  $interpreter_translated_signed = parseFlagString($line[145]);
  $interpreter_translated_el = parseFlagString($line[146]);
  $keyboard_wp_computer_used = parseFlagString($line[147]);
  $brailler_note_taker_used = parseFlagString($line[148]);
  $augmented_communication_device_used = parseFlagString($line[149]);
  $computer_assistive_technology_used = parseFlagString($line[150]);
  $translated_dictionary = parseFlagString($line[151]);
  $other_tool_used = parseFlagString($line[152]);
  $extended_time = parseFlagString($line[153]);
  $frequent_breaks = parseFlagString($line[154]);
  $changed_test_schedule = parseFlagString($line[155]);
  $other_accommodations = parseFlagString($line[156]);
  $optional_field1 = parseFlagString($line[157]);
  $optional_field2 = parseFlagString($line[158]);
  $optional_field3 = parseFlagString($line[159]);
  $optional_field4 = parseFlagString($line[160]);
  $federal_el_subgroup = parseFlagString($line[161]);
  $federal_non_el_subgroup = parseFlagString($line[162]);
  $military_family = parseFlagString($line[163]);
  $homeless = parseFlagString($line[164]);
  $foster = parseFlagString($line[165]);
  $section504 = parseFlagString($line[166]);
  $combined_ethnicity = parseFlagString($line[167]);

  $sql = "
    (
      '$test_name',
      $tested_district_id,
      $tested_district_name,
      $tested_school_code,
      $tested_school_name,
      $tested_district_instituional_code,
      '$drc_student_id',
      '$pa_secure_id',
      $studentID,
      '$last_name',
      '$first_name',
      $middle_initial,
      $birthdate,
      $grade,
      $test_year,
      '$test_season',
      $mod1_form_number,
      $mod1_spanish_version,
      $mod1_attempted,
      $mod1_result,
      $mod1_exclusion_code,
      $mod1_mode,
      $mod1_scale_score,
      $mod1_raw_score,
      $mod1_anchor1_raw_score,
      $mod1_anchor1_cr_raw_score,
      $mod1_anchor2_raw_score,
      $mod1_anchor2_cr_raw_score,
      $mod1_anchor3_raw_score,
      $mod1_anchor3_cr_raw_score,
      $mod1_anchor4_raw_score,
      $mod1_anchor4_cr_raw_score,
      $mod2_form_number,
      $mod2_spanish_version,
      $mod2_attempted,
      $mod2_result,
      $mod2_exclusion_code,
      $mod2_mode,
      $mod2_scale_score,
      $mod2_raw_score,
      $mod2_anchor1_raw_score,
      $mod2_anchor1_cr_raw_score,
      $mod2_anchor2_raw_score,
      $mod2_anchor2_cr_raw_score,
      $mod2_anchor3_raw_score,
      $mod2_anchor3_cr_raw_score,
      $mod2_anchor4_raw_score,
      $mod2_anchor4_cr_raw_score,
      $admin_scale_score,
      $admin_scale_score_standard_error,
      $admin_raw_score,
      $admin_mc_total_raw_score,
      $admin_cr_total_raw_score,
      $admin_performance_level_code,
      $admin_performance_level,
      $combined_admin_score_flag,
      $code_of_conduct,
      $gender,
      $ethnicity_code,
      $iep,
      $iep_exited,
      $title_one,
      $migratory_child,
      $title_three,
      $foreign_exchange_student,
      $economically_disadvantaged,
      $historically_underperforming_subgroup,
      $enrolled_after_oct_school,
      $enrolled_after_oct_district,
      $pa_resident_after_oct,
      $enrolled_after_oct_previous_school,
      $enrolled_after_oct_previous_district,
      $court_placed,
      $home_school_student,
      $residence_district,
      $residence_school,
      $braille_format,
      $large_print_format,
      $computer_assistive_technology,
      $some_test_read_aloud,
      $all_test_read_aloud,
      $test_items_signed,
      $test_items_interpreted_el,
      $amplification_device,
      $magnification_device,
      $color_overlay,
      $other_test_accomodation,
      $audio,
      $color_chooser,
      $contrasting_text_chooser,
      $reverse_contrast,
      $video_sign_language,
      $refreshable_braille,
      $hospital_home_setting,
      $one_on_one_setting,
      $small_group_setting,
      $other_accomodation,
      $administrator_marks_test,
      $administrator_scribed_OE,
      $administrator_transcribed,
      $interpreter_translated_signed,
      $interpreter_translated_el,
      $keyboard_wp_computer_used,
      $brailler_note_taker_used,
      $augmented_communication_device_used,
      $computer_assistive_technology_used,
      $translated_dictionary,
      $other_tool_used,
      $extended_time,
      $frequent_breaks,
      $changed_test_schedule,
      $other_accommodations,
      $optional_field1,
      $optional_field2,
      $optional_field3,
      $optional_field4,
      $federal_el_subgroup,
      $federal_non_el_subgroup,
      $military_family,
      $homeless,
      $foster,
      $section504,
      $combined_ethnicity,
      $siteID
    )";
  return $sql;
}

function parseFlagString($str){
  if($str == "Y"){
    return 1;
  }
  if($str == "N"){
    return 0;
  }
  return 'NULL';
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $dropIDColumn = "ALTER TABLE {$temporaryTableName} DROP id";
  $dropIDStmt = $db->stmt_init();
  $dropIDStmt->prepare($dropIDColumn);
  $dropIDStmt->execute();
  $dropIDStmt->close();

  $insertSQL = "INSERT INTO abre_keystone
                  SELECT 0, t.* FROM {$temporaryTableName} t
                ON DUPLICATE KEY UPDATE
                tested_district_id = t.tested_district_id,
                tested_district_name = t.tested_district_name,
                tested_school_code = t.tested_school_code,
                tested_school_name = t.tested_school_name,
                tested_district_instituional_code = t.tested_district_instituional_code,
                drc_student_id = t.drc_student_id,
                pa_secure_id = t.pa_secure_id,
                last_name = t.last_name,
                first_name = t.first_name,
                middle_initial = t.middle_initial,
                birth_date = t.birth_date,
                grade = t.grade,
                mod1_form_number = t.mod1_form_number,
                mod1_spanish_version = t.mod1_spanish_version,
                mod1_attempted = t.mod1_attempted,
                mod1_result = t.mod1_result,
                mod1_exclusion_code = t.mod1_exclusion_code,
                mod1_mode = t.mod1_mode,
                mod1_scale_score = t.mod1_scale_score,
                mod1_raw_score = t.mod1_raw_score,
                mod1_anchor1_raw_score = t.mod1_anchor1_raw_score,
                mod1_anchor1_cr_raw_score = t.mod1_anchor1_cr_raw_score,
                mod1_anchor2_raw_score = t.mod1_anchor2_raw_score,
                mod1_anchor2_cr_raw_score = t.mod1_anchor2_cr_raw_score,
                mod1_anchor3_raw_score = t.mod1_anchor3_raw_score,
                mod1_anchor3_cr_raw_score = t.mod1_anchor3_cr_raw_score,
                mod1_anchor4_raw_score = t.mod1_anchor4_raw_score,
                mod1_anchor4_cr_raw_score = t.mod1_anchor4_cr_raw_score,
                mod2_form_number = t.mod2_form_number,
                mod2_spanish_version = t.mod2_spanish_version,
                mod2_attempted = t.mod2_attempted,
                mod2_result = t.mod2_result,
                mod2_exclusion_code = t.mod2_exclusion_code,
                mod2_mode = t.mod2_mode,
                mod2_scale_score = t.mod2_scale_score,
                mod2_raw_score = t.mod2_raw_score,
                mod2_anchor1_raw_score = t.mod2_anchor1_raw_score,
                mod2_anchor1_cr_raw_score = t.mod2_anchor1_cr_raw_score,
                mod2_anchor2_raw_score = t.mod2_anchor2_raw_score,
                mod2_anchor2_cr_raw_score = t.mod2_anchor2_cr_raw_score,
                mod2_anchor3_raw_score = t.mod2_anchor3_raw_score,
                mod2_anchor3_cr_raw_score = t.mod2_anchor3_cr_raw_score,
                mod2_anchor4_raw_score = t.mod2_anchor4_raw_score,
                mod2_anchor4_cr_raw_score = t.mod2_anchor4_cr_raw_score,
                admin_scale_score = t.admin_scale_score,
                admin_scale_score_standard_error = t.admin_scale_score_standard_error,
                admin_raw_score = t.admin_raw_score,
                admin_mc_total_raw_score = t.admin_mc_total_raw_score,
                admin_cr_total_raw_score = t.admin_cr_total_raw_score,
                admin_performance_level_code = t.admin_performance_level_code,
                admin_performance_level = t.admin_performance_level,
                combined_admin_score_flag = t.combined_admin_score_flag,
                code_of_conduct = t.code_of_conduct,
                gender = t.gender,
                ethnicity_code = t.ethnicity_code,
                iep = t.iep,
                iep_exited = t.iep_exited,
                title_one = t.title_one,
                migratory_child = t.migratory_child,
                title_three = t.title_three,
                foreign_exchange_student = t.foreign_exchange_student,
                economically_disadvantaged = t.economically_disadvantaged,
                historically_underperforming_subgroup = t.historically_underperforming_subgroup,
                enrolled_after_oct_school = t.enrolled_after_oct_school,
                enrolled_after_oct_district = t.enrolled_after_oct_district,
                pa_resident_after_oct = t.pa_resident_after_oct,
                enrolled_after_oct_previous_school = t.enrolled_after_oct_previous_school,
                enrolled_after_oct_previous_district = t.enrolled_after_oct_previous_district,
                court_placed = t.court_placed,
                home_school_student = t.home_school_student,
                residence_district = t.residence_district,
                residence_school = t.residence_school,
                braille_format = t.braille_format,
                large_print_format = t.large_print_format,
                computer_assistive_technology = t.computer_assistive_technology,
                some_test_read_aloud = t.some_test_read_aloud,
                all_test_read_aloud = t.all_test_read_aloud,
                test_items_signed = t.test_items_signed,
                test_items_interpreted_el = t.test_items_interpreted_el,
                amplification_device = t.amplification_device,
                magnification_device = t.magnification_device,
                color_overlay = t.color_overlay,
                other_test_accomodation = t.other_test_accomodation,
                audio = t.audio,
                color_chooser = t.color_chooser,
                contrasting_text_chooser = t.contrasting_text_chooser,
                reverse_contrast = t.reverse_contrast,
                video_sign_language = t.video_sign_language,
                refreshable_braille = t.refreshable_braille,
                hospital_home_setting = t.hospital_home_setting,
                one_on_one_setting = t.one_on_one_setting,
                small_group_setting = t.small_group_setting,
                other_accomodation = t.other_accomodation,
                administrator_marks_test = t.administrator_marks_test,
                administrator_scribed_OE = t.administrator_scribed_OE,
                administrator_transcribed = t.administrator_transcribed,
                interpreter_translated_signed = t.interpreter_translated_signed,
                interpreter_translated_el = t.interpreter_translated_el,
                keyboard_wp_computer_used = t.keyboard_wp_computer_used,
                brailler_note_taker_used = t.brailler_note_taker_used,
                augmented_communication_device_used = t.augmented_communication_device_used,
                computer_assistive_technology_used = t.computer_assistive_technology_used,
                translated_dictionary = t.translated_dictionary,
                other_tool_used = t.other_tool_used,
                extended_time = t.extended_time,
                frequent_breaks = t.frequent_breaks,
                changed_test_schedule = t.changed_test_schedule,
                other_accommodations = t.other_accommodations,
                optional_field1 = t.optional_field1,
                optional_field2 = t.optional_field2,
                optional_field3 = t.optional_field3,
                optional_field4 = t.optional_field4,
                federal_el_subgroup = t.federal_el_subgroup,
                federal_non_el_subgroup = t.federal_non_el_subgroup,
                military_family = t.military_family,
                homeless = t.homeless,
                foster = t.foster,
                section504 = t.section504,
                combined_ethnicity = t.combined_ethnicity";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>