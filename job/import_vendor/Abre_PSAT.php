<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'PSAT Import v1.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("MAX_IMPORT_LIMIT", 100);

    $error = null;
    $skip = null;

    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-psat');
    $isFileLoaded = $sftp->get('Abre_PSAT_v1.0.csv', $localTempFile);

    if($isFileLoaded){
      importPSATData($db, $siteID, $localTempFile);
    }else{
      $skip = true;
      throw new Exception("No file found.");
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];

  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
  
}

function importPSATData($db, $siteID, $tempFile){
    try{
      $temporaryTableName = createTemporaryTable($db);
      loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName);
      insertTemporaryTable($siteID, $db, $temporaryTableName);
    }finally{
      if(isset($temporaryTableName)){
        dropTemporaryTable($db, $temporaryTableName);
      }
    }
  }
  
  function createTemporaryTable($db){
    $tableCreated = false;
    $tries = 0;
    while(!$tableCreated && $tries < 3){
      $tries++;
      $temporaryTableName = "temp" . uniqid();
      $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                  LIKE abre_psat";
      $stmt = $db->stmt_init();
      $stmt->prepare($createTemporaryTableSQL);
      $tableCreated = $stmt->execute();
      $stmt->close();
    }
    return $temporaryTableName;
  }

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName){
  $temporaryTableColumnsSQL = "
    INSERT INTO {$temporaryTableName}
    (
      ai_name,
      cohort_year, 
      district_name, 
      last_name, 
      first_name, 
      middle_initial, 
      gender,
      birth_date,
      collegeboard_id, 
      student_id, 
      latest_psat_date, 
      latest_psat_grade,
      latest_psat_total, 
      latest_psat_ebrw,
      latest_psat_math_section,
      latest_psat_reading, 
      latest_psat_writ_lang,
      latest_psat_math_test,
      latest_psat_sci_cross, 
      latest_psat_hist_socst_cross,
      latest_psat_words_context, 
      latest_psat_comm_evidence,
      latest_psat_expr_ideas,
      latest_psat_eng_convent, 
      latest_psat_heart_algebra, 
      latest_psat_adv_math,
      latest_psat_probslv_data, 
      percentile_natrep_psat_total, 
      percentile_natrep_ebrw,
      percentile_natrep_math_section,
      percentile_natrep_reading, 
      percentile_natrep_writ_lang,
      percentile_natrep_math_test,
      percentile_natrep_sci_cross, 
      percentile_natrep_hist_socst_cross,
      percentile_natrep_words_context, 
      percentile_natrep_comm_evidence,
      percentile_natrep_expr_ideas,
      percentile_natrep_eng_convent, 
      percentile_natrep_heart_algebra, 
      percentile_natrep_adv_math,
      percentile_natrep_probslv_data, 
      percentile_natuser_psat_total, 
      percentile_natuser_ebrw,
      percentile_natuser_math_section,
      percentile_natuser_reading, 
      percentile_natuser_writ_lang,
      percentile_natuser_math_test,
      percentile_natuser_sci_cross, 
      percentile_natuser_hist_socst_cross,
      percentile_natuser_words_context, 
      percentile_natuser_comm_evidence,
      percentile_natuser_expr_ideas,
      percentile_natuser_eng_convent, 
      percentile_natuser_heart_algebra, 
      percentile_natuser_adv_math,
      percentile_natuser_probslv_data,
      site_id
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, ",");
    $studentID = trim($lineArray[25]);
    if($studentID != ""){
      array_push($records, buildInsertLine($db, $siteID, $studentID, $lineArray));
    }

    if(count($records) == MAX_IMPORT_LIMIT){
      $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
      $stmt = $db->stmt_init();
      $stmt->prepare($temporaryTableInsertSQL);
      $stmt->execute();
      $stmt->close();

      $records = [];
    }
  }

  if(count($records)){
    $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }
}

function buildInsertLine($db, $siteID, $studentID, $line){

    $ai_name = $line[1];
    $cohort_year = $line[3];
    $district_name = $db->escape_string($line[4]); 
    $last_name = $db->escape_string($line[5]); 
    $first_name = $db->escape_string($line[6]);
    $middle_initial = nullCheck($line[7]);
    $gender = $line[8];
    $birth_date = $line[22]; 

    $birth_date = date_create_from_format("Y-m-d", $birth_date);
    if($birth_date !== false){
      $birth_date = "'".$birth_date->format("Y-m-d")."'";
    }else{
      $birth_date = 'NULL';
    }

    $collegeboard_id = $line[24];
    $student_id = nullCheck($line[25]);
    $latest_psat_date = $line[45];

    $latest_psat_date = date_create_from_format("Y-m-d", $latest_psat_date);
    $latest_psat_date = "'".$latest_psat_date->format("Y-m-d")."'";
    

    $latest_psat_grade = $line[46];
    $latest_psat_total = $line[47];
    $latest_psat_ebrw = $line[48];
    $latest_psat_math_section = $line[49];
    $latest_psat_reading = $line[50];
    $latest_psat_writ_lang = $line[51];
    $latest_psat_math_test = $line[52];
    $latest_psat_sci_cross = $line[53]; 
    $latest_psat_hist_socst_cross =$line[54];
    $latest_psat_words_context = $line[55];
    $latest_psat_comm_evidence = $line[56];
    $latest_psat_expr_ideas = $line[57];
    $latest_psat_eng_convent = $line[58];
    $latest_psat_heart_algebra = $line[59];
    $latest_psat_adv_math = $line[60];
    $latest_psat_probslv_data = $line[61];
    $percentile_natrep_psat_total = $line[72];
    $percentile_natrep_ebrw = $line[73];
    $percentile_natrep_math_section = $line[74];
    $percentile_natrep_reading = $line[75];
    $percentile_natrep_writ_lang = $line[76];
    $percentile_natrep_math_test = $line[77];
    $percentile_natrep_sci_cross = $line[78];
    $percentile_natrep_hist_socst_cross = $line[79];
    $percentile_natrep_words_context = $line[80];
    $percentile_natrep_comm_evidence = $line[81];
    $percentile_natrep_expr_ideas = $line[82];
    $percentile_natrep_eng_convent = $line[83];
    $percentile_natrep_heart_algebra = $line[84]; 
    $percentile_natrep_adv_math = $line[85];
    $percentile_natrep_probslv_data = $line[86];
    $percentile_natuser_psat_total = $line[89];
    $percentile_natuser_ebrw = $line[90];
    $percentile_natuser_math_section = $line[91];
    $percentile_natuser_reading = $line[92];
    $percentile_natuser_writ_lang = $line[93];
    $percentile_natuser_math_test = $line[94];
    $percentile_natuser_sci_cross = $line[95];
    $percentile_natuser_hist_socst_cross = $line[96];
    $percentile_natuser_words_context = $line[97];
    $percentile_natuser_comm_evidence = $line[98];
    $percentile_natuser_expr_ideas = $line[99];
    $percentile_natuser_eng_convent = $line[100];
    $percentile_natuser_heart_algebra = $line[101];
    $percentile_natuser_adv_math = $line[102];
    $percentile_natuser_probslv_data = $line[103];

  $sql = "
    (
      '$ai_name',
      '$cohort_year',
      '$district_name',
      '$last_name',
      '$first_name',
      '$middle_initial',
      '$gender',
      $birth_date, 
      $collegeboard_id,
      $student_id,
      $latest_psat_date, 
      $latest_psat_grade,
      $latest_psat_total,
      $latest_psat_ebrw,
      $latest_psat_math_section, 
      $latest_psat_reading, 
      $latest_psat_writ_lang,
      $latest_psat_math_test, 
      $latest_psat_sci_cross, 
      $latest_psat_hist_socst_cross, 
      $latest_psat_words_context, 
      $latest_psat_comm_evidence, 
      $latest_psat_expr_ideas,
      $latest_psat_eng_convent, 
      $latest_psat_heart_algebra, 
      $latest_psat_adv_math,
      $latest_psat_probslv_data, 
      $percentile_natrep_psat_total, 
      $percentile_natrep_ebrw,
      $percentile_natrep_math_section, 
      $percentile_natrep_reading,
      $percentile_natrep_writ_lang,
      $percentile_natrep_math_test,
      $percentile_natrep_sci_cross,
      $percentile_natrep_hist_socst_cross,
      $percentile_natrep_words_context,
      $percentile_natrep_comm_evidence,
      $percentile_natrep_expr_ideas,
      $percentile_natrep_eng_convent,
      $percentile_natrep_heart_algebra,
      $percentile_natrep_adv_math,
      $percentile_natrep_probslv_data,
      $percentile_natuser_psat_total,
      $percentile_natuser_ebrw,
      $percentile_natuser_math_section,
      $percentile_natuser_reading,
      $percentile_natuser_writ_lang,
      $percentile_natuser_math_test,
      $percentile_natuser_sci_cross,
      $percentile_natuser_hist_socst_cross,
      $percentile_natuser_words_context,
      $percentile_natuser_comm_evidence,
      $percentile_natuser_expr_ideas,
      $percentile_natuser_eng_convent,
      $percentile_natuser_heart_algebra,
      $percentile_natuser_adv_math,
      $percentile_natuser_probslv_data,
      $siteID
    )";
  return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $dropIDColumn = "ALTER TABLE {$temporaryTableName} DROP id";
  $dropIDStmt = $db->stmt_init();
  $dropIDStmt->prepare($dropIDColumn);
  $dropIDStmt->execute();
  $dropIDStmt->close();

  $insertSQL = "INSERT INTO abre_psat
                  SELECT 0, t.* FROM {$temporaryTableName} t
                ON DUPLICATE KEY UPDATE
                  ai_name = t.ai_name,
                  cohort_year = t.cohort_year,
                  district_name = t.district_name, 
                  last_name = t.last_name, 
                  first_name = t.first_name, 
                  middle_initial = t.middle_initial, 
                  gender = t.gender, 
                  birth_date = t.birth_date, 
                  collegeboard_id = t.collegeboard_id, 
                  student_id = t.student_id, 
                  latest_psat_date = t.latest_psat_date, 
                  latest_psat_grade = t.latest_psat_grade, 
                  latest_psat_total = t.latest_psat_total, 
                  latest_psat_ebrw = t.latest_psat_ebrw, 
                  latest_psat_math_section = t.latest_psat_math_section, 
                  latest_psat_reading = t.latest_psat_reading, 
                  latest_psat_writ_lang = t.latest_psat_writ_lang, 
                  latest_psat_math_test = t.latest_psat_math_test, 
                  latest_psat_sci_cross = t.latest_psat_sci_cross, 
                  latest_psat_hist_socst_cross = t.latest_psat_hist_socst_cross, 
                  latest_psat_words_context = t.latest_psat_words_context, 
                  latest_psat_comm_evidence = t.latest_psat_comm_evidence,  
                  latest_psat_expr_ideas = t.latest_psat_expr_ideas, 
                  latest_psat_eng_convent = t.latest_psat_eng_convent, 
                  latest_psat_heart_algebra = t.latest_psat_heart_algebra, 
                  latest_psat_adv_math = t.latest_psat_adv_math, 
                  latest_psat_probslv_data = t.latest_psat_probslv_data, 
                  percentile_natrep_psat_total = t.percentile_natrep_psat_total, 
                  percentile_natrep_ebrw = t.percentile_natrep_ebrw, 
                  percentile_natrep_math_section = t.percentile_natrep_math_section, 
                  percentile_natrep_reading = t.percentile_natrep_reading, 
                  percentile_natrep_writ_lang = t.percentile_natrep_writ_lang,
                  percentile_natrep_math_test = t.percentile_natrep_math_test, 
                  percentile_natrep_sci_cross = t.percentile_natrep_sci_cross, 
                  percentile_natrep_hist_socst_cross = t.percentile_natrep_hist_socst_cross,
                  percentile_natrep_words_context = t.percentile_natrep_words_context, 
                  percentile_natrep_comm_evidence = t.percentile_natrep_comm_evidence,
                  percentile_natrep_expr_ideas = t.percentile_natrep_expr_ideas,
                  percentile_natrep_eng_convent = t.percentile_natrep_eng_convent,
                  percentile_natrep_heart_algebra = t.percentile_natrep_heart_algebra, 
                  percentile_natrep_adv_math = t.percentile_natrep_adv_math, 
                  percentile_natrep_probslv_data = t.percentile_natrep_probslv_data, 
                  percentile_natuser_psat_total = t.percentile_natuser_psat_total, 
                  percentile_natuser_ebrw = t.percentile_natuser_ebrw,
                  percentile_natuser_math_section = t.percentile_natuser_math_section,
                  percentile_natuser_reading = t.percentile_natuser_reading,
                  percentile_natuser_writ_lang = t.percentile_natuser_writ_lang,
                  percentile_natuser_math_test = t.percentile_natuser_math_test,
                  percentile_natuser_sci_cross = t.percentile_natuser_sci_cross,
                  percentile_natuser_hist_socst_cross = t.percentile_natuser_hist_socst_cross,
                  percentile_natuser_words_context = t.percentile_natuser_words_context,
                  percentile_natuser_comm_evidence = t.percentile_natuser_comm_evidence,
                  percentile_natuser_expr_ideas = t.percentile_natuser_expr_ideas,
                  percentile_natuser_eng_convent = t.percentile_natuser_eng_convent,
                  percentile_natuser_heart_algebra = t.percentile_natuser_heart_algebra,
                  percentile_natuser_adv_math = t.percentile_natuser_adv_math,
                  percentile_natuser_probslv_data = t.percentile_natuser_probslv_data";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>
