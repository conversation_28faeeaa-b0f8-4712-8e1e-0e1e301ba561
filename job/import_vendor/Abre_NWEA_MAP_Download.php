<?php
require(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config)
{

    $cronName = 'NWEA Map Download';

    define("MAX_IMPORT_LIMIT", 500);

    try {

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        $url = 'https://api.mapnwea.org/services/reporting/dex';

        if (!$siteID) {
            throw new InvalidArgumentException("No siteID provided");
        }

        // All of this work is done because <PERSON><PERSON> had permissions issues concerning
        // opening the temp stream and writing the temp file. We ultimately are
        // creating a temp dir in /tmp for this work so that we own the file
        $tempDir = tempnam(sys_get_temp_dir(), 'map-zip-');
        unlink($tempDir); // delete the file created for us so we can make the directory
        mkdir($tempDir);

        $zipFile = "$tempDir/map.zip";
        $csvFile = "$tempDir/ComboStudentAssessment.csv";

        $currentTerm = getFile($config->nwea, $url, $tempDir, $csvFile, $zipFile, $siteID);
        if ($currentTerm !== false) { // if no data
            $deleteOldMapDataSql = "DELETE FROM Abre_MAPData
                              WHERE siteID = $siteID AND TermName = '$currentTerm';";
            $db->query($deleteOldMapDataSql);

            $insertedRows = importMAPData($db, $siteID, $csvFile);
        } else {
            $noDataLoaded = true;
        }
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    // gather results
    $details = [];

    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    if (isset($noDataLoaded) && !is_null($noDataLoaded)) {
        $details["noDataLoaded"] = true;
    }
    if (isset($insertedRows)) {
        $details["rows"] = $insertedRows;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

// FUNCTIONS
/**
 * returns false if no data, current term otherwise
 */
function getFile($nweaConfig, $url, $tempDir, $csvFile, $zipFile, $siteID)
{
    $auth = base64_encode("$nweaConfig->userName:$nweaConfig->password");
    $authHeader = "Authorization: Basic $auth";

    $dest = fopen($zipFile, 'w');

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HTTPHEADER, array($authHeader));
    curl_setopt($curl, CURLOPT_FILE, $dest);
    $data = curl_exec($curl);

    $responseCode = curl_getinfo($curl, CURLINFO_RESPONSE_CODE);
    if ($responseCode >= 300) {
        throw new Exception("Error getting zip file. $responseCode: $data");
    }
    curl_close($curl);

    $zip = new ZipArchive;
    if ($zip->open($zipFile) === TRUE) {
        $zip->extractTo($tempDir);
        $zip->close();
    } else {
        throw new Exception("couldn't find zip file");
    }

    //Move Zip File to Google Cloud Storage
    $currentDate = date("Ymd");
    $fileContent = file_get_contents($csvFile);
    $file = "nweamap_ComboStudentAssessment.csv";
    $storage = new StorageClient([
        'projectId' => "abre-production"
    ]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($fileContent, [
        'name' => "$currentDate/site-id/$siteID/$file"
    ]);
    $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
    $folderName = pathinfo($file, PATHINFO_FILENAME);
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($fileContent, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);
    //End Move Zip File to Google Cloud Storage

    $handle = fopen($csvFile, "r");
    if ($handle !== false) {
        if (fgetcsv($handle) === false) { // header line
            throw new Exception("file is empty");
        }

        if (($line = fgetcsv($handle)) === false) { // read first data line
            return false;
        } else {
            return $line[0];
        }
    } else {
        throw new Exception("Cannot read CSV file");
    }
}

function importMAPData($db, $siteID, $tempFile)
{
    try {
        $temporaryTableName = createTemporaryTable($db);
        loadDataIntoTemporaryTable($tempFile, $db, $temporaryTableName);
        $insertedRows = insertTemporaryTable($siteID, $db, $temporaryTableName);

        return $insertedRows;
    } finally {
        if (isset($temporaryTableName)) {
            dropTemporaryTable($db, $temporaryTableName);
        }
    }
}

function createTemporaryTable($db)
{
    $tableCreated = false;
    $tries = 0;
    while (!$tableCreated && $tries < 3) {
        $tries++;
        $temporaryTableName = "temp" . uniqid();
        $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE Abre_MAPData";
        $stmt = $db->stmt_init();
        $stmt->prepare($createTemporaryTableSQL);
        $tableCreated = $stmt->execute();
        $stmt->close();
    }

    $deleteUnwantedColumnsSql = "ALTER TABLE {$temporaryTableName} DROP ID, DROP siteID, DROP last_modified_cdc";
    $deleteUnwantedColumnStmt = $db->stmt_init();
    $deleteUnwantedColumnStmt->prepare($deleteUnwantedColumnsSql);
    $deleteUnwantedColumnStmt->execute();
    $deleteUnwantedColumnStmt->close();

    return $temporaryTableName;
}

function loadDataIntoTemporaryTable($fileName, $db, $temporaryTableName)
{
    $temporaryTableInsertSQL = "
    INSERT INTO {$temporaryTableName}
    (
      TermName,
      DistrictName,
      SchoolName,
      StudentLastName,
      StudentFirstName,
      StudentMI,
      StudentID,
      StudentDateOfBirth,
      StudentEthnicGroup,
      StudentGender,
      Grade,
      MeasurementScale,
      Discipline,
      NormsReferenceData,
      WISelectedAYFall,
      WISelectedAYWinter,
      WISelectedAYSpring,
      WIPreviousAYFall,
      WIPreviousAYWinter,
      WIPreviousAYSpring,
      TestType,
      TestName,
      TestStartDate,
      TestDurationMinutes,
      TestRITScore,
      TestStandardError,
      TestPercentile,
      FallToFallProjectedGrowth,
      FallToFallObservedGrowth,
      FallToFallObservedGrowthSE,
      FallToFallMetProjectedGrowth,
      FallToFallConditionalGrowthIndex,
      FallToFallConditionalGrowthPercentile,
      FallToWinterProjectedGrowth,
      FallToWinterObservedGrowth,
      FallToWinterObservedGrowthSE,
      FallToWinterMetProjectedGrowth,
      FallToWinterConditionalGrowthIndex,
      FallToWinterConditionalGrowthPercentile,
      FallToSpringProjectedGrowth,
      FallToSpringObservedGrowth,
      FallToSpringObservedGrowthSE,
      FallToSpringMetProjectedGrowth,
      FallToSpringConditionalGrowthIndex,
      FallToSpringConditionalGrowthPercentile,
      WinterToWinterProjectedGrowth,
      WinterToWinterObservedGrowth,
      WinterToWinterObservedGrowthSE,
      WinterToWinterMetProjectedGrowth,
      WinterToWinterConditionalGrowthIndex,
      WinterToWinterConditionalGrowthPercentile,
      WinterToSpringProjectedGrowth,
      WinterToSpringObservedGrowth,
      WinterToSpringObservedGrowthSE,
      WinterToSpringMetProjectedGrowth,
      WinterToSpringConditionalGrowthIndex,
      WinterToSpringConditionalGrowthPercentile,
      SpringToSpringProjectedGrowth,
      SpringToSpringObservedGrowth,
      SpringToSpringObservedGrowthSE,
      SpringToSpringMetProjectedGrowth,
      SpringToSpringConditionalGrowthIndex,
      SpringToSpringConditionalGrowthPercentile,
      RITtoReadingScore,
      RITtoReadingMin,
      RITtoReadingMax,
      Goal1Name,
      Goal1RitScore,
      Goal1StdErr,
      Goal1Range,
      Goal1Adjective,
      Goal2Name,
      Goal2RitScore,
      Goal2StdErr,
      Goal2Range,
      Goal2Adjective,
      Goal3Name,
      Goal3RitScore,
      Goal3StdErr,
      Goal3Range,
      Goal3Adjective,
      Goal4Name,
      Goal4RitScore,
      Goal4StdErr,
      Goal4Range,
      Goal4Adjective,
      Goal5Name,
      Goal5RitScore,
      Goal5StdErr,
      Goal5Range,
      Goal5Adjective,
      Goal6Name,
      Goal6RitScore,
      Goal6StdErr,
      Goal6Range,
      Goal6Adjective,
      Goal7Name,
      Goal7RitScore,
      Goal7StdErr,
      Goal7Range,
      Goal7Adjective,
      Goal8Name,
      Goal8RitScore,
      Goal8StdErr,
      Goal8Range,
      Goal8Adjective,
      TestStartTime,
      PercentCorrect,
      ProjectedProficiencyStudy1,
      ProjectedProficiencyLevel1,
      ProjectedProficiencyStudy2,
      ProjectedProficiencyLevel2,
      ProjectedProficiencyStudy3,
      ProjectedProficiencyLevel3,
      AccommodationCategory,
      Accommodations,
      TypicalFallToFallGrowth,
      TypicalFallToWinterGrowth,
      TypicalFallToSpringGrowth,
      TypicalWinterToWinterGrowth,
      TypicalWinterToSpringGrowth,
      TypicalSpringToSpringGrowth,
      ProjectedProficiencyStudy4,
      ProjectedProficiencyLevel4,
      ProjectedProficiencyStudy5,
      ProjectedProficiencyLevel5,
      ProjectedProficiencyStudy6,
      ProjectedProficiencyLevel6,
      ProjectedProficiencyStudy7,
      ProjectedProficiencyLevel7,
      ProjectedProficiencyStudy8,
      ProjectedProficiencyLevel8,
      ProjectedProficiencyStudy9,
      ProjectedProficiencyLevel9,
      ProjectedProficiencyStudy10,
      ProjectedProficiencyLevel10
    )
    VALUES";

    $handle = fopen($fileName, "r");
    if ($handle === false) {
        throw new Exception("Cannot read CSV file");
    }
    if (fgets($handle) === false) { // header line
        throw new Exception("file is empty");
    }

    $records = [];
    while (($line = fgets($handle)) !== false) {
        $lineArray = str_getcsv($line);

        $records[] = buildInsertLine($db, $lineArray);
        if (count($records) == MAX_IMPORT_LIMIT) {
            insertRows($db, $temporaryTableInsertSQL, $records);
            $records = [];
        }
    }

    if (count($records)) {
        insertRows($db, $temporaryTableInsertSQL, $records);
    }
}

function buildInsertLine($db, $line)
{
    $termName = $db->escape_string($line[0]);
    $districtName = $db->escape_string($line[1]);
    $schoolName = $db->escape_string($line[3]);
    $studentLastName = $db->escape_string($line[5]);
    $studentFirstName = $db->escape_string($line[6]);
    $studentMI = $db->escape_string($line[7]);
    $studentID = $db->escape_string($line[8]);
    $studentDateOfBirth = $db->escape_string($line[10]);
    $studentEthnicGroup = $db->escape_string($line[11]);
    $studentGender = $db->escape_string($line[13]);
    $grade = $db->escape_string($line[14]);
    $measurementScale = $db->escape_string($line[17]);
    $discipline = $db->escape_string($line[16]);
    $normsReferenceData = $db->escape_string($line[18]);
    $WISelectedAYFall = $db->escape_string($line[19]);
    $WISelectedAYWinter = $db->escape_string($line[20]);
    $WISelectedAYSpring = $db->escape_string($line[21]);
    $WIPreviousAYFall = $db->escape_string($line[22]);
    $WIPreviousAYWinter = $db->escape_string($line[23]);
    $WIPreviousAYSpring = $db->escape_string($line[24]);
    $testType = $db->escape_string($line[25]);
    $testName = $db->escape_string($line[26]);
    $testStartDate = $db->escape_string($line[27]);
    $testDurationMinutes = $db->escape_string($line[29]);
    $testRITScore = $db->escape_string($line[30]);
    $testStandardError = $db->escape_string($line[31]);
    $testPercentile = $db->escape_string($line[32]);
    $fallToFallProjectedGrowth = $db->escape_string($line[36]);
    $fallToFallObservedGrowth = $db->escape_string($line[37]);
    $fallToFallObservedGrowthSE = $db->escape_string($line[38]);
    $fallToFallMetProjectedGrowth = $db->escape_string($line[39]);
    $fallToFallConditionalGrowthIndex = $db->escape_string($line[40]);
    $fallToFallConditionalGrowthPercentile = $db->escape_string($line[41]);
    $fallToWinterProjectedGrowth = $db->escape_string($line[43]);
    $fallToWinterObservedGrowth = $db->escape_string($line[44]);
    $fallToWinterObservedGrowthSE = $db->escape_string($line[45]);
    $fallToWinterMetProjectedGrowth = $db->escape_string($line[46]);
    $fallToWinterConditionalGrowthIndex = $db->escape_string($line[47]);
    $fallToWinterConditionalGrowthPercentile = $db->escape_string($line[48]);
    $fallToSpringProjectedGrowth = $db->escape_string($line[50]);
    $fallToSpringObservedGrowth = $db->escape_string($line[51]);
    $fallToSpringObservedGrowthSE = $db->escape_string($line[52]);
    $fallToSpringMetProjectedGrowth = $db->escape_string($line[53]);
    $fallToSpringConditionalGrowthIndex = $db->escape_string($line[54]);
    $fallToSpringConditionalGrowthPercentile = $db->escape_string($line[55]);
    $winterToWinterProjectedGrowth = $db->escape_string($line[57]);
    $winterToWinterObservedGrowth = $db->escape_string($line[58]);
    $winterToWinterObservedGrowthSE = $db->escape_string($line[59]);
    $winterToWinterMetProjectedGrowth = $db->escape_string($line[60]);
    $winterToWinterConditionalGrowthIndex = $db->escape_string($line[61]);
    $winterToWinterConditionalGrowthPercentile = $db->escape_string($line[62]);
    $winterToSpringProjectedGrowth = $db->escape_string($line[64]);
    $winterToSpringObservedGrowth = $db->escape_string($line[65]);
    $winterToSpringObservedGrowthSE = $db->escape_string($line[66]);
    $winterToSpringMetProjectedGrowth = $db->escape_string($line[67]);
    $winterToSpringConditionalGrowthIndex = $db->escape_string($line[68]);
    $winterToSpringConditionalGrowthPercentile = $db->escape_string($line[69]);
    $springToSpringProjectedGrowth = $db->escape_string($line[71]);
    $springToSpringObservedGrowth = $db->escape_string($line[72]);
    $springToSpringObservedGrowthSE = $db->escape_string($line[73]);
    $springToSpringMetProjectedGrowth = $db->escape_string($line[74]);
    $springToSpringConditionalGrowthIndex = $db->escape_string($line[75]);
    $springToSpringConditionalGrowthPercentile = $db->escape_string($line[76]);
    $RITtoReadingScore = $db->escape_string($line[78]);
    $RITtoReadingMin = $db->escape_string($line[79]);
    $RITtoReadingMax = $db->escape_string($line[80]);
    $goal1Name = $db->escape_string($line[84]);
    $goal1RitScore = $db->escape_string($line[85]);
    $goal1StdErr = $db->escape_string($line[86]);
    $goal1Range = $db->escape_string($line[87]);
    $goal1Adjective = $db->escape_string($line[88]);
    $goal2Name = $db->escape_string($line[89]);
    $goal2RitScore = $db->escape_string($line[90]);
    $goal2StdErr = $db->escape_string($line[91]);
    $goal2Range = $db->escape_string($line[92]);
    $goal2Adjective = $db->escape_string($line[93]);
    $goal3Name = $db->escape_string($line[94]);
    $goal3RitScore = $db->escape_string($line[95]);
    $goal3StdErr = $db->escape_string($line[96]);
    $goal3Range = $db->escape_string($line[97]);
    $goal3Adjective = $db->escape_string($line[98]);
    $goal4Name = $db->escape_string($line[99]);
    $goal4RitScore = $db->escape_string($line[100]);
    $goal4StdErr = $db->escape_string($line[101]);
    $goal4Range = $db->escape_string($line[102]);
    $goal4Adjective = $db->escape_string($line[103]);
    $goal5Name = $db->escape_string($line[104]);
    $goal5RitScore = $db->escape_string($line[105]);
    $goal5StdErr = $db->escape_string($line[106]);
    $goal5Range = $db->escape_string($line[107]);
    $goal5Adjective = $db->escape_string($line[108]);
    $goal6Name = $db->escape_string($line[109]);
    $goal6RitScore = $db->escape_string($line[110]);
    $goal6StdErr = $db->escape_string($line[111]);
    $goal6Range = $db->escape_string($line[112]);
    $goal6Adjective = $db->escape_string($line[113]);
    $goal7Name = $db->escape_string($line[114]);
    $goal7RitScore = $db->escape_string($line[115]);
    $goal7StdErr = $db->escape_string($line[116]);
    $goal7Range = $db->escape_string($line[117]);
    $goal7Adjective = $db->escape_string($line[118]);
    $goal8Name = $db->escape_string($line[119]);
    $goal8RitScore = $db->escape_string($line[120]);
    $goal8StdErr = $db->escape_string($line[121]);
    $goal8Range = $db->escape_string($line[122]);
    $goal8Adjective = $db->escape_string($line[123]);
    $testStartTime = $db->escape_string($line[28]);
    $percentCorrect = $db->escape_string($line[34]);
    $projectedProficiencyStudy1 = $db->escape_string($line[132]);
    $projectedProficiencyLevel1 = $db->escape_string($line[133]);
    $projectedProficiencyStudy2 = $db->escape_string($line[134]);
    $projectedProficiencyLevel2 = $db->escape_string($line[135]);
    $projectedProficiencyStudy3 = $db->escape_string($line[136]);
    $projectedProficiencyLevel3 = $db->escape_string($line[137]);
    $accommodationCategory = $db->escape_string($line[124]);
    $accommodations = $db->escape_string($line[125]);
    $typicalFallToFallGrowth = $db->escape_string($line[126]);
    $typicalFallToWinterGrowth = $db->escape_string($line[127]);
    $typicalFallToSpringGrowth = $db->escape_string($line[128]);
    $typicalWinterToWinterGrowth = $db->escape_string($line[129]);
    $typicalWinterToSpringGrowth = $db->escape_string($line[130]);
    $typicalSpringToSpringGrowth = $db->escape_string($line[131]);
    $projectedProficiencyStudy4 = $db->escape_string($line[138]);
    $projectedProficiencyLevel4 = $db->escape_string($line[139]);
    $projectedProficiencyStudy5 = $db->escape_string($line[140]);
    $projectedProficiencyLevel5 = $db->escape_string($line[141]);
    $projectedProficiencyStudy6 = $db->escape_string($line[142]);
    $projectedProficiencyLevel6 = $db->escape_string($line[143]);
    $projectedProficiencyStudy7 = $db->escape_string($line[144]);
    $projectedProficiencyLevel7 = $db->escape_string($line[145]);
    $projectedProficiencyStudy8 = $db->escape_string($line[146]);
    $projectedProficiencyLevel8 = $db->escape_string($line[147]);
    $projectedProficiencyStudy9 = $db->escape_string($line[148]);
    $projectedProficiencyLevel9 = $db->escape_string($line[149]);
    $projectedProficiencyStudy10 = $db->escape_string($line[150]);
    $projectedProficiencyLevel10 = $db->escape_string($line[151]);

    $sql = "
    (
      '$termName',
      '$districtName',
      '$schoolName',
      '$studentLastName',
      '$studentFirstName',
      '$studentMI',
      '$studentID',
      '$studentDateOfBirth',
      '$studentEthnicGroup',
      '$studentGender',
      '$grade',
      '$measurementScale',
      '$discipline',
      '$normsReferenceData',
      '$WISelectedAYFall',
      '$WISelectedAYWinter',
      '$WISelectedAYSpring',
      '$WIPreviousAYFall',
      '$WIPreviousAYWinter',
      '$WIPreviousAYSpring',
      '$testType',
      '$testName',
      '$testStartDate',
      '$testDurationMinutes',
      '$testRITScore',
      '$testStandardError',
      '$testPercentile',
      '$fallToFallProjectedGrowth',
      '$fallToFallObservedGrowth',
      '$fallToFallObservedGrowthSE',
      '$fallToFallMetProjectedGrowth',
      '$fallToFallConditionalGrowthIndex',
      '$fallToFallConditionalGrowthPercentile',
      '$fallToWinterProjectedGrowth',
      '$fallToWinterObservedGrowth',
      '$fallToWinterObservedGrowthSE',
      '$fallToWinterMetProjectedGrowth',
      '$fallToWinterConditionalGrowthIndex',
      '$fallToWinterConditionalGrowthPercentile',
      '$fallToSpringProjectedGrowth',
      '$fallToSpringObservedGrowth',
      '$fallToSpringObservedGrowthSE',
      '$fallToSpringMetProjectedGrowth',
      '$fallToSpringConditionalGrowthIndex',
      '$fallToSpringConditionalGrowthPercentile',
      '$winterToWinterProjectedGrowth',
      '$winterToWinterObservedGrowth',
      '$winterToWinterObservedGrowthSE',
      '$winterToWinterMetProjectedGrowth',
      '$winterToWinterConditionalGrowthIndex',
      '$winterToWinterConditionalGrowthPercentile',
      '$winterToSpringProjectedGrowth',
      '$winterToSpringObservedGrowth',
      '$winterToSpringObservedGrowthSE',
      '$winterToSpringMetProjectedGrowth',
      '$winterToSpringConditionalGrowthIndex',
      '$winterToSpringConditionalGrowthPercentile',
      '$springToSpringProjectedGrowth',
      '$springToSpringObservedGrowth',
      '$springToSpringObservedGrowthSE',
      '$springToSpringMetProjectedGrowth',
      '$springToSpringConditionalGrowthIndex',
      '$springToSpringConditionalGrowthPercentile',
      '$RITtoReadingScore',
      '$RITtoReadingMin',
      '$RITtoReadingMax',
      '$goal1Name',
      '$goal1RitScore',
      '$goal1StdErr',
      '$goal1Range',
      '$goal1Adjective',
      '$goal2Name',
      '$goal2RitScore',
      '$goal2StdErr',
      '$goal2Range',
      '$goal2Adjective',
      '$goal3Name',
      '$goal3RitScore',
      '$goal3StdErr',
      '$goal3Range',
      '$goal3Adjective',
      '$goal4Name',
      '$goal4RitScore',
      '$goal4StdErr',
      '$goal4Range',
      '$goal4Adjective',
      '$goal5Name',
      '$goal5RitScore',
      '$goal5StdErr',
      '$goal5Range',
      '$goal5Adjective',
      '$goal6Name',
      '$goal6RitScore',
      '$goal6StdErr',
      '$goal6Range',
      '$goal6Adjective',
      '$goal7Name',
      '$goal7RitScore',
      '$goal7StdErr',
      '$goal7Range',
      '$goal7Adjective',
      '$goal8Name',
      '$goal8RitScore',
      '$goal8StdErr',
      '$goal8Range',
      '$goal8Adjective',
      '$testStartTime',
      '$percentCorrect',
      '$projectedProficiencyStudy1',
      '$projectedProficiencyLevel1',
      '$projectedProficiencyStudy2',
      '$projectedProficiencyLevel2',
      '$projectedProficiencyStudy3',
      '$projectedProficiencyLevel3',
      '$accommodationCategory',
      '$accommodations',
      '$typicalFallToFallGrowth',
      '$typicalFallToWinterGrowth',
      '$typicalFallToSpringGrowth',
      '$typicalWinterToWinterGrowth',
      '$typicalWinterToSpringGrowth',
      '$typicalSpringToSpringGrowth',
      '$projectedProficiencyStudy4',
      '$projectedProficiencyLevel4',
      '$projectedProficiencyStudy5',
      '$projectedProficiencyLevel5',
      '$projectedProficiencyStudy6',
      '$projectedProficiencyLevel6',
      '$projectedProficiencyStudy7',
      '$projectedProficiencyLevel7',
      '$projectedProficiencyStudy8',
      '$projectedProficiencyLevel8',
      '$projectedProficiencyStudy9',
      '$projectedProficiencyLevel9',
      '$projectedProficiencyStudy10',
      '$projectedProficiencyLevel10'
    )";
    return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName)
{
    $insertSQL = "INSERT INTO Abre_MAPData
                SELECT 0, {$temporaryTableName}.*, ?, NOW()
                FROM {$temporaryTableName}";
    $stmt = $db->stmt_init();
    $stmt->prepare($insertSQL);
    $stmt->bind_param("i", $siteID);
    $stmt->execute();
    $insertedRows = $db->affected_rows;
    $stmt->close();

    return $insertedRows;
}

function dropTemporaryTable($db, $temporaryTableName)
{
    $dropTemporaryTableSQL = "DROP TABLE {$temporaryTableName}";
    $stmt = $db->stmt_init();
    $stmt->prepare($dropTemporaryTableSQL);
    $stmt->execute();
    $stmt->close();
}
