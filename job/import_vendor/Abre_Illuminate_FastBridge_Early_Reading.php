<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre FastBridge Early Reading';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "[Early Reading English]";

    //Define
    define("SAFE_COLUMN_COUNT", 57);
    define("MAX_IMPORT_LIMIT", 25);

    $error = null;
    $skip = null;
    $separator = "\r\n";
    $currentSchoolYearID = getCurrentSchoolYearID($db);

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

    if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO `abre_fastbridge_early_reading`
                 (`Assessment`,
                  `Assessment_Language`,`State`,`District`,`School`,`Local_ID`,`State_ID`,`FAST_ID`,`First_Name`,
                  `Last_Name`,`Gender`,`DOB`,`Race`,`Special_Ed_Status`,`Grade`,
                  `Quarter_1_early_reading_composite_score`,`Quarter_1_early_reading_Percentile_at_School`,
                  `Quarter_1_early_reading_Percentile_at_LEA`,`Quarter_1_early_reading_Percentile_at_Nation`,
                  `Quarter_1_early_reading_Risk_Level`,`Quarter_1_early_reading_Final_Date`,

                  `Quarter_1_concepts_of_print_total_items`,`Quarter_1_concepts_of_print_items_correct`,`Quarter_1_concepts_of_print_risk_level`,
                  `Quarter_1_onset_sounds_total_items`,`Quarter_1_onset_sounds_items_correct`,`Quarter_1_onset_sounds_risk_level`,`Quarter_1_letter_names_WRC_per_min`,
                  `Quarter_1_letter_names_risk_level`,`Quarter_1_letter_sounds_WRC_per_min`,`Quarter_1_letter_sounds_risk_level`,`Quarter_1_word_segmenting_total_items`,
                  `Quarter_1_word_segmenting_items_correct`,`Quarter_1_word_segmenting_risk_level`,`Quarter_1_nonsense_words_WRC_per_min`,`Quarter_1_nonsense_words_risk_level`,
                  `Quarter_1_sight_words_WRC_per_min`,`Quarter_1_sight_words_risk_level`,`Quarter_1_sentence_reading_WRC_per_min`,`Quarter_1_sentence_reading_risk_level`,

                  `Quarter_2_early_reading_composite_score`, `Quarter_2_early_reading_Percentile_at_School`,
                  `Quarter_2_early_reading_Percentile_at_LEA`,`Quarter_2_early_reading_Percentile_at_Nation`,
                  `Quarter_2_early_reading_Risk_Level`,`Quarter_2_early_reading_Final_Date`,
                  `Growth_Score_from_Quarter_1_to_Quarter_2`,`School_Growth_Percentile_from_Quarter_1_to_Quarter_2`,
                  `District_Growth_Percentile_from_Quarter_1_to_Quarter_2`,`National_Growth_Percentile_from_Quarter_1_to_Quarter_2`,
                  `Growth_Percentile_by_Start_Score_from_Quarter_1_to_Quarter_2`,`Quarter_3_early_reading_composite_score`,
                  `Quarter_3_early_reading_Percentile_at_School`,`Quarter_3_early_reading_Percentile_at_LEA`,
                  `Quarter_3_early_reading_Percentile_at_Nation`,`Quarter_3_early_reading_Risk_Level`,
                  `Quarter_3_early_reading_Final_Date`,`Growth_Score_from_Quarter_1_to_Quarter_3`,
                  `School_Growth_Percentile_from_Quarter_1_to_Quarter_3`,`District_Growth_Percentile_from_Quarter_1_to_Quarter_3`,
                  `National_Growth_Percentile_from_Quarter_1_to_Quarter_3`,`Growth_Percentile_by_Start_Score_from_Quarter_1_to_Quarter_3`,
                  `Growth_Score_from_Quarter_2_to_Quarter_3`,`School_Growth_Percentile_from_Quarter_2_to_Quarter_3`,
                  `District_Growth_Percentile_from_Quarter_2_to_Quarter_3`,`National_Growth_Percentile_from_Quarter_2_to_Quarter_3`,
                  `Growth_Percentile_by_Start_Score_from_Quarter_2_to_Quarter_3`,`Quarter_4_early_reading_composite_score`,
                  `Quarter_4_early_reading_Percentile_at_School`,`Quarter_4_early_reading_Percentile_at_LEA`,
                  `Quarter_4_early_reading_Percentile_at_Nation`,`Quarter_4_early_reading_Risk_Level`,`Quarter_4_early_reading_Final_Date`,
                  `site_id`,`school_year_id`, 
                
                  `Quarter_2_concepts_of_print_total_items`,`Quarter_2_concepts_of_print_items_correct`,`Quarter_2_concepts_of_print_risk_level`,   
                  `Quarter_2_onset_sounds_total_items`,`Quarter_2_onset_sounds_items_correct`,`Quarter_2_onset_sounds_risk_level`,`Quarter_2_letter_names_WRC_per_min`,   
                  `Quarter_2_letter_names_risk_level`,`Quarter_2_letter_sounds_WRC_per_min`,`Quarter_2_letter_sounds_risk_level`,`Quarter_2_word_segmenting_total_items`,   
                  `Quarter_2_word_segmenting_items_correct`,`Quarter_2_word_segmenting_risk_level`,`Quarter_2_nonsense_words_WRC_per_min`,`Quarter_2_nonsense_words_risk_level`,   
                  `Quarter_2_sight_words_WRC_per_min`,`Quarter_2_sight_words_risk_level`,`Quarter_2_sentence_reading_WRC_per_min`,`Quarter_2_sentence_reading_risk_level`,   
                  
                  `Quarter_3_concepts_of_print_total_items`,`Quarter_3_concepts_of_print_items_correct`,`Quarter_3_concepts_of_print_risk_level`,`Quarter_3_onset_sounds_total_items`,   
                  `Quarter_3_onset_sounds_items_correct`,`Quarter_3_onset_sounds_risk_level`,`Quarter_3_letter_names_WRC_per_min`,`Quarter_3_letter_names_risk_level`,   
                  `Quarter_3_letter_sounds_WRC_per_min`,`Quarter_3_letter_sounds_risk_level`,`Quarter_3_word_segmenting_total_items`,`Quarter_3_word_segmenting_items_correct`,   
                  `Quarter_3_word_segmenting_risk_level`,`Quarter_3_nonsense_words_WRC_per_min`,`Quarter_3_nonsense_words_risk_level`,`Quarter_3_sight_words_WRC_per_min`,   
                  `Quarter_3_sight_words_risk_level`,`Quarter_3_sentence_reading_WRC_per_min`,`Quarter_3_sentence_reading_risk_level`,
                  
                  `Quarter_4_concepts_of_print_total_items`,   
                  `Quarter_4_concepts_of_print_items_correct`,`Quarter_4_concepts_of_print_risk_level`,`Quarter_4_onset_sounds_total_items`,`Quarter_4_onset_sounds_items_correct`,   
                  `Quarter_4_onset_sounds_risk_level`,`Quarter_4_letter_names_WRC_per_min`,`Quarter_4_letter_names_risk_level`,`Quarter_4_letter_sounds_WRC_per_min`,   
                  `Quarter_4_letter_sounds_risk_level`,`Quarter_4_word_segmenting_total_items`,`Quarter_4_word_segmenting_items_correct`,`Quarter_4_word_segmenting_risk_level`,   
                  `Quarter_4_nonsense_words_WRC_per_min`, `Quarter_4_nonsense_words_risk_level`, `Quarter_4_sight_words_WRC_per_min`, `Quarter_4_sight_words_risk_level`,   
                  `Quarter_4_sentence_reading_WRC_per_min`,`Quarter_4_sentence_reading_risk_level`)
                  VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM abre_fastbridge_early_reading WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
    do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }
      if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;
        $assessment = $data[0] ? trim($db->escape_string($data[0])) : "";
        $assessmentLanguage = $data[1] ? trim($db->escape_string($data[1])) : "";
        $state = $data[2] ? trim($db->escape_string($data[2])) : "";
        $district = $data[3] ? trim($db->escape_string($data[3])) : "";
        $school = $data[4] ? trim($db->escape_string($data[4])) : "";
        $localID = $data[5] ? trim($db->escape_string($data[5])) : "";
        $stateID = $data[6] ? trim($db->escape_string($data[6])) : "";
        $fastID = $data[7] ? trim($db->escape_string($data[7])) : "" ;
        $firstName = $data[8] ? trim($db->escape_string($data[8])) : "";
        $lastName = $data[9] ? trim($db->escape_string($data[9])) : "";
        $gender = $data[10] ? trim($db->escape_string($data[10])) : "";

        //format date type for insert
        if($data[11] !== null){
          $dobRaw = $data[11];
          $dateTimeObject = new DateTime($dobRaw, new DateTimeZone("UTC"));   
          $dobFormatted = $dateTimeObject->format("Y-m-d");
          $dob = "'".$dobFormatted."'";
        } else {
          $dob = "";
        }

        $race = $data[12] ? trim($db->escape_string($data[12])) : "";
        $specialEdStatus = $data[13] ? trim($db->escape_string($data[13])) : "";
        $grade = $data[14] ? trim($db->escape_string($data[14])) : "";
        
        //NOTE: $data[15] is an empty cell hence why it is skipped

        $q1CompositeScore = is_numeric($data[16]) ? $data[16] : "NULL";
        $q1PercentileAtSchool = is_numeric($data[17]) ? $data[17] : "NULL";
        $q1PercentileAtLEA = is_numeric($data[18]) ? $data[18] : "NULL";
        $q1PercentileAtNation = is_numeric($data[19]) ? $data[19] : "NULL";
        $q1RiskLevel = $data[20] ? trim($db->escape_string($data[20])) : "NULL";

        //format date type for insert
        if($data[21]){
          $q1FinalDate = $data[21];
          $q1FinalDate = date('Y-m-d', strtotime($q1FinalDate));
          $q1FinalDate = "'".$q1FinalDate."'";
        } else {
          $q1FinalDate = "NULL";
        }

        $q1ConceptsTotalItems = is_numeric($data[22]) ? $data[22] : "NULL";
        $q1ConceptsItemsCorrect = is_numeric($data[23]) ? $data[23] : "NULL";
        $q1ConceptsRiskLevel = $data[24] ? trim($db->escape_string($data[24])) : "NULL";
        $q1OnsetSoundsTotalItems = is_numeric($data[25]) ? $data[25] : "NULL";
        $q1OnsetSoundsItemsCorrect = is_numeric($data[26]) ? $data[26] : "NULL";
        $q1OnsetSoundsRiskLevel = $data[27] ? trim($db->escape_string($data[27])) : "NULL";
        $q1LetterNamesWrcPerMin = is_numeric($data[28]) ? $data[28] : "NULL";
        $q1LetterNamesRiskLevel = $data[29] ? trim($db->escape_string($data[29])) : "NULL";
        $q1LetterSoundsWrcPerMin = is_numeric($data[30]) ? $data[30] : "NULL";
        $q1LetterSoundsRiskLevel = $data[31] ? trim($db->escape_string($data[31])) : "NULL";
        $q1WordSegmentingTotalItems = is_numeric($data[32]) ? $data[32] : "NULL";
        $q1WordSegmentingItemsCorrect = is_numeric($data[33]) ? $data[33] : "NULL";
        $q1WordSegmentingRiskLevel = $data[34] ? trim($db->escape_string($data[34])) : "NULL";
        $q1NonsenseWordsWrcPerMin = is_numeric($data[35]) ? $data[35] : "NULL";
        $q1NonsenseWordsRiskLevel = $data[36] ? trim($db->escape_string($data[36])) : "NULL";
        $q1SightWordsWrcPerMin = is_numeric($data[37]) ? $data[37] : "NULL";
        $q1SightWordsRiskLevel = $data[38] ? trim($db->escape_string($data[38])) : "NULL";
        $q1SentenceReadingWrcPerMin = is_numeric($data[39]) ? $data[39] : "NULL";
        $q1SentenceReadingRiskLevel = $data[40] ? trim($db->escape_string($data[40])) : "NULL";

        $q2CompositeScore = is_numeric($data[41]) ? $data[41] : "NULL";
        $q2PercentileAtSchool = is_numeric($data[42]) ? $data[42] : "NULL";
        $q2PercentileAtLEA = is_numeric($data[43]) ? $data[43] : "NULL";
        $q2PercentileAtNation = is_numeric($data[44]) ? $data[44] : "NULL";
        $q2RiskLevel = $data[45] ? $data[45] : "NULL";

        //format date type for insert
        if($data[46]){
          $q2FinalDate = $data[46];
          $q2FinalDate = date('Y-m-d', strtotime($q2FinalDate));
          $q2FinalDate = "'".$q2FinalDate."'";
        } else {
          $q2FinalDate = "NULL";
        }

        $growthScoreFromQ1ToQ2 = is_numeric($data[47]) ? $data[47] : "NULL";
        $schoolGrowthPercentileFromQ1ToQ2 = is_numeric($data[48]) ? $data[48] : "NULL";
        $districtGrowthPercentileFromQ1ToQ2 = is_numeric($data[49]) ? $data[49] : "NULL";
        $nationalGrowthPercentileFromQ1toQ2 = is_numeric($data[50]) ? $data[50] : "NULL";
        $growthPercentileByStartScoreFromQ1ToQ2 = is_numeric($data[51]) ? $data[51] : "NULL";

        $q3CompositeScore = is_numeric($data[52]) ? $data[52] : "NULL";
        $q3PercentileAtSchool = is_numeric($data[53]) ? $data[53] : "NULL";
        $q3PercentileAtLEA = is_numeric($data[54]) ? $data[54] : "NULL";
        $q3PercentileAtNation = is_numeric($data[55]) ? $data[55] : "NULL";
        $q3RiskLevel = $data[56] ? trim($db->escape_string($data[56])) : "NULL";

        if($data[57]){
          $q3FinalDate = $data[57];
          $q3FinalDate = date('Y-m-d', strtotime($q3FinalDate));
          $q3FinalDate = "'".$q3FinalDate."'";
        } else {
          $q3FinalDate = "NULL";
        }
        
        $growthScoreFromQ1ToQ3 = is_numeric($data[58]) ? $data[58] : "NULL";
        $schoolGrowthPercentileFromQ1ToQ3 = is_numeric($data[59]) ? $data[59] : "NULL";
        $districtGrowthPercentileFromQ1ToQ3 = is_numeric($data[60]) ? $data[60] : "NULL";
        $nationalGrowthPercentileFromQ1toQ3 = is_numeric($data[61]) ? $data[61] : "NULL";
        $growthPercentileByStartScoreFromQ1ToQ3 = is_numeric($data[62]) ? $data[62] : "NULL";

        $growthScoreFromQ2ToQ3 = is_numeric($data[63]) ? $data[63] : "NULL";
        $schoolGrowthPercentileFromQ2ToQ3 = is_numeric($data[64]) ? $data[64] : "NULL";
        $districtGrowthPercentileFromQ2ToQ3 = is_numeric($data[65]) ? $data[65] : "NULL";
        $nationalGrowthPercentileFromQ2toQ3 = is_numeric($data[66]) ? $data[66] : "NULL";
        $growthPercentileByStartScoreFromQ2ToQ3 = is_numeric($data[67]) ? $data[67] : "NULL";
        
        $q4CompositeScore = is_numeric($data[68]) ? $data[68] : "NULL";
        $q4PercentileAtSchool = is_numeric($data[69]) ? $data[69] : "NULL";
        $q4PercentileAtLEA = is_numeric($data[70]) ? $data[70] : "NULL";
        $q4PercentileAtNation = is_numeric($data[71]) ? $data[71] : "NULL";
        $q4RiskLevel = $data[72] ? trim($db->escape_string($data[72])) : "NULL";
        
        if($data[73]){
          $q4FinalDate = $data[73];
          $q4FinalDate = date('Y-m-d', strtotime($q4FinalDate));
          $q4FinalDate = "'".$q4FinalDate."'";
        } else {
          $q4FinalDate = "NULL";
        }

        $q2ConceptsTotalItems = is_numeric($data[74]) ? $data[74] : "NULL";
        $q2ConceptsItemsCorrect = is_numeric($data[75]) ? $data[75] : "NULL";
        $q2ConceptsRiskLevel = $data[76] ? trim($db->escape_string($data[76])) : "NULL";
        $q2OnsetSoundsTotalItems = is_numeric($data[77]) ? $data[77] : "NULL";
        $q2OnsetSoundsItemsCorrect = is_numeric($data[78]) ? $data[78] : "NULL";
        $q2OnsetSoundsRiskLevel = $data[79] ? trim($db->escape_string($data[79])) : "NULL";
        $q2LetterNamesWrcPerMin = is_numeric($data[80]) ? $data[80] : "NULL";
        $q2LetterNamesRiskLevel = $data[81] ? trim($db->escape_string($data[81])) : "NULL";
        $q2LetterSoundsWrcPerMin = is_numeric($data[82]) ? $data[82] : "NULL";
        $q2LetterSoundsRiskLevel = $data[83] ? trim($db->escape_string($data[83])) : "NULL";
        $q2WordSegmentingTotalItems = is_numeric($data[84]) ? $data[84] : "NULL";
        $q2WordSegmentingItemsCorrect = is_numeric($data[85]) ? $data[85] : "NULL";
        $q2WordSegmentingRiskLevel = $data[86] ? trim($db->escape_string($data[86])) : "NULL";
        $q2NonsenseWordsWrcPerMin = is_numeric($data[87]) ? $data[87] : "NULL";
        $q2NonsenseWordsRiskLevel = $data[88] ? trim($db->escape_string($data[88])) : "NULL";
        $q2SightWordsWrcPerMin = is_numeric($data[89]) ? $data[89] : "NULL";
        $q2SightWordsRiskLevel = $data[90] ? trim($db->escape_string($data[90])) : "NULL";
        $q2SentenceReadingWrcPerMin = is_numeric($data[91]) ? $data[91] : "NULL";
        $q2SentenceReadingRiskLevel = $data[92] ? trim($db->escape_string($data[92])) : "NULL";

        $q3ConceptsTotalItems = is_numeric($data[93]) ? $data[93] : "NULL";
        $q3ConceptsItemsCorrect = is_numeric($data[94]) ? $data[94] : "NULL";
        $q3ConceptsRiskLevel = $data[95] ? trim($db->escape_string($data[95])) : "NULL";
        $q3OnsetSoundsTotalItems = is_numeric($data[96]) ? $data[96] : "NULL";
        $q3OnsetSoundsItemsCorrect = is_numeric($data[97]) ? $data[97] : "NULL";
        $q3OnsetSoundsRiskLevel = $data[98] ? trim($db->escape_string($data[98])) : "NULL";
        $q3LetterNamesWrcPerMin = is_numeric($data[99]) ? $data[99] : "NULL";
        $q3LetterNamesRiskLevel = $data[100] ? trim($db->escape_string($data[100])) : "NULL";
        $q3LetterSoundsWrcPerMin = is_numeric($data[101]) ? $data[101] : "NULL";
        $q3LetterSoundsRiskLevel = $data[102] ? trim($db->escape_string($data[102])) : "NULL";
        $q3WordSegmentingTotalItems = is_numeric($data[103]) ? $data[103] : "NULL";
        $q3WordSegmentingItemsCorrect = is_numeric($data[104]) ? $data[104] : "NULL";
        $q3WordSegmentingRiskLevel = $data[105] ? trim($db->escape_string($data[105])) : "NULL";
        $q3NonsenseWordsWrcPerMin = is_numeric($data[106]) ? $data[106] : "NULL";
        $q3NonsenseWordsRiskLevel = $data[107] ? trim($db->escape_string($data[107])) : "NULL";
        $q3SightWordsWrcPerMin = is_numeric($data[108]) ? $data[108] : "NULL";
        $q3SightWordsRiskLevel = $data[109] ? trim($db->escape_string($data[109])) : "NULL";
        $q3SentenceReadingWrcPerMin = is_numeric($data[110]) ? $data[110] : "NULL";
        $q3SentenceReadingRiskLevel = $data[111] ?trim($db->escape_string($data[111])) : "NULL";

        $q4ConceptsTotalItems = is_numeric($data[112]) ? $data[112] : "NULL";
        $q4ConceptsItemsCorrect = is_numeric($data[113]) ? $data[113] : "NULL";
        $q4ConceptsRiskLevel = $data[114] ? trim($db->escape_string($data[114])) : "NULL";
        $q4OnsetSoundsTotalItems = is_numeric($data[115]) ? $data[115] : "NULL";
        $q4OnsetSoundsItemsCorrect = is_numeric($data[116]) ? $data[116] : "NULL";
        $q4OnsetSoundsRiskLevel = $data[117] ? trim($db->escape_string($data[117])) : "NULL";
        $q4LetterNamesWrcPerMin = is_numeric($data[118]) ? $data[118] : "NULL";
        $q4LetterNamesRiskLevel = $data[119] ? trim($db->escape_string($data[119])) : "NULL";
        $q4LetterSoundsWrcPerMin = is_numeric($data[120]) ? $data[120] : "NULL";
        $q4LetterSoundsRiskLevel = $data[121] ? trim($db->escape_string($data[121])) : "NULL";
        $q4WordSegmentingTotalItems = is_numeric($data[122]) ? $data[122] : "NULL";
        $q4WordSegmentingItemsCorrect = is_numeric($data[123]) ? $data[123] : "NULL";
        $q4WordSegmentingRiskLevel = $data[124] ? trim($db->escape_string($data[124])) : "NULL";
        $q4NonsenseWordsWrcPerMin = is_numeric($data[125]) ? $data[125] : "NULL";
        $q4NonsenseWordsRiskLevel = $data[126] ? trim($db->escape_string($data[126])) : "NULL";
        $q4SightWordsWrcPerMin = is_numeric($data[127]) ? $data[127] : "NULL";
        $q4SightWordsRiskLevel = $data[128] ? trim($db->escape_string($data[128])) : "NULL";
        $q4SentenceReadingWrcPerMin = is_numeric($data[129]) ? $data[129] : "NULL";
        $q4SentenceReadingRiskLevel = $data[130] ? trim($db->escape_string($data[130])) : "NULL";

        $valuesToImport [] = "('$assessment', '$assessmentLanguage', '$state', '$district', '$school',
                            '$localID', '$stateID', '$fastID', '$firstName', '$lastName', '$gender',
                              $dob, '$race', '$specialEdStatus', '$grade', $q1CompositeScore, $q1PercentileAtSchool,
                              $q1PercentileAtLEA, $q1PercentileAtNation, '$q1RiskLevel', $q1FinalDate, 
                              $q1ConceptsTotalItems,$q1ConceptsItemsCorrect,'$q1ConceptsRiskLevel',
                              $q1OnsetSoundsTotalItems,$q1OnsetSoundsItemsCorrect,'$q1OnsetSoundsRiskLevel',
                              $q1LetterNamesWrcPerMin,'$q1LetterNamesRiskLevel',$q1LetterSoundsWrcPerMin,
                              '$q1LetterSoundsRiskLevel',$q1WordSegmentingTotalItems,$q1WordSegmentingItemsCorrect,
                              '$q1WordSegmentingRiskLevel',$q1NonsenseWordsWrcPerMin,'$q1NonsenseWordsRiskLevel',
                              $q1SightWordsWrcPerMin,'$q1SightWordsRiskLevel',$q1SentenceReadingWrcPerMin,
                              '$q1SentenceReadingRiskLevel', $q2CompositeScore,

                              $q2PercentileAtSchool, $q2PercentileAtLEA, $q2PercentileAtNation,
                              '$q2RiskLevel', $q2FinalDate, $growthScoreFromQ1ToQ2, $schoolGrowthPercentileFromQ1ToQ2,
                              $districtGrowthPercentileFromQ1ToQ2, $nationalGrowthPercentileFromQ1toQ2,
                              $growthPercentileByStartScoreFromQ1ToQ2, $q3CompositeScore, $q3PercentileAtSchool,
                              $q3PercentileAtLEA, $q3PercentileAtNation, '$q3RiskLevel', $q3FinalDate,
                              $growthScoreFromQ1ToQ3, $schoolGrowthPercentileFromQ1ToQ3, $districtGrowthPercentileFromQ1ToQ3,
                              $nationalGrowthPercentileFromQ1toQ3, $growthPercentileByStartScoreFromQ1ToQ3,
                              $growthScoreFromQ2ToQ3, $schoolGrowthPercentileFromQ2ToQ3, $districtGrowthPercentileFromQ2ToQ3,
                              $nationalGrowthPercentileFromQ2toQ3, $growthPercentileByStartScoreFromQ2ToQ3,
                              $q4CompositeScore, $q4PercentileAtSchool, $q4PercentileAtLEA, $q4PercentileAtNation, '$q4RiskLevel',

                               $q4FinalDate, $siteID, $currentSchoolYearID,
                               $q2ConceptsTotalItems,$q2ConceptsItemsCorrect,'$q2ConceptsRiskLevel',
                               $q2OnsetSoundsTotalItems,$q2OnsetSoundsItemsCorrect,'$q2OnsetSoundsRiskLevel',
                               $q2LetterNamesWrcPerMin,'$q2LetterNamesRiskLevel',$q2LetterSoundsWrcPerMin,
                               '$q2LetterSoundsRiskLevel',$q2WordSegmentingTotalItems,$q2WordSegmentingItemsCorrect,
                               '$q2WordSegmentingRiskLevel',$q2NonsenseWordsWrcPerMin,'$q2NonsenseWordsRiskLevel',
                               $q2SightWordsWrcPerMin,'$q2SightWordsRiskLevel',$q2SentenceReadingWrcPerMin,
                               '$q2SentenceReadingRiskLevel',
                               $q3ConceptsTotalItems,$q3ConceptsItemsCorrect,'$q3ConceptsRiskLevel',
                               $q3OnsetSoundsTotalItems,$q3OnsetSoundsItemsCorrect,'$q3OnsetSoundsRiskLevel',
                               $q3LetterNamesWrcPerMin,'$q3LetterNamesRiskLevel',$q3LetterSoundsWrcPerMin,
                               '$q3LetterSoundsRiskLevel',$q3WordSegmentingTotalItems,$q3WordSegmentingItemsCorrect,
                               '$q3WordSegmentingRiskLevel',$q3NonsenseWordsWrcPerMin,'$q3NonsenseWordsRiskLevel',
                               $q3SightWordsWrcPerMin,'$q3SightWordsRiskLevel',$q3SentenceReadingWrcPerMin,
                               '$q3SentenceReadingRiskLevel',
                               $q4ConceptsTotalItems,$q4ConceptsItemsCorrect,'$q4ConceptsRiskLevel',
                               $q4OnsetSoundsTotalItems,$q4OnsetSoundsItemsCorrect,'$q4OnsetSoundsRiskLevel',
                               $q4LetterNamesWrcPerMin,'$q4LetterNamesRiskLevel',$q4LetterSoundsWrcPerMin,
                               '$q4LetterSoundsRiskLevel',$q4WordSegmentingTotalItems,$q4WordSegmentingItemsCorrect,
                               '$q4WordSegmentingRiskLevel',$q4NonsenseWordsWrcPerMin,'$q4NonsenseWordsRiskLevel',
                               $q4SightWordsWrcPerMin,'$q4SightWordsRiskLevel',$q4SentenceReadingWrcPerMin,
                               '$q4SentenceReadingRiskLevel'
                               )";


        if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
        }
      }
      $line = strtok($separator);

    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }
  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
