<?php

require(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../../vendor/autoload.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config) {

  $cronName = 'Terra Nova Import';

  try {

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
      throw new Exception("Login to SFTP failed.");
    }
    $sftp->chdir('terra-nova');

    $files = $sftp->nlist();
    foreach ($files as $fileName) {
      if ($fileName != "." && $fileName != "..") {
        $localTempFile = tempnam(sys_get_temp_dir(), 'terra-nova');

        //Write file to local temp file
        $sftp->get($fileName, $localTempFile);

        $version = getVersion($fileName);
        if ($version == 1.0) {
          require_once('Abre_TerraNova_Run_v1.0.php');
          importTerraNovaData($db, $siteID, $localTempFile, $fileName);
        }
      }
    }
  } catch(Exception $ex) {
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];

  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
