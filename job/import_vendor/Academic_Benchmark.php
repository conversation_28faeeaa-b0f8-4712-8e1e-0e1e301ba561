<?php

/*
* Copyright 2025 Abre.io Inc.
*/

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use Google\Cloud\Storage\StorageClient;
use LearnositySdk\Request\DataApi;

/**
 * Helper function for common file operations
 * @param array $data The data to encode and write to temp file
 * @return resource Temporary file handle
 * @throws Exception If JSON encoding fails
 */
function _createTempFile($data)
{
    $jsonEncoded = json_encode($data);
    if ($jsonEncoded === false) {
        throw new Exception('Failed to encode JSON');
    }
    
    $tempFile = tmpfile();
    fwrite($tempFile, $jsonEncoded);
    rewind($tempFile);
    return $tempFile;
}

/**
 * Unified upload function for GCS
 * @param array $data The data to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Current date in Ymd format
 * @param string $path The path within the bucket
 * @param string $fileName The name of the file
 * @param string $logPrefix Prefix for log messages
 * @return void
 */
function _uploadToGCS($data, $bucket, $currentDate, $path, $fileName, $logPrefix)
{
    $tempFile = null;
    try {
        if (empty($data)) {
            error_log("No data to upload for {$logPrefix}");
            return;
        }

        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            throw new Exception('Failed to encode JSON');
        }
        
        $tempFile = tmpfile();
        if ($tempFile === false) {
            throw new Exception('Failed to create temporary file');
        }
        
        if (fwrite($tempFile, $jsonEncoded) === false) {
            throw new Exception('Failed to write to temporary file');
        }
        
        rewind($tempFile);
        
        $bucket->upload($tempFile, [
            'name' => "{$currentDate}/{$path}/{$fileName}"
        ]);

        error_log("Successfully uploaded {$logPrefix} to GCS");
    } catch (Exception $e) {
        error_log("Error uploading {$logPrefix} to GCS: {$e->getMessage()}");
    } finally {
        if ($tempFile !== null && is_resource($tempFile)) {
            fclose($tempFile);
        }
    }
}

/**
 * Helper function for GCS upload to GUIDs bucket
 * @param array $guids The GUIDs to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Current date in Ymd format
 * @return void
 */
function _uploadGuidsToGuidsBucket($guids, $bucket, $currentDate)
{
    _uploadToGCS(
        $guids,
        $bucket,
        $currentDate,
        'Academic_Benchmark',
        'Academic_Benchmark_GUIDs.json',
        'GUIDs'
    );
}

/**
 * Helper function for GCS upload to site-specific folder
 * @param array $data The data to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Current date in Ymd format
 * @param string|null $siteId Site identifier (optional)
 * @return void
 */
function _uploadToSiteGCS($data, $bucket, $currentDate, $siteId)
{
    _uploadToGCS(
        $data,
        $bucket,
        $currentDate,
        'site-id/' . $siteId,
        'Academic_Benchmark_Standards.json',
        'Academic Benchmark Standards'
    );
}

/**
 * Helper function for GCS upload to global folder
 * @param array $data The data to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Current date in Ymd format
 * @return void
 */
function _uploadToGlobalGCS($data, $bucket, $currentDate)
{
    _uploadToGCS(
        $data,
        $bucket,
        $currentDate,
        'filename/Academic_Benchmark',
        'Academic_Benchmark_Standards.json',
        'global standards data'
    );
}

/**
 * Extract GUIDs from Learnosity tags data
 * @param array $tagsData The tags data from Learnosity
 * @return array Array of unique GUIDs
 */
function _extractGuidsFromTags($tagsData)
{
    $guids = [];
    
    if (empty($tagsData) || !is_array($tagsData)) {
        error_log('No valid tags data to extract GUIDs from');
        return $guids;
    }
    
    foreach ($tagsData as $tag) {
        if (isset($tag['name']) && !empty($tag['name']) && $tag['type'] === 'ABStandardGUID') {
            $guids[] = $tag['name'];
        }
    }
    
    // Remove duplicates
    $guids = array_unique($guids);
    error_log('Extracted ' . count($guids) . ' unique GUIDs from tags data');
    
    return $guids;
}

/**
 * Make a request to AB Connect API
 * @param string $guid The GUID to fetch
 * @param string $apiKey AB Connect API key
 * @param string $apiSecret AB Connect API secret
 * @return array Response data
 */
function _makeABConnectRequest($guid, $apiKey, $apiSecret)
{
    try {
        // Generate expiration (5 minutes from now)
        $expires = time() + 300;
        
        // Create signature
        $method = 'GET';
        $message = "{$expires}\n\n{$method}";
        $signature = base64_encode(hash_hmac('sha256', $message, $apiSecret, true));
        
        // Set up cURL request
        $ch = curl_init();
        $url = "https://api.abconnect.instructure.com/rest/v4.1/standards/{$guid}?fields[standards]=*";
        
        // Add authentication parameters
        $params = [
            'partner.id' => $apiKey,
            'auth.signature' => $signature,
            'auth.expires' => $expires
        ];
        
        $url .= '&' . http_build_query($params);
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        
        // Execute request
        $response = curl_exec($ch);
        
        // Get HTTP status code
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        // Check for cURL errors
        if (curl_errno($ch)) {
            throw new Exception('cURL Error: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        // Decode response
        $data = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('JSON decode error: ' . json_last_error_msg());
        }
        
        // The standards data is directly in the data field
        if (isset($data['data'])) {
            return $data['data'];
        }
        
        return [];
    } catch (Exception $e) {
        error_log('Error in AB Connect request: ' . $e->getMessage());
        throw $e;
    }
}

/**
 * Helper function to fetch paginated data from Learnosity API
 * @param object $dataApi Learnosity DataApi instance
 * @param string $uri API endpoint URI
 * @param array $securityPacket Security credentials
 * @param string $consumerSecret Consumer secret
 * @param string $mintime Minimum time to fetch data from
 * @param array $additionalParams Additional parameters for the API request
 * @return array Collected data
 */
function _fetchPaginatedData($dataApi, $uri, $securityPacket, $consumerSecret, $mintime, $additionalParams = [])
{
    $allData = [];
    $nextValue = '';
    $batchSize = 25;
    $maxRetries = 5;
    $baseDelay = 2;
    $maxDelay = 30;
    $rateLimitDelay = 60;
    
    while(true) {
        $request = array_merge([
            'mintime' => $mintime,
            'limit' => $batchSize
        ], $additionalParams);
        
        if ($nextValue !== '') {
            $request['next'] = $nextValue;
        }

        $retryCount = 0;
        while ($retryCount < $maxRetries) {
            try {
                $result = $dataApi->request(
                    $uri,
                    $securityPacket,
                    $consumerSecret,
                    $request,
                    'get'
                );

                $response = $result->json();
                
                // Check for API errors
                if (isset($response['error'])) {
                    throw new Exception('API Error: ' . json_encode($response['error']));
                }
                
                // Check if zero records
                if (isset($response['meta']['records']) && $response['meta']['records'] == 0) {
                    break 2;
                }

                // Add data to collection
                if (isset($response['data']) && is_array($response['data'])) {
                    $allData = array_merge($allData, $response['data']);
                }

                // Check for next page after processing current data
                if (isset($response['meta']['next'])) {
                    $nextValue = $response['meta']['next'];
                } else {
                    $nextValue = '';
                    break 2;
                }

                // Add a dynamic delay based on response size and rate
                $delay = min(500000, max(100000, count($response['data']) * 10000));
                usleep($delay);
                break;

            } catch (Exception $e) {
                $retryCount++;
                
                // Check if this is a rate limit error
                if (strpos($e->getMessage(), 'rate limit') !== false || 
                    strpos($e->getMessage(), '429') !== false) {
                    sleep($rateLimitDelay);
                    continue;
                }
                
                if ($retryCount >= $maxRetries) {
                    break 2;
                }
                
                // Calculate exponential backoff delay
                $delay = min($maxDelay, $baseDelay * pow(2, $retryCount - 1));
                sleep($delay);
            }
        }
    }
    
    return $allData;
}

/**
 * Fetch standards data from AB Connect API
 * @param array $guids Array of GUIDs to fetch
 * @param string $apiKey AB Connect API key
 * @param string $apiSecret AB Connect API secret
 * @return array Standards data
 */
function _fetchABStandards(array $guids, string $apiKey, string $apiSecret)
{
    $standardsData = [];
    $maxRetries = 5;
    $baseDelay = 2;
    $maxDelay = 30;
    $rateLimitDelay = 60;
    
    foreach ($guids as $guid) {
        $retryCount = 0;
        while ($retryCount < $maxRetries) {
            try {
                $response = _makeABConnectRequest($guid, $apiKey, $apiSecret);
                if (!empty($response)) {
                    $standardsData[] = $response;
                    break;
                }
                $retryCount++;
            } catch (Exception $e) {
                $retryCount++;
                
                // Check if this is a rate limit error
                if (strpos($e->getMessage(), 'rate limit') !== false || 
                    strpos($e->getMessage(), '429') !== false) {
                    sleep($rateLimitDelay);
                    continue;
                }
                
                if ($retryCount >= $maxRetries) {
                    error_log("Max retries reached for GUID $guid: " . $e->getMessage());
                    break;
                }
                
                // Calculate exponential backoff delay
                $delay = min($maxDelay, $baseDelay * pow(2, $retryCount - 1));
                sleep($delay);
            }
        }
    }
    
    return $standardsData;
}

/**
 * Main job function to process Academic Benchmarks data
 * @param object $db Database connection
 * @param string|null $siteId Site identifier (optional)
 * @param object $config Configuration object
 */
function runJob($db, $siteId = null, $config)
{
    $cronName = 'AcademicBenchmark';
    $status = CRON_SUCCESS;
    $details = 'Academic Benchmark job completed successfully';

    // Use a default siteId for logging if none provided
    $logSiteId = !empty($siteId) ? $siteId : 'global';
    $uuid = Logger::logCronStart($db, $logSiteId, $cronName);

    try {
        $storage = new StorageClient(['projectId' => 'abre-production']);
        $intermediateBucket = $storage->bucket('learnosity-standards-guid');
        $mainBucket = $storage->bucket('prd-landing-zone');
        $currentDate = date('Ymd');

        // Get Learnosity credentials from config
        $consumerKey = $config->learnosityFull->consumerKey;
        $consumerSecret = $config->learnosityFull->consumerSecret;
        $abApiKey = $config->learnosityFull->academicStandardsId;
        $abApiSecret = $config->learnosityFull->academicStandardsKey;
        
        $securityPacket = [
            'consumer_key' => $consumerKey,
            'domain'       => 'abre.io'
        ];
        
        $dataApi = new DataApi();
        
        // Always use 2018-01-01 for GCS operations
        $startDate = '2018-01-01';
        error_log("Starting data collection from {$startDate}");
        
        // Fetch Learnosity tags data
        $baseUrl = 'https://data.learnosity.com/v2023.1.LTS';
        $tagsUri = "{$baseUrl}/itembank/tagging/tags";
        
        $tagsData = _fetchPaginatedData(
            $dataApi,
            $tagsUri,
            $securityPacket,
            $consumerSecret,
            $startDate,
            ['reference_type' => 'academic_benchmarks']
        );
        
        $standardsData = [];
        
        if (!empty($tagsData)) {
            // Extract GUIDs from tags data
            $guids = _extractGuidsFromTags($tagsData);
            
            if (!empty($guids)) {
                // Upload GUIDs to intermediate bucket for tracking
                _uploadGuidsToGuidsBucket($guids, $intermediateBucket, $currentDate);
                
                // Fetch detailed standards data from AB Connect
                $standardsData = _fetchABStandards($guids, $abApiKey, $abApiSecret);
            }
        }
        
        // Upload final standards data to main bucket if we have data
        if (!empty($standardsData)) {
            if (!empty($siteId)) {
                _uploadToSiteGCS($standardsData, $mainBucket, $currentDate, $siteId);
            }
            _uploadToGlobalGCS($standardsData, $mainBucket, $currentDate);
            error_log("Final GCS upload completed successfully");
        }
        
        Logger::logCronFinish($db, $logSiteId, $cronName, $status, $details, $uuid);
    } catch (Exception $e) {
        $status = CRON_FAILURE;
        $details = $e->getMessage();
        Logger::logCronFinish($db, $logSiteId, $cronName, $status, $details, $uuid);
        throw $e;
    }
}
