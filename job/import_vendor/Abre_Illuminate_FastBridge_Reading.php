<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'Abre FastBridge Reading';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "[aReading]";

    //Define
    define("SAFE_COLUMN_COUNT", 57);
    define("MAX_IMPORT_LIMIT", 25);

    $error = null;
    $skip = null;
    $separator = "\r\n";
    $currentSchoolYearID = getCurrentSchoolYearID($db);

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if(!$cronFile){
      $cronFile = $sftp->get("$fileName.csv");
      $fileType = "csv";
      $columnSeparator = ",";
    }else{
      $cronFile = $sftp->get("$fileName.txt");
      $fileType = "txt";
      $columnSeparator = "\t";
    }

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO `abre_fastbridge_a_reading`
                  (`Assessment`,`Assessment_Language`,`State`,`District`,`School`,`Local_ID`,
                  `State_ID`,`FAST_ID`,`First_Name`,`Last_Name`,`Gender`,`DOB`,`Race`,`Special_Ed_Status`,
                  `Grade`,`Quarter_1_aReading_Test_Theta`,`Quarter_1_aReading_Percentile_at_School`,
                  `Quarter_1_aReading_Percentile_at_LEA`,`Quarter_1_aReading_Percentile_at_Nation`,
                  `Quarter_1_aReading_Risk_Level`,`Quarter_1_aReading_Final_Date`,`Quarter_2_aReading_Test_Theta`,
                  `Quarter_2_aReading_Percentile_at_School`,`Quarter_2_aReading_Percentile_at_LEA`,
                  `Quarter_2_aReading_Percentile_at_Nation`,`Quarter_2_aReading_Risk_Level`,
                  `Quarter_2_aReading_Final_Date`,`Growth_Score_from_Quarter_1_to_Quarter_2`,
                  `School_Growth_Percentile_from_Quarter_1_to_Quarter_2`,`District_Growth_Percentile_from_Quarter_1_to_Quarter_2`,
                  `National_Growth_Percentile_from_Quarter_1_to_Quarter_2`,`Growth_Percentile_by_Start_Score_from_Quarter_1_to_Quarter_2`,
                  `Quarter_3_aReading_Test_Theta`,`Quarter_3_aReading_Percentile_at_School`,
                  `Quarter_3_aReading_Percentile_at_LEA`,`Quarter_3_aReading_Percentile_at_Nation`,
                  `Quarter_3_aReading_Risk_Level`,`Quarter_3_aReading_Final_Date`,
                  `Growth_Score_from_Quarter_1_to_Quarter_3`,`School_Growth_Percentile_from_Quarter_1_to_Quarter_3`,
                  `District_Growth_Percentile_from_Quarter_1_to_Quarter_3`,`National_Growth_Percentile_from_Quarter_1_to_Quarter_3`,
                  `Growth_Percentile_by_Start_Score_from_Quarter_1_to_Quarter_3`,`Growth_Score_from_Quarter_2_to_Quarter_3`,
                  `School_Growth_Percentile_from_Quarter_2_to_Quarter_3`,`District_Growth_Percentile_from_Quarter_2_to_Quarter_3`,
                  `National_Growth_Percentile_from_Quarter_2_to_Quarter_3`,`Growth_Percentile_by_Start_Score_from_Quarter_2_to_Quarter_3`,
                  `Quarter_4_aReading_Test_Theta`,`Quarter_4_aReading_Percentile_at_School`,
                  `Quarter_4_aReading_Percentile_at_LEA`,`Quarter_4_aReading_Percentile_at_Nation`,
                  `Quarter_4_aReading_Risk_Level`,`Quarter_4_aReading_Final_Date`,`site_id`,`school_year_id`)
                  VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM abre_fastbridge_a_reading WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
  	do{

      //Split Columns
      if($fileType == "txt"){
        $data = str_getcsv($line, $columnSeparator);
      }else{
        $data = str_getcsv($line, $columnSeparator);
      }
  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$assessment = $data[0] ? trim($db->escape_string($data[0])) : "";
        $assessmentLanguage = $data[1] ? trim($db->escape_string($data[1])) : "";
        $state = $data[2] ? trim($db->escape_string($data[2])) : "";
        $district = $data[3] ? trim($db->escape_string($data[3])) : "";
        $school = $data[4] ? trim($db->escape_string($data[4])) : "";
        $localID = $data[5] ? trim($db->escape_string($data[5])) : "";
        $stateID = $data[6] ? trim($db->escape_string($data[6])) : "";
        $fastID = $data[7] ? trim($db->escape_string($data[7])) : "" ;
        $firstName = $data[8] ? trim($db->escape_string($data[8])) : "";
        $lastName = $data[9] ? trim($db->escape_string($data[9])) : "";
        $gender = $data[10] ? trim($db->escape_string($data[10])) : "";

        //format date type for insert
        if($data[11] !== null){
          $dobRaw = $data[11];
          $dateTimeObject = new DateTime($dobRaw, new DateTimeZone("UTC"));   
          $dobFormatted = $dateTimeObject->format("Y-m-d");
          $dob = "'".$dobFormatted."'";
        } else {
          $dob = "";
        }

        $race = $data[12] ? trim($db->escape_string($data[12])) : "";
        $specialEdStatus = $data[13] ? trim($db->escape_string($data[13])) : "";
        $grade = $data[14] ? trim($db->escape_string($data[14])) : "";
        
        //NOTE: $data[15] is an empty cell hence why it is skipped

        $q1TestTheta = is_numeric($data[16]) ? $data[16] : "NULL";
        $q1PercentileAtSchool = is_numeric($data[17]) ? $data[17] : "NULL";
        $q1PercentileAtLEA = is_numeric($data[18]) ? $data[18] : "NULL";
        $q1PercentileAtNation = is_numeric($data[19]) ? $data[19] : "NULL";
        $q1RiskLevel = $data[20] ? trim($db->escape_string($data[20])) : "NULL";

        //format date type for insert
        if($data[21]){
          $q1FinalDate = $data[21];
          $q1FinalDate = date('Y-m-d', strtotime($q1FinalDate));
          $q1FinalDate = "'".$q1FinalDate."'";
        } else {
          $q1FinalDate = "NULL";
        }

        $q2TestTheta = is_numeric($data[22]) ? $data[22] : "NULL";
        $q2PercentileAtSchool = is_numeric($data[23]) ? $data[23] : "NULL";
        $q2PercentileAtLEA = is_numeric($data[24]) ? $data[24] : "NULL";
        $q2PercentileAtNation = is_numeric($data[25]) ? $data[25] : "NULL";
        $q2RiskLevel = $data[26] ? $data[26] : "NULL";

        //format date type for insert
        if($data[27]){
          $q2FinalDate = $data[27];
          $q2FinalDate = date('Y-m-d', strtotime($q2FinalDate));
          $q2FinalDate = "'".$q2FinalDate."'";
        } else {
          $q2FinalDate = "NULL";
        }

        $growthScoreFromQ1ToQ2 = is_numeric($data[28]) ? $data[28] : "NULL";
        $schoolGrowthPercentileFromQ1ToQ2 = is_numeric($data[29]) ? $data[29] : "NULL";
        $districtGrowthPercentileFromQ1ToQ2 = is_numeric($data[30]) ? $data[30] : "NULL";
        $nationalGrowthPercentileFromQ1toQ2 = is_numeric($data[31]) ? $data[31] : "NULL";
        $growthPercentileByStartScoreFromQ1ToQ2 = is_numeric($data[32]) ? $data[32] : "NULL";

        $q3TestTheta = is_numeric($data[33]) ? $data[33] : "NULL";
        $q3PercentileAtSchool = is_numeric($data[34]) ? $data[34] : "NULL";
        $q3PercentileAtLEA = is_numeric($data[35]) ? $data[35] : "NULL";
        $q3PercentileAtNation = is_numeric($data[36]) ? $data[36] : "NULL";
        $q3RiskLevel = $data[37] ? trim($db->escape_string($data[37])) : "NULL";

        if($data[38]){
          $q3FinalDate = $data[38];
          $q3FinalDate = date('Y-m-d', strtotime($q3FinalDate));
          $q3FinalDate = "'".$q3FinalDate."'";
        } else {
          $q3FinalDate = "NULL";
        }
  
        $growthScoreFromQ1ToQ3 = is_numeric($data[39]) ? $data[39] : "NULL";
        $schoolGrowthPercentileFromQ1ToQ3 = is_numeric($data[40]) ? $data[40] : "NULL";
        $districtGrowthPercentileFromQ1ToQ3 = is_numeric($data[41]) ? $data[41] : "NULL";
        $nationalGrowthPercentileFromQ1toQ3 = is_numeric($data[42]) ? $data[42] : "NULL";
        $growthPercentileByStartScoreFromQ1ToQ3 = is_numeric($data[43]) ? $data[43] : "NULL";

        $growthScoreFromQ2ToQ3 = is_numeric($data[44]) ? $data[44] : "NULL";
        $schoolGrowthPercentileFromQ2ToQ3 = is_numeric($data[45]) ? $data[45] : "NULL";
        $districtGrowthPercentileFromQ2ToQ3 = is_numeric($data[46]) ? $data[46] : "NULL";
        $nationalGrowthPercentileFromQ2toQ3 = is_numeric($data[47]) ? $data[47] : "NULL";
        $growthPercentileByStartScoreFromQ2ToQ3 = is_numeric($data[48]) ? $data[48] : "NULL";
        
        $q4TestTheta = is_numeric($data[49]) ? $data[49] : "NULL";
        $q4PercentileAtSchool = is_numeric($data[50]) ? $data[50] : "NULL";
        $q4PercentileAtLEA = is_numeric($data[51]) ? $data[51] : "NULL";
        $q4PercentileAtNation = is_numeric($data[52]) ? $data[52] : "NULL";
        $q4RiskLevel = $data[53] ? trim($db->escape_string($data[53])) : "NULL";
        
        if($data[54]){
          $q4FinalDate = $data[54];
          $q4FinalDate = date('Y-m-d', strtotime($q4FinalDate));
          $q4FinalDate = "'".$q4FinalDate."'";
        } else {
          $q4FinalDate = "NULL";
        }

        $valuesToImport [] = "('$assessment', '$assessmentLanguage', '$state', '$district', '$school',
                            '$localID', '$stateID', '$fastID', '$firstName', '$lastName',  '$gender',
                              $dob, '$race', '$specialEdStatus', '$grade', $q1TestTheta, $q1PercentileAtSchool,
                              $q1PercentileAtLEA, $q1PercentileAtNation, '$q1RiskLevel', $q1FinalDate,  $q2TestTheta,
                              $q2PercentileAtSchool, $q2PercentileAtLEA, $q2PercentileAtNation,
                              '$q2RiskLevel', $q2FinalDate, $growthScoreFromQ1ToQ2, $schoolGrowthPercentileFromQ1ToQ2,
                              $districtGrowthPercentileFromQ1ToQ2, $nationalGrowthPercentileFromQ1toQ2,
                              $growthPercentileByStartScoreFromQ1ToQ2, $q3TestTheta, $q3PercentileAtSchool,
                              $q3PercentileAtLEA, $q3PercentileAtNation, '$q3RiskLevel', $q3FinalDate,
                              $growthScoreFromQ1ToQ3, $schoolGrowthPercentileFromQ1ToQ3, $districtGrowthPercentileFromQ1ToQ3,
                              $nationalGrowthPercentileFromQ1toQ3, $growthPercentileByStartScoreFromQ1ToQ3,
                              $growthScoreFromQ2ToQ3, $schoolGrowthPercentileFromQ2ToQ3, $districtGrowthPercentileFromQ2ToQ3,
                              $nationalGrowthPercentileFromQ2toQ3, $growthPercentileByStartScoreFromQ2ToQ3,
                              $q4TestTheta, $q4PercentileAtSchool, $q4PercentileAtLEA, $q4PercentileAtNation, '$q4RiskLevel',
                               $q4FinalDate, $siteID, $currentSchoolYearID)";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);

    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }
  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
