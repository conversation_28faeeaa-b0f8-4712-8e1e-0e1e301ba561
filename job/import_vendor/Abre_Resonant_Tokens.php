<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config){

  $cronName = 'Abre Resonant Tokens';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("SAFE_COLUMN_COUNT", 2);
    define("MAX_IMPORT_LIMIT", 25);

    $error = null;
    $skip = null;
    $separator = "\n";

  	$sftp = new SFTP($config->sftp->ip);
  	if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
  		throw new Exception("Login to SFTP failed.");
  	}

  	$cronFile = $sftp->get('data-exports/tokens.csv');

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, ",");
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO abre_resonant_tokens (
      survey, run, last_name, first_name, email, external_id, token, token_url, token_completed, completed_at, subject_last_name, subject_first_name, subject_external_id, survey_run_start, survey_run_end_date
     ) VALUES ";

    // Upload SFTP File to Google Cloud Storage Bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Resonant_Tokens.csv";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($cronFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Upload to the other folder location in landing zone bucket
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $folderName = pathinfo($fileName, PATHINFO_FILENAME);
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($cronFile, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

    $db->query("DELETE FROM abre_resonant_tokens WHERE email!='<EMAIL>' AND email!='<EMAIL>'");
  	do{
      $data = str_getcsv($line, ",");

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$survey = trim($db->escape_string($data[0]));
        $run = trim($db->escape_string($data[1]));
        $last_name = trim($db->escape_string($data[2]));
        $first_name = trim($db->escape_string($data[3]));
        $email = trim($db->escape_string($data[4]));
        $external_id = trim($db->escape_string($data[5]));
        $token = trim($db->escape_string($data[6]));
        $token_url = trim($db->escape_string($data[7]));
        $token_completed = trim($db->escape_string($data[8]));
        $completed_at = trim($db->escape_string($data[9]));
        $subject_last_name = trim($db->escape_string($data[10]));
        $subject_first_name = trim($db->escape_string($data[11]));
        $subject_external_id = trim($db->escape_string($data[12]));
        $survey_run_start = trim($db->escape_string($data[13]));
        $survey_run_end_date = trim($db->escape_string($data[14]));

        if($email!="<EMAIL>" && $email!="<EMAIL>")
        {
          $valuesToImport []= "('$survey', '$run', '$last_name', '$first_name', '$email', '$external_id', '$token', '$token_url', '$token_completed', '$completed_at', '$subject_last_name', '$subject_first_name', '$subject_external_id', '$survey_run_start', '$survey_run_end_date')";
        }

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
?>
