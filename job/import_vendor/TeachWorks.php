<?php

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use Google\Cloud\Storage\StorageClient;

// Private upload function
function _uploadToGCS($data, $bucket, $currentDate, $siteID) {
    try {
        if (empty($data)) {
            error_log("No data to upload");
            return;
        }

        // Ensure consistent data structure
        $formattedData = [];
        foreach ($data as $lesson) {
            if (isset($lesson['participants']) && is_array($lesson['participants'])) {
                foreach ($lesson['participants'] as $participant) {
                    $formattedData[] = [
                        'lesson_id' => intval($lesson['id']),
                        'lesson_name' => $lesson['name'] ?? '',
                        'series_id' => $lesson['series_id'] ?? '',
                        'location_name' => $lesson['location_name'] ?? '',
                        'location_id' => $lesson['location_id'] ?? '',
                        'employee_id' => $lesson['employee_id'] ?? '',
                        'employee_name' => $lesson['employee_name'] ?? '',
                        'date' => $lesson['from_date'] ?? null,
                        'time' => $lesson['from_time'] ?? null,
                        'lesson_status' => $lesson['status'] ?? '',
                        'student_name' => $participant['student_name'] ?? '',
                        'teachworks_student_id' => $participant['student_id'] ?? '',
                        'public_notes' => isset($participant['public_notes']) ? strip_tags($participant['public_notes']) : null,
                        'private_notes' => isset($participant['private_notes']) ? strip_tags($participant['private_notes']) : null,
                        'notes_sent_at' => $participant['notes_sent_at'] ?? null,
                        'site_id' => $siteID
                    ];
                }
            }
        }

        $jsonData = json_encode($formattedData);
        if ($jsonData === false) {
            error_log("Failed to encode data to JSON");
            return;
        }

        // Upload to site-id folder
        $fileName = "TeachWorks.json";
        $bucket->upload($jsonData, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);

        // Upload to filename folder
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        $folderName = pathinfo($fileName, PATHINFO_FILENAME);
        $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
        $bucket->upload($jsonData, [
            'name' => "$currentDate/filename/$folderName/$modifiedFile"
        ]);

        error_log("Successfully uploaded TeachWorks data to GCS");
    } catch (Exception $e) {
        error_log("Error uploading data to GCS: " . $e->getMessage());
    }
}

function runJob($db, $siteID, $config) {

    if ($db->connect_error) {
        die("Database connection failed: " . $db->connect_error);
    }

    $cronName = 'TeachWorks Import';
    $page = 1;
    $perPage = 50;
    $rowCounter = 0;

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        $apiToken = $config->teachworks->token;
        $baseUrl = 'https://api.teachworks.com/v1/lessons';

        // Initialize Google Cloud Storage
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $currentDate = date("Ymd");

        // Initialize array to hold data for bucket upload
        $allLessonsData = [];

        do {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "$baseUrl?page=$page&per_page=$perPage");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Token token=' . $apiToken
            ]);

            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                throw new Exception('cURL error: ' . curl_error($ch));
            }

            curl_close($ch);
            $data = json_decode($response, true);

            if (!is_array($data)) {
                error_log("Unexpected response format.");
                break;
            }

            $sql = "INSERT INTO `vendor_teachworks_participants` (
                        `lesson_id`, `lesson_name`, `series_id`, `location_name`, 
                        `location_id`, `employee_id`, `employee_name`, `date`, 
                        `time`, `lesson_status`, `student_name`, `teachworks_student_id`,
                        `public_notes`, `private_notes`, `notes_sent_at`, `site_id`
                    ) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE 
                        `lesson_name` = VALUES(`lesson_name`),
                        `series_id` = VALUES(`series_id`),
                        `location_name` = VALUES(`location_name`),
                        `location_id` = VALUES(`location_id`),
                        `employee_id` = VALUES(`employee_id`),
                        `employee_name` = VALUES(`employee_name`),
                        `date` = VALUES(`date`),
                        `time` = VALUES(`time`),
                        `lesson_status` = VALUES(`lesson_status`),
                        `student_name` = VALUES(`student_name`),
                        `teachworks_student_id` = VALUES(`teachworks_student_id`),
                        `public_notes` = VALUES(`public_notes`),
                        `private_notes` = VALUES(`private_notes`),
                        `notes_sent_at` = VALUES(`notes_sent_at`),
                        `site_id` = VALUES(`site_id`)";

            foreach ($data as $lesson) {
                if (isset($lesson['participants']) && is_array($lesson['participants'])) {
                    // Add lesson to collection
                    $allLessonsData[] = $lesson;
                    
                    foreach ($lesson['participants'] as $participant) {
                        $stmt = $db->prepare($sql);
                        if (!$stmt) {
                            throw new Exception("Statement preparation failed: " . $db->error);
                        }

                        $lesson_id = intval($lesson['id']);
                        $lesson_name = $lesson['name'] ?? '';
                        $series_id = $lesson['series_id'] ?? '';
                        $location_name = $lesson['location_name'] ?? '';
                        $location_id = $lesson['location_id'] ?? '';
                        $employee_id = $lesson['employee_id'] ?? '';
                        $employee_name = $lesson['employee_name'] ?? '';
                        $date = $lesson['from_date'] ?? null;
                        $time = $lesson['from_time'] ?? null;
                        $lesson_status = $lesson['status'] ?? '';
                        $student_name = $participant['student_name'] ?? '';
                        $teachworks_student_id = $participant['student_id'] ?? '';
                        $publicNotes = isset($participant['public_notes']) ? strip_tags($participant['public_notes']) : null;
                        $privateNotes = isset($participant['private_notes']) ? strip_tags($participant['private_notes']) : null;
                        $notesSentAt = $participant['notes_sent_at'] ?? null;

                        $stmt->bind_param(
                            "issssssssssssssi",
                            $lesson_id, $lesson_name, $series_id, $location_name,
                            $location_id, $employee_id, $employee_name, $date,
                            $time, $lesson_status, $student_name, $teachworks_student_id,
                            $publicNotes, $privateNotes, $notesSentAt, $siteID
                        );                    

                        if ($stmt->execute()) {
                            $rowCounter++;
                        } else {
                            error_log("Error executing statement: " . $stmt->error);
                        }

                        $stmt->close();
                    }
                } else {
                    error_log("No participants found.");
                }
            }

            $page++;
        } while (count($data) === $perPage);

        // Upload to GCS if we have data
        if (!empty($allLessonsData)) {
            _uploadToGCS($allLessonsData, $bucket, $currentDate, $siteID);
        }

        echo "Total records inserted/updated: $rowCounter\n";
    } catch (Exception $ex) {
        error_log("Error: " . $ex->getMessage());
    } finally {
        $details = ["rowsInserted" => $rowCounter];
        $status = CRON_SUCCESS;
        Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);

        $db->close();
    }
}