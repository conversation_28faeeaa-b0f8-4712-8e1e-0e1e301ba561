<?php

/*
* Copyright Abre.io Inc.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use phpseclib\Crypt\RSA;
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function processFile($sftp, $fileType, $db, $siteID, $currentDate, $storage, $config)
{
    $maxTimeHours = 4;
    $checkIntervalMinutes = 30;
    $startTime = time();
    $endTime = $startTime + ($maxTimeHours * 3600);
    $attempt = 1;
    
    while (time() < $endTime) {
        try {
            // Check SFTP connection
            if (!$sftp->isConnected()) {
                $sftp->disconnect();
                $sftp = new SFTP($config->rethinkeducation->ip);
                $key = new RSA();
                $key->loadKey(file_get_contents('/opt/cron-js/' . $config->rethinkeducation->keyPath));
                if (!$sftp->login($config->rethinkeducation->userName, $key)) {
                    throw new Exception('SFTP login failed');
                }
            }

            $directory = '/';
            $directories = $sftp->nlist($directory);
            $latestFile = null;
            $latestTime = 0;
            
            foreach ($directories as $file) {
                if (strpos($file, $fileType) === false) {
                    continue;
                }

                $fileInfo = $sftp->stat($file);
                if ($fileInfo && $fileInfo['mtime'] > $latestTime) {
                     $latestTime = $fileInfo['mtime'];
                     $latestFile = $file;
                }
            }
            
            if ($latestFile) {
                return $latestFile;
            }

            if (time() < $endTime) {
                sleep($checkIntervalMinutes * 60);
            }
            
        } catch (Exception $e) {
            if (time() < $endTime) {
                sleep($checkIntervalMinutes * 60);
            }
        }
        
        $attempt++;
    }
    
    throw new Exception('No ' . $fileType . ' found after ' . $maxTimeHours . ' hours of checking.');
}

function runJob($db, $siteID, $config)
{

    $cronName = 'Rethink Education Import';

    try {

        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        $currentDate = date("Ymd");

        define("SAFE_COLUMN_COUNT", 5);
        define("MAX_IMPORT_LIMIT", 1);
        $rowCounter = 0;

        $error = null;
        $skip = null;
        $separator = "\r\n";

        $sftp = new SFTP($config->rethinkeducation->ip);
        $key = new RSA();
        $key->loadKey(file_get_contents('/opt/cron-js/' . $config->rethinkeducation->keyPath));
        if (!$sftp->login($config->rethinkeducation->userName, $key)) {
            throw new Exception("Login to SFTP failed.");
        }

        // Initialize storage client
        $storage = new StorageClient([
            'projectId' => "abre-production"
        ]);

        // Get the latest student file with retries
        $latestStudentFile = processFile($sftp, 'StudentReport', $db, $siteID, $currentDate, $storage, $config);

        // Move student file to landing zone
        $cronFileStudent = $sftp->get($latestStudentFile);
        $file = "Rethink_Education_StudentReport.csv";
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($cronFileStudent, [
            'name' => "$currentDate/site-id/$siteID/$file"
        ]);
        $modifiedFile = $file . "-" . $siteID . ".csv";
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($cronFileStudent, [
            'name' => "$currentDate/filename/Rethink_Education_StudentReport/$modifiedFile"
        ]);

        //Process student file
        $cronFile = $sftp->get($latestStudentFile);
        $columnSeparator = ",";
        $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
        if ($fileDetails["isEmpty"]) {
            $skip = true;
            throw new Exception("File is empty.");
        } elseif ($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]) {
            $skip = true;
            throw new Exception("File only contains a header row.");
        } elseif (!$fileDetails["hasValidDataRow"]) {
            throw new Exception("No valid data row found.");
        }

        //Import student file
        $studentValuesToImport = [];
        $dbColumns = "INSERT IGNORE INTO vendor_rethink_education_student_report (
            user_activity_id, organization_name, student_id, student_first_name, student_last_name,
            domain, topic, videos_completed, interactives_completed, quizzes_completed,
            date_logged, site_id

            ) VALUES ";

        $line = strtok($cronFile, $separator);
        $line = strtok($separator);
        do {

            //Split Columns
            $data = str_getcsv($line, $columnSeparator);

            if (count($data) >= SAFE_COLUMN_COUNT) {
                $rowCounter++;

                $UserActivityId = trim($db->escape_string($data[0]));
                $OrganizationName = trim($db->escape_string($data[1]));
                $StudentId = trim($db->escape_string($data[2]));
                $StudentFirstName = trim($db->escape_string($data[3]));
                $StudentLastName = trim($db->escape_string($data[4]));
                $Domain = trim($db->escape_string($data[5]));
                $Topic = trim($db->escape_string($data[6]));
                $VideosCompleted = trim($db->escape_string($data[7]));
                $InteractivesCompleted = trim($db->escape_string($data[8]));
                $QuizzesCompleted = trim($db->escape_string($data[9]));
                $DateLogged = trim($db->escape_string($data[10]));

                $studentValuesToImport[] = "('$UserActivityId', '$OrganizationName', '$StudentId',
                                    '$StudentFirstName', '$StudentLastName', '$Domain', '$Topic',
                                    '$VideosCompleted', '$InteractivesCompleted', '$QuizzesCompleted',
                                    '$DateLogged', $siteID)";

                if (count($studentValuesToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $dbColumns, $studentValuesToImport);
                    $studentValuesToImport = [];
                }
            }

            $line = strtok($separator);
        } while (
            $line !== false
        );
        if (count($studentValuesToImport)) {
            insertRows($db, $dbColumns, $studentValuesToImport);
        }

        // Get the latest teacher file with retries
        $latestTeacherFile = processFile($sftp, 'TeacherReport', $db, $siteID, $currentDate, $storage, $config);

        // Move teacher file to Landing Zone
        $cronFileTeacher = $sftp->get($latestTeacherFile);
        $file = "Rethink_Education_TeacherReport.csv";
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($cronFileTeacher, [
            'name' => "$currentDate/site-id/$siteID/$file"
        ]);
        $modifiedFile = $file . "-" . $siteID . ".csv";
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($cronFileTeacher, [
            'name' => "$currentDate/filename/Rethink_Education_TeacherReport/$modifiedFile"
        ]);

        //Process teacher file
        $cronFile = $sftp->get($latestTeacherFile);
        $columnSeparator = ",";
        $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
        if ($fileDetails["isEmpty"]) {
            $skip = true;
            throw new Exception("File is empty.");
        } elseif ($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]) {
            $skip = true;
            throw new Exception("File only contains a header row.");
        } elseif (!$fileDetails["hasValidDataRow"]) {
            throw new Exception("No valid data row found.");
        }

        //Import teacher file
        $TeacherValuesToImport = [];
        $dbColumns = "INSERT IGNORE INTO vendor_rethink_education_teacher_report (
            user_activity_id, organization_name, assigned_buildings, teacher_id, member_first_name,
            member_last_name, domain, topic, viewed_activity_plan, viewed_lesson_plan,
            viewed_videos, viewed_materials, total_activities_present, total_learn_sections_presented,
            total_practive_sections_presented, total_dive_deeper_sections_presented, date_logged, site_id
            ) VALUES ";

        $line = strtok($cronFile, $separator);
        $line = strtok($separator);
        do {

            //Split Columns
            $data = str_getcsv(
                $line,
                $columnSeparator
            );

            if (count($data) >= SAFE_COLUMN_COUNT) {
                $rowCounter++;

                $UserActivityId = trim($db->escape_string($data[0]));
                $OrganizationName = trim($db->escape_string($data[1]));
                $AssignedBuildings = trim($db->escape_string($data[2]));
                $TeacherId = trim($db->escape_string($data[3]));
                $MemberFirstName = trim($db->escape_string($data[4]));
                $MemberLastName = trim($db->escape_string($data[5]));
                $Domain = trim($db->escape_string($data[6]));
                $Topic = trim($db->escape_string($data[7]));
                $ViewedActivityPlan = trim($db->escape_string($data[8]));
                $ViewedLessonPlan = trim($db->escape_string($data[9]));
                $ViewedVideos = trim($db->escape_string($data[10]));
                $ViewedMaterials = trim($db->escape_string($data[11]));
                $TotalActivitiesPresent = trim($db->escape_string($data[12]));
                $TotalLearnSectionsPresented = trim($db->escape_string($data[13]));
                $TotalPractiveSectionsPresented = trim($db->escape_string($data[14]));
                $TotalDiveDeeperSectionsPresented = trim($db->escape_string($data[15]));
                $DateLogged = trim($db->escape_string($data[16]));

                $TeacherValuesToImport[] = "('$UserActivityId', '$OrganizationName', '$AssignedBuildings',
                                    '$TeacherId', '$MemberFirstName', '$MemberLastName', '$Domain', '$Topic',
                                    '$ViewedActivityPlan', '$ViewedLessonPlan', '$ViewedVideos', '$ViewedMaterials', '$TotalActivitiesPresent',
                                    '$TotalLearnSectionsPresented', '$TotalPractiveSectionsPresented', '$TotalDiveDeeperSectionsPresented',
                                    '$DateLogged', $siteID)";

                if (count($TeacherValuesToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $dbColumns, $TeacherValuesToImport);
                    $TeacherValuesToImport = [];
                }
            }

            $line = strtok($separator);
        } while (
            $line !== false
        );
        if (count($TeacherValuesToImport)) {
            insertRows($db, $dbColumns, $TeacherValuesToImport);
        }
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {

        $details = [
            "rowsInserted" => $rowCounter
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
