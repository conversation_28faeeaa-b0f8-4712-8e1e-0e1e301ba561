<?php

use Google\Cloud\Storage\StorageClient;
use PHPExcel_IOFactory;

function importTerraNovaData($db, $siteID, $tempFile, $fileName) {
    // Convert XLS to CSV if needed
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if ($fileExtension === 'xls') {
        $csvTempFile = convertXLStoCSV($tempFile);
        if ($csvTempFile === false) {
            throw new Exception("Failed to convert XLS file to CSV");
        }
        $tempFile = $csvTempFile;
        // Update fileName to reflect CSV extension
        $fileName = pathinfo($fileName, PATHINFO_FILENAME) . '.csv';
    }

    // Import to Landing Zone
    try {
        $fileData = file_get_contents($tempFile);
        $currentDate = date("Ymd");
        $storage = new StorageClient(['projectId' => "abre-production"]);
        // Upload SFTP File to Google Cloud Storage Bucket
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($fileData, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);

        // Upload to the other folder location in landing zone bucket
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        $folderName = pathinfo($fileName, PATHINFO_FILENAME);
        $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($fileData, [
            'name' => "$currentDate/filename/$folderName/$modifiedFile"
        ]);
    }
    catch (Exception $ex) {
        // To DO: Catch and log exception
    }

    // Import to Database
    try {       
        $temporaryTableName = createTemporaryTable($db);
        loadDataIntoTemporaryTable($tempFile, $db, $temporaryTableName);
        insertTemporaryTable($siteID, $db, $temporaryTableName);
    } finally {
        if (isset($temporaryTableName)) {
            dropTemporaryTable($db, $temporaryTableName);
        }
    }
}

function convertXLStoCSV($xlsFile) {
    try {
        // Create temporary file for CSV output
        $csvFile = tempnam(sys_get_temp_dir(), 'csv_');
        
        // Load the XLS file
        $objReader = PHPExcel_IOFactory::createReaderForFile($xlsFile);
        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($xlsFile);
        
        // Get the first worksheet
        $worksheet = $objPHPExcel->getActiveSheet();
        
        // Open CSV file for writing
        $csvHandle = fopen($csvFile, 'w');
        if ($csvHandle === false) {
            throw new Exception("Could not open CSV file for writing");
        }
        
        // Iterate through rows and write to CSV
        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $rowData = array();
            foreach ($cellIterator as $cell) {
                $rowData[] = $cell->getValue();
            }
            
            fputcsv($csvHandle, $rowData);
        }
        
        // Clean up
        fclose($csvHandle);
        $objPHPExcel->disconnectWorksheets();
        unset($objPHPExcel);
        
        return $csvFile;
    } catch (Exception $e) {
        if (isset($csvHandle) && is_resource($csvHandle)) {
            fclose($csvHandle);
        }
        if (isset($csvFile) && file_exists($csvFile)) {
            unlink($csvFile);
        }
        throw new Exception("Excel conversion failed: " . $e->getMessage());
    }
}

  function createTemporaryTable($db) {
    $tableCreated = false;
    $tries = 0;
    while (!$tableCreated && $tries < 3) {
      $tries++;
      $temporaryTableName = "temp" . uniqid();
      $createTempTableSQL =  "CREATE TABLE {$temporaryTableName} (
        `state_student_id` varchar(16) NOT NULL,
        `test_date` date DEFAULT NULL,
        `reading_national_percentile` tinyint(4) DEFAULT NULL,
        `language_national_percentile` tinyint(4) DEFAULT NULL,
        `math_national_percentile` tinyint(4) DEFAULT NULL,
        `science_national_percentile` tinyint(4) DEFAULT NULL,
        `social_studies_national_percentile` tinyint(4) DEFAULT NULL,
        `total_score_national_percentile` tinyint(4) DEFAULT NULL,
        `reading_national_stanine` tinyint(4) DEFAULT NULL,
        `language_national_stanine` tinyint(4) DEFAULT NULL,
        `math_national_stanine` tinyint(4) DEFAULT NULL,
        `science_national_stanine` tinyint(4) DEFAULT NULL,
        `social_studies_national_stanine` tinyint(4) DEFAULT NULL,
        `total_score_national_stanine` tinyint(4) DEFAULT NULL,
        `reading_scale_score` smallint(6) DEFAULT NULL,
        `language_scale_score` smallint(6) DEFAULT NULL,
        `math_scale_score` smallint(6) DEFAULT NULL,
        `science_scale_score` smallint(6) DEFAULT NULL,
        `social_studies_scale_score` smallint(6) DEFAULT NULL,
        `total_score_scale_score` smallint(6) DEFAULT NULL,
        `total_score_grade_equivalent` varchar(4) DEFAULT NULL,
        `reading_grade_equivalent` varchar(4) DEFAULT NULL,
        `math_grade_equivalent` varchar(4) DEFAULT NULL,
        `language_grade_equivalent` varchar(4) DEFAULT NULL,
        `science_grade_equivalent` varchar(4) DEFAULT NULL,
        `social_studies_grade_equivalent` varchar(4) DEFAULT NULL,
        `reading_anticipated_achievement_national_percentile` tinyint(4) DEFAULT NULL,
        `language_anticipated_achievement_national_percentile` tinyint(4) DEFAULT NULL,
        `math_anticipated_achievement_national_percentile` tinyint(4) DEFAULT NULL,
        `science_anticipated_achievement_national_percentile` tinyint(4) DEFAULT NULL,
        `social_studies_anticipated_achievement_national_percentile` tinyint(4) DEFAULT NULL,
        `total_score_anticipated_achievement_national_percentile` tinyint(4) DEFAULT NULL,
        `cognitive_skills_index` int(11) DEFAULT NULL,
        `cognitive_skills_index_upper_bound` int(11) DEFAULT NULL,
        `cognitive_skills_index_lower_bound` int(11) DEFAULT NULL,
        `lexile_score` smallint(6) DEFAULT NULL
      )";

      $stmt = $db->stmt_init();
      $stmt->prepare($createTempTableSQL);
      $tableCreated = $stmt->execute();
      $stmt->close();
    }
    return $temporaryTableName;
  }

  function buildInsertLine($stateStudentID, $line) {
    if (trim($line[24]) == "") {
      $testDate = "NULL";
    } else {
      $testDate = str_pad(trim($line[24]), 6, "0", STR_PAD_LEFT);
      $month = substr($testDate, 0, 2);
      $day = substr($testDate, 2, 2);
      $year = substr($testDate, 4, 2);
      $testDate = "20{$year}-{$month}-{$day}";
    }

    $readingCombined = explode(" ", trim($line[302]));
    if (count($readingCombined) == 2) {
      $readingNationalPercentile = $readingCombined[1];
      $readingNationalStanine = $readingCombined[0];
    } else {
      $readingNationalPercentile = "NULL";
      $readingNationalStanine = "NULL";
    }

    $languageCombined = explode(" ", trim($line[305]));
    if (count($languageCombined) == 2) {
      $languageNationalPercentile = $languageCombined[1];
      $languageNationalStanine = $languageCombined[0];
    } else {
      $languageNationalPercentile = "NULL";
      $languageNationalStanine = "NULL";
    }

    $mathCombined = explode(" ", trim($line[308]));
    if (count($mathCombined) == 2) {
      $mathNationalPercentile = $mathCombined[1];
      $mathNationalStanine = $mathCombined[0];
    } else {
      $mathNationalPercentile = "NULL";
      $mathNationalStanine = "NULL";
    }

    $scienceCombined = explode(" ", trim($line[312]));
    if (count($scienceCombined) == 2) {
      $scienceNationalPercentile = $scienceCombined[1];
      $scienceNationalStanine = $scienceCombined[0];
    } else {
      $scienceNationalPercentile = "NULL";
      $scienceNationalStanine = "NULL";
    }

    $socialStudiesCombined = explode(" ", trim($line[313]));
    if (count($socialStudiesCombined) == 2) {
      $socialStudiesNationalPercentile = $socialStudiesCombined[1];
      $socialStudiesNationalStanine = $socialStudiesCombined[0];
    } else {
      $socialStudiesNationalPercentile = "NULL";
      $socialStudiesNationalStanine = "NULL";
    }

    $totalScoreCombined = explode(" ", trim($line[311]));
    if (count($totalScoreCombined) == 2) {
      $totalScoreNationalPercentile = $totalScoreCombined[1];
      $totalScoreNationalStanine = $totalScoreCombined[0];
    } else {
      $totalScoreNationalPercentile = "NULL";
      $totalScoreNationalStanine = "NULL";
    }

    $readingScaleScore = nullCheck($line[232]);
    $languageScaleScore = nullCheck($line[235]);
    $mathScaleScore = nullCheck($line[238]);
    $scienceScaleScore = nullCheck($line[242]);
    $socialStudiesScaleScore = nullCheck($line[243]);
    $totalScoreScaleScore = nullCheck($line[241]);

    $readingGradeEquivalent = nullCheckWithQuotes($line[246]);
    $languageGradeEquivalent = nullCheckWithQuotes($line[249]);
    $mathGradeEquivalent = nullCheckWithQuotes($line[252]);
    $scienceGradeEquivalent = nullCheckWithQuotes($line[256]);
    $socialStudiesGradeEquivalent = nullCheckWithQuotes($line[257]);
    $totalScoreGradeEquivalent = nullCheckWithQuotes($line[255]);

    $readingAACombined = explode(" ", trim($line[540]));
    if (count($readingAACombined) == 2) {
      $readingAANationalPercentile = $readingAACombined[1];
    } else {
      $readingAANationalPercentile = "NULL";
    }
    $languageAACombined = explode(" ", trim($line[543]));
    if (count($languageAACombined) == 2) {
      $languageAANationalPercentile = $languageAACombined[1];
    } else {
      $languageAANationalPercentile = "NULL";
    }
    $mathAACombined = explode(" ", trim($line[546]));
    if (count($mathAACombined) == 2) {
      $mathAANationalPercentile = $mathAACombined[1];
    } else {
      $mathAANationalPercentile = "NULL";
    }
    $scienceAACombined = explode(" ", trim($line[550]));
    if (count($scienceAACombined) == 2) {
      $scienceAANationalPercentile = $scienceAACombined[1];
    } else {
      $scienceAANationalPercentile = "NULL";
    }
    $socialStudiesAACombined = explode(" ", trim($line[551]));
    if (count($socialStudiesAACombined) == 2) {
      $socialStudiesAANationalPercentile = $socialStudiesAACombined[1];
    } else {
      $socialStudiesAANationalPercentile = "NULL";
    }
    $totalScoreAACombined = explode(" ", trim($line[549]));
    if (count($totalScoreAACombined) == 2) {
      $totalScoreAANationalPercentile = $totalScoreAACombined[1];
    } else {
      $totalScoreAANationalPercentile = "NULL";
    }

    $cognitiveSkillsIndex = nullCheck($line[568]);
    $cognitiveSkillsIndexUpper = nullCheck($line[569]);
    $cognitiveSkillsIndexLower = nullCheck($line[270]);
    $lexileScore = nullCheck($line[490]);

    $sql = "
      (
        '$stateStudentID',
        '$testDate',

        $readingNationalPercentile,
        $languageNationalPercentile,
        $mathNationalPercentile,
        $scienceNationalPercentile,
        $socialStudiesNationalPercentile,
        $totalScoreNationalPercentile,

        $readingNationalStanine,
        $languageNationalStanine,
        $mathNationalStanine ,
        $scienceNationalStanine,
        $socialStudiesNationalStanine,
        $totalScoreNationalStanine,

        $readingScaleScore,
        $languageScaleScore,
        $mathScaleScore,
        $scienceScaleScore,
        $socialStudiesScaleScore,
        $totalScoreScaleScore,

        $readingGradeEquivalent,
        $languageGradeEquivalent,
        $mathGradeEquivalent,
        $scienceGradeEquivalent,
        $socialStudiesGradeEquivalent,
        $totalScoreGradeEquivalent,

        $readingAANationalPercentile,
        $languageAANationalPercentile,
        $mathAANationalPercentile,
        $scienceAANationalPercentile,
        $socialStudiesAANationalPercentile,
        $totalScoreAANationalPercentile,

        $cognitiveSkillsIndex,
        $cognitiveSkillsIndexUpper,
        $cognitiveSkillsIndexLower,
        $lexileScore
      )";
    return $sql;
  }

  function loadDataIntoTemporaryTable($fileName, $db, $temporaryTableName) {
    $temporaryTableInsertSQL = "
      INSERT INTO {$temporaryTableName}
      (
        state_student_id,
        test_date,
        reading_national_percentile,
        language_national_percentile,
        math_national_percentile,
        science_national_percentile,
        social_studies_national_percentile,
        total_score_national_percentile,

        reading_national_stanine,
        language_national_stanine,
        math_national_stanine,
        science_national_stanine,
        social_studies_national_stanine,
        total_score_national_stanine,

        reading_scale_score,
        language_scale_score,
        math_scale_score,
        science_scale_score,
        social_studies_scale_score,
        total_score_scale_score,

        reading_grade_equivalent,
        language_grade_equivalent,
        math_grade_equivalent,
        science_grade_equivalent,
        social_studies_grade_equivalent,
        total_score_grade_equivalent,

        reading_anticipated_achievement_national_percentile,
        language_anticipated_achievement_national_percentile,
        math_anticipated_achievement_national_percentile,
        science_anticipated_achievement_national_percentile,
        social_studies_anticipated_achievement_national_percentile,
        total_score_anticipated_achievement_national_percentile,

        cognitive_skills_index,
        cognitive_skills_index_upper_bound,
        cognitive_skills_index_lower_bound,
        lexile_score
      )
      VALUES";

    $handle = fopen($fileName, "r");

    if ($handle === false) {
      throw new Exception("Cannot read CSV file");
    }
    if (fgetcsv($handle) === false) { // header line
      throw new Exception("file is empty");
    }

    //concatenate all records
    $records = array();
    while (($line = fgetcsv($handle)) !== false) {
      $stateStudentID = trim($line[26]);
      if($stateStudentID != ""){
        array_push($records, buildInsertLine($stateStudentID, $line));
      }
    }
    $temporaryTableInsertSQL .= implode(",", $records) . ";";

    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }

  function insertTemporaryTable($siteID, $db, $temporaryTableName) {
    $replaceIntoSQL = "
      REPLACE INTO abre_terranova (
        site_id,
        state_student_id,
        test_date,
        reading_national_percentile,
        language_national_percentile,
        math_national_percentile,
        science_national_percentile,
        social_studies_national_percentile,
        total_score_national_percentile,

        reading_national_stanine,
        language_national_stanine,
        math_national_stanine,
        science_national_stanine,
        social_studies_national_stanine,
        total_score_national_stanine,

        reading_scale_score,
        language_scale_score,
        math_scale_score,
        science_scale_score,
        social_studies_scale_score,
        total_score_scale_score,

        reading_grade_equivalent,
        language_grade_equivalent,
        math_grade_equivalent,
        science_grade_equivalent,
        social_studies_grade_equivalent,
        total_score_grade_equivalent,

        reading_anticipated_achievement_national_percentile,
        language_anticipated_achievement_national_percentile,
        math_anticipated_achievement_national_percentile,
        science_anticipated_achievement_national_percentile,
        social_studies_anticipated_achievement_national_percentile,
        total_score_anticipated_achievement_national_percentile,

        cognitive_skills_index,
        cognitive_skills_index_upper_bound,
        cognitive_skills_index_lower_bound,
        lexile_score
      )
      SELECT
        ?,
        state_student_id,
        test_date,
        reading_national_percentile,
        language_national_percentile,
        math_national_percentile,
        science_national_percentile,
        social_studies_national_percentile,
        total_score_national_percentile,

        reading_national_stanine,
        language_national_stanine,
        math_national_stanine,
        science_national_stanine,
        social_studies_national_stanine,
        total_score_national_stanine,

        reading_scale_score,
        language_scale_score,
        math_scale_score,
        science_scale_score,
        social_studies_scale_score,
        total_score_scale_score,

        reading_grade_equivalent,
        language_grade_equivalent,
        math_grade_equivalent,
        science_grade_equivalent,
        social_studies_grade_equivalent,
        total_score_grade_equivalent,

        reading_anticipated_achievement_national_percentile,
        language_anticipated_achievement_national_percentile,
        math_anticipated_achievement_national_percentile,
        science_anticipated_achievement_national_percentile,
        social_studies_anticipated_achievement_national_percentile,
        total_score_anticipated_achievement_national_percentile,

        cognitive_skills_index,
        cognitive_skills_index_upper_bound,
        cognitive_skills_index_lower_bound,
        lexile_score
      FROM {$temporaryTableName}
    ";
    $stmt = $db->stmt_init();
    $stmt->prepare($replaceIntoSQL);
    $stmt->bind_param('i', $siteID);
    $stmt->execute();
    $stmt->close();
  }

  function dropTemporaryTable($db, $temporaryTableName) {
    $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
    $stmt = $db->stmt_init();
    $stmt->prepare($dropTemporaryTableSQL);
    $stmt->execute();
    $stmt->close();
  }
 ?>
