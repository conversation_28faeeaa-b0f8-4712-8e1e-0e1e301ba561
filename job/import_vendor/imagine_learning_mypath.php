<?php

/*
* Copyright Abre.io Inc.
*/

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use phpseclib\Crypt\RSA;
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

class ImagineLearningMyPathImporter
{
    const SAFE_COLUMN_COUNT = 10;
    const MAX_IMPORT_LIMIT = 25;
    const MAX_DELETE_ATTEMPTS = 3;
    const DELETE_BATCH_SIZE = 500;
    const MAX_DELETE_BATCHES = 5000;
    const MAX_LOCK_RETRIES = 5;
    const MAX_TOTAL_LOCK_TIME = 1800;

    private $db;
    private $siteId;
    private $config;
    private $sftp;
    private $storage;

    private $cronName = 'Imagine Learning MyPath';
    private $fileName = 'Wkly_CA_PerrisElementarySD_IMP_Assessment_';
    private $separator = "\r\n";
    private $columnSeparator = ',';
    private $currentSchoolYearId;
    private $uuid;
    private $error = null;
    private $skip = null;

    /**
     * Constructor
     * @param object $db Database connection
     * @param string $siteId
     * @param object $config Configuration object
     * @param StorageClient $storage Google Cloud Storage client instance
     */
    public function __construct($db, $siteId, $config, StorageClient $storage)
    {
        $this->db = $db;
        $this->siteId = $siteId;
        $this->config = $config;
        $this->currentSchoolYearId = getCurrentSchoolYearID($this->db);
        $this->storage = $storage;
    }

    /**
     * Run the import job
     */
    public function run()
    {
        $rowCount = 0;
        try {
            $this->_logStart();
            $this->_connectSftp();
            $fileContent = $this->_findAndDownloadFile();
            $this->_validateFileContent($fileContent);
            $this->_uploadToGCS($fileContent);
            // Capture the row count from processing
            $rowCount = $this->_processData($fileContent);
        } catch (Exception $ex) {
            $this->error = $ex->getMessage();
        } finally {
            // Always log finish from here, passing the final row count
            $this->_logFinish($rowCount);
        }
    }

    /**
     * Log the start of the cron job
     */
    private function _logStart()
    {
        $this->uuid = Logger::logCronStart($this->db, $this->siteId, $this->cronName);
    }

    /**
     * Log the end of the cron job
     * @param int $rowCount Number of rows inserted
     */
    private function _logFinish($rowCount = 0)
    {
        $details = [];
        if (!is_null($this->error)) {
            $details['error'] = $this->error;
            $status = !is_null($this->skip) && $this->skip ? CRON_NOT_RUN : CRON_FAILURE;
        } else {
            $details = ['rowsInserted' => $rowCount];
            $status = CRON_SUCCESS;
        }
        Logger::logCronFinish($this->db, $this->siteId, $this->cronName, $status, $details, $this->uuid);
    }

    /**
     * Connect to the SFTP server
     * @throws Exception If connection or login fails
     */
    private function _connectSftp()
    {
        $this->sftp = new SFTP($this->config->imaginelearning->host);
        $key = new RSA();
        $keyPath = '/opt/cron-js/' . $this->config->imaginelearning->keyPath;
        if (!file_exists($keyPath)) {
            throw new Exception('SFTP key file not found: ' . $keyPath);
        }
        $key->loadKey(file_get_contents($keyPath));
        if (!$this->sftp->login($this->config->imaginelearning->userName, $key)) {
            throw new Exception('Login to SFTP failed.');
        }
    }

    /**
     * Find the latest file on SFTP and download its content
     * @return string File content
     * @throws Exception If no file is found or content cannot be retrieved
     */
    private function _findAndDownloadFile()
    {
        $directory = $this->config->imaginelearning->directory ?? '/';
        $files = $this->sftp->nlist($directory);

        if ($files === false) {
             throw new Exception('Failed to list files in SFTP directory: ' . $directory);
        }

        $prefix = $this->fileName;
        $filteredFiles = array_filter($files, function ($file) use ($prefix) {
            return $file !== '.' && $file !== '..' && strpos($file, $prefix) === 0;
        });

        rsort($filteredFiles);

        if (empty($filteredFiles)) {
            $this->skip = true;
            throw new Exception('No file found matching pattern: ' . $this->fileName);
        }

        $latestFile = reset($filteredFiles);
        $filePath = rtrim($directory, '/') . '/' . $latestFile;
        $cronFile = $this->sftp->get($filePath);

        if ($cronFile === false) {
            $this->skip = true;
            throw new Exception('Could not retrieve file content from: ' . $filePath);
        }
        return $cronFile;
    }

    /**
     * Validate the structure and content of the downloaded file
     * @param string $fileContent
     * @throws Exception If file is empty, has only headers, or no valid data rows
     */
    private function _validateFileContent($fileContent)
    {
        $fileDetails = getFileStructure($fileContent, $this->separator, self::SAFE_COLUMN_COUNT, $this->columnSeparator);
        if ($fileDetails['isEmpty']) {
            $this->skip = true;
            throw new Exception('File is empty.');
        } elseif ($fileDetails['hasHeaderRow'] && !$fileDetails['hasDataRow']) {
            $this->skip = true;
            throw new Exception('File only contains a header row.');
        } elseif (!$fileDetails['hasValidDataRow']) {
            throw new Exception('No valid data row found.');
        }
    }

    /**
     * Upload file content to Google Cloud Storage
     * @param string $fileContent
     */
    private function _uploadToGCS($fileContent)
    {
        $currentDate = date('Ymd');
        $bucketName = 'prd-landing-zone';
        $bucket = $this->storage->bucket($bucketName);

        // Upload to site-id directory
        $gcsPathSite = "$currentDate/site-id/{$this->siteId}/{$this->fileName}.csv";
        $bucket->upload($fileContent, ['name' => $gcsPathSite]);

        // Upload to filename directory
        $folderName = $this->fileName;
        $modifiedFile = "{$folderName}-{$this->siteId}.csv";
        $gcsPathFilename = "$currentDate/filename/{$folderName}/{$modifiedFile}";
        $bucket->upload($fileContent, ['name' => $gcsPathFilename]);
    }

    /**
     * Process the data: delete existing records and insert new ones
     * @param string $fileContent
     * @throws Exception If deletion fails
     */
    private function _processData($fileContent)
    {
        $this->_deleteExistingData();

        $rowCounter = 0;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO vendor_imagine_learning_mypath (
            district_name, school_name, student_name, external_id, 
            numerical_grade, assessment_subject, assessment_name, 
            test_type, assessment_started_date, score_scaled, 
            site_id, school_year_id
        ) VALUES ";

        $line = strtok($fileContent, $this->separator);
        $line = strtok($this->separator);

        do {
            $data = str_getcsv($line, $this->columnSeparator);

            if (count($data) >= self::SAFE_COLUMN_COUNT) {
                $rowCounter++;
                $valuesToImport[] = $this->_prepareInsertValues($data);

                if (count($valuesToImport) == self::MAX_IMPORT_LIMIT) {
                    $this->_insertBatch($dbColumns, $valuesToImport);
                    $valuesToImport = [];
                }
            }
            $line = strtok($this->separator);
        } while ($line !== false);

        if (!empty($valuesToImport)) {
            $this->_insertBatch($dbColumns, $valuesToImport);
        }
        
        return $rowCounter;
    }

    /**
     * Prepare a single row's values for SQL insertion
     * @param array $data Row data from CSV
     * @return string Formatted values string for INSERT
     */
    private function _prepareInsertValues(array $data)
    {
        $district_name = trim($this->db->escape_string($data[0]));
        $school_name = trim($this->db->escape_string($data[1]));
        $student_name = trim($this->db->escape_string($data[2]));
        $external_id = trim($this->db->escape_string($data[3]));
        $numerical_grade = trim($this->db->escape_string($data[4]));
        $assessment_subject = trim($this->db->escape_string($data[5]));
        $assessment_name = trim($this->db->escape_string($data[6]));
        $test_type = trim($this->db->escape_string($data[7]));
        $assessment_started_date = trim($this->db->escape_string($data[8]));
        $score_scaled = !empty($data[9]) ? intval($data[9]) : 'NULL';
        $score_value = ($score_scaled === 'NULL') ? 'NULL' : "'$score_scaled'";

        return "(
            '$district_name', '$school_name', '$student_name', '$external_id',
            '$numerical_grade', '$assessment_subject', '$assessment_name',
            '$test_type', '$assessment_started_date', $score_value,
            {$this->siteId}, {$this->currentSchoolYearId}
        )";
    }

    /**
     * Insert a batch of rows into the database
     * @param string $dbColumns INSERT INTO statement prefix
     * @param array $valuesToImport Array of value strings
     */
    private function _insertBatch($dbColumns, array $valuesToImport)
    {
        insertRows($this->db, $dbColumns, $valuesToImport);
    }

    /**
     * Delete existing data with retry logic
     * @throws Exception If deletion fails after multiple attempts
     */
    private function _deleteExistingData()
    {
        for ($attempt = 1; $attempt <= self::MAX_DELETE_ATTEMPTS; $attempt++) {
            $this->_attemptToDeleteExistingData();
            if ($this->_checkIfDataDeleted()) {
                return true;
            }
            if ($attempt < self::MAX_DELETE_ATTEMPTS) {
                sleep(2);
            }
        }
        throw new Exception('Could not delete existing data after ' . self::MAX_DELETE_ATTEMPTS . ' attempts.');
    }

    /**
     * Attempt to delete existing data in batches with lock handling
     * @throws Exception On database error or timeout
     */
    private function _attemptToDeleteExistingData()
    {
        $batchCount = 0;
        // Force 1 row to enter the loop
        $deletedRows = 1;
        $totalLockTime = 0;

        do {
            $lockRetries = 0;
            $success = false;
            
            while (!$success && $lockRetries < self::MAX_LOCK_RETRIES && $totalLockTime < self::MAX_TOTAL_LOCK_TIME) {
                try {
                     // Small delay between batches
                    if ($batchCount > 0) usleep(100000);
                    
                    $query = "DELETE FROM vendor_imagine_learning_mypath WHERE site_id = ? AND school_year_id = ? LIMIT ?";
                    $stmt = $this->db->prepare($query);
                    $limit = self::DELETE_BATCH_SIZE;
                    $stmt->bind_param('iii', $this->siteId, $this->currentSchoolYearId, $limit);
                    $result = $stmt->execute();
                    
                    if ($result) {
                        $success = true;
                        $deletedRows = $stmt->affected_rows;
                        $stmt->close();
                    } else {
                        if (strpos($this->db->error, 'Lock wait timeout exceeded') !== false || 
                            strpos($this->db->error, 'Deadlock found') !== false) {
                            $lockRetries++;
                            $delayMinutes = $this->_calculateBackoffDelay($lockRetries);
                            $delaySeconds = $delayMinutes * 60;
                            $totalLockTime += $delaySeconds;
                            
                            if ($lockRetries < self::MAX_LOCK_RETRIES && $totalLockTime < self::MAX_TOTAL_LOCK_TIME) {
                                error_log("Lock detected on delete batch {$batchCount}, attempt {$lockRetries}. Waiting {$delayMinutes} minutes. Total lock time: " . ($totalLockTime / 60) . " minutes");
                                sleep($delaySeconds);
                                 // Retry the same batch
                                continue;
                            }
                        }
                        throw new Exception('Error deleting batch: ' . $this->db->error);
                    }
                } catch (Exception $e) {
                    if ($lockRetries >= self::MAX_LOCK_RETRIES || $totalLockTime >= self::MAX_TOTAL_LOCK_TIME) {
                        throw new Exception("Failed to delete batch after {$lockRetries} attempts or " . ($totalLockTime / 60) . " minutes of lock time: " . $e->getMessage());
                    }
                    $lockRetries++;
                    $delayMinutes = $this->_calculateBackoffDelay($lockRetries);
                    $delaySeconds = $delayMinutes * 60;
                    $totalLockTime += $delaySeconds;
                    sleep($delaySeconds);
                    // The loop will retry
                }
            }
            
            if (!$success) {
                throw new Exception('Failed to delete batch after exhausting all retry attempts');
            }
            
            $batchCount++;
        } while ($deletedRows > 0 && $batchCount < self::MAX_DELETE_BATCHES);

        if ($batchCount >= self::MAX_DELETE_BATCHES && $deletedRows > 0) {
            error_log("Warning: Delete operation reached max batches (" . self::MAX_DELETE_BATCHES . ") but data might still remain.");
        }
    }

    /**
     * Check if all data for the site and school year has been deleted
     * @return bool True if no rows exist
     */
    private function _checkIfDataDeleted()
    {
        $query = "SELECT COUNT(*) FROM vendor_imagine_learning_mypath WHERE site_id = ? AND school_year_id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->bind_param('ii', $this->siteId, $this->currentSchoolYearId);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_row()[0];
        $stmt->close();
        return $count == 0;
    }

    /**
     * Calculate backoff delay in minutes
     * @param int
     * @return int
     */
    private function _calculateBackoffDelay($retryAttempt)
    {
        if ($retryAttempt <= 0) return 1;
        $delay = min(15, pow(2, $retryAttempt - 1));
         // Exponential backoff: 1, 2, 4, 8, 15
         // Force at least 1 minute
        return max(1, intval($delay));
    }
}

// Main execution: run the importer via the runJob function
function runJob($db, $siteId, $config)
{
    try {
        // Create the StorageClient instance first
        $storageClient = new StorageClient(['projectId' => 'abre-production']);

        // Inject the StorageClient into the importer
        $importer = new ImagineLearningMyPathImporter($db, $siteId, $config, $storageClient);
        $importer->run();
    } catch (Exception $e) {
        // Log the error if instantiation or the run fails
        error_log("Fatal error running Imagine Learning MyPath importer for site {$siteId}: " . $e->getMessage());
        // Optionally, re-throw or handle as needed by runner
        // throw $e;
    }
}
