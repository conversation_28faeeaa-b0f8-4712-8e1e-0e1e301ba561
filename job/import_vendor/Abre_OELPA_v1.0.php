<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;
use DateTime;

function runJob($db, $siteID, $config){

  $cronName = 'OELPA Import v1.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("MAX_IMPORT_LIMIT", 100);

    $error = null;
    $skip = null;
    
    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-oelpa');
    $isFileLoaded = $sftp->get('Abre_OELPA_v1.0.csv', $localTempFile);

    if($isFileLoaded){
      importOELPAData($db, $siteID, $localTempFile);
    }else{
      $skip = true;
      throw new Exception("No file found.");
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);

}


function importOELPAData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE Abre_OELPA";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName){
  $temporaryTableColumnsSQL = "
    INSERT INTO {$temporaryTableName}
    (
      administrative_date,
      comparison_date,
      studentID,
      enrolled_grade,
      lep,
      gifted,
      504,
      iep,
      ela_accommodations,
      mathematics_accommodations,
      socialstudies_accommodations,
      science_accommodations,
      attending_school,
      school_irn,
      test_name,
      overall_score,
      overall_standard_error,
      proficiency_status,
      comprehension_score,
      comprehension_standard_error,
      comprehension_flag,
      listening_score,
      listening_standard_error,
      listening_level,
      listening_flag,
      reading_score,
      reading_standard_error,
      reading_level,
      reading_flag,
      speaking_score,
      speaking_standard_error,
      speaking_level,
      speaking_flag,
      writing_score,
      writing_standard_error,
      writing_level,
      writing_flag,
      siteID
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, ",");
    $studentID = trim($lineArray[3]);
    if($studentID != ""){
      array_push($records, buildInsertLine($db, $siteID, $studentID, $lineArray));
    }

    if(count($records) == MAX_IMPORT_LIMIT){
      $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
      $stmt = $db->stmt_init();
      $stmt->prepare($temporaryTableInsertSQL);
      $stmt->execute();
      $stmt->close();

      $records = [];
    }
  }

  if(count($records)){
    $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }
}

function buildInsertLine($db, $siteID, $studentID, $line){

  $administrativeDate = $data[1];

  $testDateRaw = $data[118];
  $testDatetime = DateTime::createFromFormat('Ymd', $testDateRaw);
  if($testDatetime !== false){
    $testDate = $testDatetime->format("Y-m-d H:i:s");
  }else{
    $testDate = (new DateTime())->format("Y-m-d H:i:s");
  }

  $enrolledGrade = $data[9];
  $lep = $data[11];
  $gifted = $data[13];
  $plan = $data[14];
  $iep = $data[15];

  $elaAccommodations = $data[17];
  $mathematicsAccommodations = $data[18];
  $socialStudiesAccommodations = $data[19];
  $scienceAccommodations = $data[20];

  $attendingSchool = $data[25];
  $schoolIRN = $data[26];
  $testName = $data[68];

  //scores can be values such as E or N
  $overallScore = is_numeric($data[80]) ? $data[80] : null;
  $overallStandardError = is_numeric($data[81]) ? $data[81] : null;
  $proficiencyStatus = is_numeric($data[82]) ? $data[82] : null;

  if(is_numeric($data[83])){
    $comprehensionScore = $data[83];
    $comprehensionFlag = "NULL";
  }else{
    $comprehensionScore = "NULL";
    $comprehensionFlag = validateOELPAFlag($data[83]);
  }
  $comprehensionStandardError = is_numeric($data[84]) ? $data[84] : "NULL";

  if(is_numeric($data[85])){
    $listeningScore = $data[85];
    $listeningFlag = "NULL";
  }else{
    $listeningScore = "NULL";
    $listeningFlag = validateOELPAFlag($data[85]);
  }
  $listeningStandardError = is_numeric($data[86]) ? $data[86] : "NULL";
  $listeningLevel = is_numeric($data[87]) ? $data[87] : "NULL";

  if(is_numeric($data[88])){
    $readingScore = $data[88];
    $readingFlag = "NULL";
  }else{
    $readingScore = "NULL";
    $readingFlag = validateOELPAFlag($data[88]);
  }
  $readingStandardError = is_numeric($data[89]) ? $data[89] : "NULL";
  $readingLevel = is_numeric($data[90]) ? $data[90] : "NULL";

  if(is_numeric($data[91])){
    $speakingScore = $data[91];
    $speakingFlag = "NULL";
  }else{
    $speakingScore = "NULL";
    $speakingFlag = validateOELPAFlag($data[91]);
  }
  $speakingStandardError = is_numeric($data[92]) ? $data[92] : "NULL";
  $speakingLevel = is_numeric($data[93]) ? $data[93] : "NULL";

  if(is_numeric($data[94])){
    $writingScore = $data[94];
    $writingFlag = "NULL";
  }else{
    $writingScore = "NULL";
    $writingFlag = validateOELPAFlag($data[94]);
  }
  $writingStandardError = is_numeric($data[95]) ? $data[95] : "NULL";
  $writingLevel = is_numeric($data[96]) ? $data[96] : "NULL";

  $sql = "
    (
      '$administrativeDate',
      '$testDate',
      $studentID,
      '$enrolledGrade'
      '$lep',
      '$gifted'
      '$plan',
      '$iep',
      '$elaAccommodations',
      '$mathematicsAccommodations',
      '$socialStudiesAccommodations',
      '$scienceAccommodations',
      '$attendingSchool',
      '$schoolIRN',
      '$testName',
      $overallScore,
      $overallStandardError,
      $proficiencyStatus,
      $comprehensionScore,
      $comprehensionStandardError,
      $comprehensionFlag,
      $listeningScore,
      $listeningStandardError,
      $listeningLevel,
      $listeningFlag,
      $readingScore,
      $readingStandardError,
      $readingLevel,
      $readingFlag,
      $speakingScore,
      $speakingStandardError,
      $speakingLevel,
      $speakingFlag,
      $writingScore,
      $writingStandardError,
      $writingLevel,
      $writingFlag,
      $siteID
    )";
  return $sql;
}

//returns the flag or null on insert
function validateOELPAFlag($flag){
  return $flag == "E" || $flag == "N" || $flag == "INV" ? $flag : "NULL";
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $dropIDColumn = "ALTER TABLE {$temporaryTableName} DROP ID";
  $dropIDStmt = $db->stmt_init();
  $dropIDStmt->prepare($dropIDColumn);
  $dropIDStmt->execute();
  $dropIDStmt->close();

  $insertSQL = "INSERT INTO Abre_OELPA
                  SELECT 0, t.* FROM {$temporaryTableName} t
                ON DUPLICATE KEY UPDATE
                  enrolled_grade = t.enrolled_grade,
                  lep = t.lep,
                  gifted = t.gifted,
                  `504` = t.504,
                  iep = t.iep,
                  ela_accommodations = t.ela_accommodations,
                  mathematics_accommodations = t.mathematics_accommodations,
                  socialstudies_accommodations = t.socialstudies_accommodations,
                  science_accommodations = t.science_accommodations,
                  attending_school = t.attending_school,
                  school_irn = t.school_irn,
                  test_name = t.test_name,
                  overall_score = t.overall_score,
                  overall_standard_error = t.overall_standard_error,
                  proficiency_status = t.proficiency_status,
                  comprehension_score = t.comprehension_score,
                  comprehension_standard_error = t.comprehension_standard_error,
                  comprehension_flag = t.comprehension_flag,
                  listening_score = t.listening_score,
                  listening_standard_error = t.listening_standard_error,
                  listening_level = t.listening_level,
                  listening_flag = t.listening_flag,
                  reading_score = t.reading_score,
                  reading_standard_error = t.reading_standard_error,
                  reading_level = t.reading_level,
                  reading_flag = t.reading_flag,
                  speaking_score = t.speaking_score,
                  speaking_standard_error = t.speaking_standard_error,
                  speaking_level = t.speaking_level,
                  speaking_flag = t.speaking_flag,
                  writing_score = t.writing_score,
                  writing_standard_error = t.writing_standard_error,
                  writing_level = t.writing_level,
                  writing_flag = t.writing_flag";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>
