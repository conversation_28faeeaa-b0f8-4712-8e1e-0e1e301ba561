<?php
/*
* Copyright 2016-2023 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config){

  $cronName = 'Public School Works';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("SAFE_COLUMN_COUNT", 25);
    define("MAX_IMPORT_LIMIT", 25);

    $currentSchoolYearID = getCurrentSchoolYearID($db);

    $error = null;
    $skip = null;
    $separator = "\r\n";

  	$sftp = new SFTP($config->sftp->ip, 23);
  	if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
  		throw new Exception("Login to SFTP failed.");
  	}

  	$cronFile = $sftp->get($config->sftp->file);

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, ",");
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }
    }

    // Upload SFTP File to Google Cloud Storage Bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_Public_School_Works.csv";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($cronFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Upload to the other folder location in landing zone bucket
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $folderName = pathinfo($fileName, PATHINFO_FILENAME);
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($cronFile, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO abre_psw (
      PSW_ID, Status_ID, Local_Student_ID, Report_No, Report_Type,
      Student_Last_Name, Student_First_Name, Student_ID, DOB, Gender, Ethnicity, Student_IEP, Student_ED, Student_LEP,
      Student_Migrant, 504_Plan, Has_Guid, School_Name, Grade, RefYr, RefMo, Homeroom_Teacher, Offense, Offense_Group_Association, Offense_Location,
      Offense_Date, Report_Date, site_id, school_year_id
     ) VALUES ";

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row
    $db->query("DELETE FROM abre_psw WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
  	do{
      $data = str_getcsv($line, ",");

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$pswId = trim($db->escape_string($data[0]));
        $statusId = trim($db->escape_string($data[1]));
        $localStudentId = trim($db->escape_string($data[2]));
        $reportNo = trim($db->escape_string($data[3]));
        $reportType = trim($db->escape_string($data[4]));
        $studentLastName = trim($db->escape_string($data[5]));
        $studentFirstName = trim($db->escape_string($data[6]));
        $studentId = trim($db->escape_string($data[7]));
        $dob = trim($db->escape_string($data[8]));
        $gender = trim($db->escape_string($data[9]));
        $ethnicity = trim($db->escape_string($data[10]));
        $studentIep = trim($db->escape_string($data[11]));
        $studentEd = trim($db->escape_string($data[12]));
        $studentLep = trim($db->escape_string($data[13]));
        $studentMigrant = trim($db->escape_string($data[14]));
        $student504 = trim($db->escape_string($data[15]));
        $hasGuid = trim($db->escape_string($data[16]));
        $schoolName = trim($db->escape_string($data[17]));
        $grade = trim($db->escape_string($data[18]));
        $refYr = trim($db->escape_string($data[19]));
        $refMo = trim($db->escape_string($data[20]));
        $homeroomTeacher = trim($db->escape_string($data[21]));
        $offense = trim($db->escape_string($data[22]));
        $offenseGroup = trim($db->escape_string($data[23]));
        $offenseLocation = trim($db->escape_string($data[24]));
        $offenseDate = trim($db->escape_string($data[28]));
        $reportDate = trim($db->escape_string($data[30]));

        //Validate it's a real row that has a psw id and not special characters in a description
        if(is_numeric($pswId)){

          $valuesToImport []= "('$pswId', '$statusId', '$localStudentId', '$reportNo', '$reportType',
                                  '$studentLastName', '$studentFirstName', '$studentId', '$dob', '$gender', '$ethnicity',
                                  '$studentIep', '$studentEd', '$studentLep', '$studentMigrant', '$student504', '$hasGuid',
                                  '$schoolName', '$grade', '$refYr', '$refMo', '$homeroomTeacher', '$offense',
                                  '$offenseGroup', '$offenseLocation', '$offenseDate', '$reportDate',
                                  $siteID, $currentSchoolYearID)";

        }

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
  
}

?>
