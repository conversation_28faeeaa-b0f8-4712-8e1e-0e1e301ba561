<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config)
{

    $cronName = 'Aeries API';
    $currentSchoolYearID = getCurrentSchoolYearID($db);

    try {

        function apiRequest($domain, $certificate, $endpoint)
        {

            $endpoint = $domain . $endpoint;

            //cURL Request and Response
            $client = curl_init();
            curl_setopt($client, CURLOPT_URL, $endpoint);
            curl_setopt($client, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($client, CURLOPT_HTTPHEADER, array(
                'Accept: application/json',
                "AERIES-CERT: $certificate"
            ));
            $response = curl_exec($client);
            if (curl_errno($client)) {
                throw new Exception("Unable to Complete API Request");
                curl_close($client);
                return;
            }
            curl_close($client);
            return $response;
        }

        function getSchools($domain, $certificate)
        {

            $endpoint = '/api/v5/schools/';
            $response = apiRequest($domain, $certificate, $endpoint);

            //Use Results
            $schoolsRecords = [];
            $schools = json_decode($response);
            foreach ($schools as $school) {

                $schoolCode = $school->SchoolCode;
                $name = $school->Name;
                $schoolsRecords[] = array('name' => $name, 'code' => $schoolCode);
            }
            return $schoolsRecords;
        }

        function getAssertiveDiscipline($db, $siteID, $currentSchoolYearID, $domain, $certificate)
        {

            $schools = getSchools($domain, $certificate);

            $studentRecords = [];
            $db->query("DELETE FROM vendor_aeries_assertive_discipline WHERE site_id = $siteID AND school_year_id = $currentSchoolYearID");
            $stmt = $db->stmt_init();
            foreach ($schools as $school) {
                $schoolCode = $school['code'];
                $endpoint = '/api/v5/schools/' . $schoolCode . '/AssertiveDiscipline/';
                $response = apiRequest($domain, $certificate, $endpoint);

                // Decode the response
                $assertiveDiscipline = json_decode($response, true); // Use associative array
                foreach ($assertiveDiscipline as $assertiveDisciplineIncidents) {
                    $studentRecord = [
                        'StudentID' => $assertiveDisciplineIncidents['StudentID'],
                        'AssertiveDisciplines' => []
                    ];

                    foreach ($assertiveDisciplineIncidents['AssertiveDisciplines'] as $discipline) {

                        $disciplineRecord = [
                            'AdministrativeDecisions' => [],
                            'ShortDescription' => $discipline['ShortDescription'],
                            'StaffReferral' => $discipline['StaffReferral'],
                            'ReferredByOther' => $discipline['ReferredByOther'],
                            'SequenceNumber' => $discipline['SequenceNumber'],
                            'IncidentDate' => $discipline['IncidentDate'],
                            'IncidentID' => $discipline['IncidentID'],
                            'ExactTime' => $discipline['ExactTime'],
                            'ApproximateTimeCode' => $discipline['ApproximateTimeCode'],
                            'SchoolOfIncidentCode' => $discipline['SchoolOfIncidentCode'],
                            'LocationCode' => $discipline['LocationCode'],
                            'PossibleMotivationCode' => $discipline['PossibleMotivationCode'],
                            'WeaponTypeCode' => $discipline['WeaponTypeCode'],
                            'Demerits' => $discipline['Demerits'],
                            'Initials' => $discipline['Initials'],
                            'InstructionalSupportIndicator' => $discipline['InstructionalSupportIndicator'],
                            'Comment' => $discipline['Comment'],
                            'IsSubstituteTeacherReferral' => $discipline['IsSubstituteTeacherReferral'],
                            'ViolationCode1' => $discipline['ViolationCode1'],
                            'ViolationCode2' => $discipline['ViolationCode2'],
                            'ViolationCode3' => $discipline['ViolationCode3'],
                            'ViolationCode4' => $discipline['ViolationCode4'],
                            'ViolationCode5' => $discipline['ViolationCode5'],
                            'PreReferralInterventionCode1' => $discipline['PreReferralInterventionCode1'],
                            'PreReferralInterventionCode2' => $discipline['PreReferralInterventionCode2'],
                            'PreReferralInterventionCode3' => $discipline['PreReferralInterventionCode3'],
                            'UserCode1' => $discipline['UserCode1'],
                            'UserCode2' => $discipline['UserCode2'],
                            'UserCode3' => $discipline['UserCode3'],
                            'UserCode4' => $discipline['UserCode4'],
                            'UserCode5' => $discipline['UserCode5'],
                            'UserCode6' => $discipline['UserCode6'],
                            'UserCode7' => $discipline['UserCode7'],
                            'UserCode8' => $discipline['UserCode8'],
                        ];

                        // Extract AdministrativeDecisions
                        foreach ($discipline['AdministrativeDecisions'] as $decision) {
                            $decisionRecord = [
                                'StudentID' => $decision['StudentID'],
                                'AssignedDays' => $decision['AssignedDays'],
                                'AssignedHours' => $decision['AssignedHours'],
                                'AssignedStartDate' => $decision['AssignedStartDate'],
                                'AssignedEndDate' => $decision['AssignedEndDate'],
                                'AssignedReturnDate' => $decision['AssignedReturnDate'],
                                'ReasonForDifferenceCode' => $decision['ReasonForDifferenceCode'],
                                'DisciplinaryAssignmentSchoolCode' => $decision['DisciplinaryAssignmentSchoolCode'],
                                'ActionDecisionDate' => $decision['ActionDecisionDate'],
                                'SequenceNumber' => $decision['SequenceNumber'],
                                'DispositionCode' => $decision['DispositionCode'],
                                'Days' => $decision['Days'],
                                'Hours' => $decision['Hours'],
                                'StartDate' => $decision['StartDate'],
                                'EndDate' => $decision['EndDate'],
                                'ReturnDate' => $decision['ReturnDate'],
                                'ReturnStatusCode' => $decision['ReturnStatusCode'],
                                'ReturnLocationCode' => $decision['ReturnLocationCode'],
                                'ActionAuthorityCode' => $decision['ActionAuthorityCode'],
                                'PlacementCode' => $decision['PlacementCode'],
                                'ResultCode' => $decision['ResultCode'],
                                'SuspensionTagCode' => $decision['SuspensionTagCode']
                            ];

                            $disciplineRecord['AdministrativeDecisions'][] = $decisionRecord;

                            // Write to the database
                            $sql = "INSERT INTO vendor_aeries_assertive_discipline (
                                StudentID, ShortDescription, StaffReferral, ReferredByOther, SequenceNumber, IncidentDate, IncidentID, ExactTime, ApproximateTimeCode, SchoolOfIncidentCode,
                                LocationCode, PossibleMotivationCode, WeaponTypeCode, Demerits, Initials, InstructionalSupportIndicator, Comment, IsSubstituteTeacherReferral, ViolationCode1, ViolationCode2, ViolationCode3, ViolationCode4, ViolationCode5,
                                PreReferralInterventionCode1, PreReferralInterventionCode2, PreReferralInterventionCode3, UserCode1, UserCode2, UserCode3, UserCode4, UserCode5, UserCode6, UserCode7, UserCode8,
                                AssignedDays, AssignedHours, AssignedStartDate, AssignedEndDate, AssignedReturnDate, ReasonForDifferenceCode, DisciplinaryAssignmentSchoolCode,
                                ActionDecisionDate, DecisionSequenceNumber, DispositionCode, Days, Hours, StartDate, EndDate, ReturnDate, ReturnStatusCode, ReturnLocationCode, ActionAuthorityCode, PlacementCode,
                                ResultCode, SuspensionTagCode,
                                site_id, school_year_id
                        ) VALUES (
                            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                        )";
                            $stmt->prepare($sql);
                            $stmt->bind_param(
                                "ssssisissiiisssisisissssssssssssssssiissssisissssssssssss",
                                $studentRecord['StudentID'],
                                $discipline['ShortDescription'],
                                $discipline['StaffReferral'],
                                $discipline['ReferredByOther'],
                                $discipline['SequenceNumber'],
                                $discipline['IncidentDate'],
                                $discipline['IncidentID'],
                                $discipline['ExactTime'],
                                $discipline['ApproximateTimeCode'],
                                $discipline['SchoolOfIncidentCode'],
                                $discipline['LocationCode'],
                                $discipline['PossibleMotivationCode'],
                                $discipline['WeaponTypeCode'],
                                $discipline['Demerits'],
                                $discipline['Initials'],
                                $discipline['InstructionalSupportIndicator'],
                                $discipline['Comment'],
                                $discipline['IsSubstituteTeacherReferral'],
                                $discipline['ViolationCode1'],
                                $discipline['ViolationCode2'],
                                $discipline['ViolationCode3'],
                                $discipline['ViolationCode4'],
                                $discipline['ViolationCode5'],
                                $discipline['PreReferralInterventionCode1'],
                                $discipline['PreReferralInterventionCode2'],
                                $discipline['PreReferralInterventionCode3'],
                                $discipline['UserCode1'],
                                $discipline['UserCode2'],
                                $discipline['UserCode3'],
                                $discipline['UserCode4'],
                                $discipline['UserCode5'],
                                $discipline['UserCode6'],
                                $discipline['UserCode7'],
                                $discipline['UserCode8'],
                                $decision['AssignedDays'],
                                $decision['AssignedHours'],
                                $decision['AssignedStartDate'],
                                $decision['AssignedEndDate'],
                                $decision['AssignedReturnDate'],
                                $decision['ReasonForDifferenceCode'],
                                $decision['DisciplinaryAssignmentSchoolCode'],
                                $decision['ActionDecisionDate'],
                                $decision['SequenceNumber'],
                                $decision['DispositionCode'],
                                $decision['Days'],
                                $decision['Hours'],
                                $decision['StartDate'],
                                $decision['EndDate'],
                                $decision['ReturnDate'],
                                $decision['ReturnStatusCode'],
                                $decision['ReturnLocationCode'],
                                $decision['ActionAuthorityCode'],
                                $decision['PlacementCode'],
                                $decision['ResultCode'],
                                $decision['SuspensionTagCode'],
                                $siteID,
                                $currentSchoolYearID
                            );
                            $stmt->execute();
                        }

                        $studentRecord['AssertiveDisciplines'][] = $disciplineRecord;
                    }

                    // Collect the record for each student
                    $studentRecords[] = $studentRecord;
                }
            }

            // Save json to a temporary file
            $tempFilePath = tempnam(sys_get_temp_dir(), 'assertive_discipline');
            file_put_contents($tempFilePath, json_encode($studentRecords));
            $fileContent = file_get_contents($tempFilePath);
            $file = 'aeries_AssertiveDiscipline.json';
            $storage = new StorageClient([
                'projectId' => "abre-production"
            ]);

            // Upload SFTP File to Google Cloud Storage Bucket
            $bucketName = "prd-landing-zone";
            $currentDate = date("Ymd");
            $bucket = $storage->bucket($bucketName);
            $bucket->upload($fileContent, [
                'name' => "$currentDate/site-id/$siteID/$file"
            ]);

            // Upload SFTP File to Google Cloud Storage Bucket (Alternate Method)
            $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
            $folderName = pathinfo($file, PATHINFO_FILENAME);
            $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
            $bucketName = "prd-landing-zone";
            $bucket = $storage->bucket($bucketName);
            $bucket->upload($fileContent, [
                'name' => "$currentDate/filename/$folderName/$modifiedFile"
            ]);

            // Delete the temporary file
            unlink($tempFilePath);

            $stmt->close();
            return $studentRecords;
        }

        //Configuration
        $uuid = Logger::logCronStart($db, $siteID, $cronName);
        $domain = $config->aeries->domain;
        $certificate = $config->aeries->certificate;

        //Get AssertiveDiscipline
        getAssertiveDiscipline($db, $siteID, $currentSchoolYearID, $domain, $certificate);
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }
    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
