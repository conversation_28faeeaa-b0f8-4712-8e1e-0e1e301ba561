<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){
  try{
    define("MAX_IMPORT_LIMIT", 100);

    $error = null;
    $skip = null;

    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }

    $localTempFile = tempnam(sys_get_temp_dir(), 'abre-inview');
    $isFileLoaded = $sftp->get('Abre_Inview_v1.0.csv', $localTempFile);

    if($isFileLoaded){
      importInviewData($db, $siteID, $localTempFile);
    }else{
      $skip = true;
      throw new Exception("No file found.");
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];
  $cronName = 'Inview Import v1.0';
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronResult($db, $siteID, $cronName, $status, $details);
}


function importInviewData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($siteID, $tempFile, $db, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName}
                                LIKE abre_inview";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($siteID, $fileName, $db, $temporaryTableName){
  $temporaryTableColumnsSQL = "
    INSERT INTO {$temporaryTableName}
    (
      organization_id,
      school_district,
      school_district_level,
      school_district_number,
      grade,
      city,
      state,
      school_name,
      school_level,
      school_number,
      school_special_codes,
      teacher_last_name,
      teacher_level,
      teacher_code,
      test_name,
      test_form,
      test_level,
      test_date,
      student_id,
      student_race_code,
      birth_date,
      scoring,
      quarter_month,
      norms_year,
      student_first_name,
      student_last_name,
      student_middle_initial,
      age_in_months,
      student_gender,
      drc_student_id,
      gis_name,
      create_date,
      cognitive_skills_index,
      cognitive_skills_index_upper,
      cognitive_skills_index_lower,
      sequences_num_correct,
      analogies_num_correct,
      quantitative_reasoning_num_correct,
      total_non_verbal_num_correct,
      verbal_reasoning_words_num_correct,
      verbal_reasoning_context_num_correct,
      total_verbal_num_correct,
      total_score_num_correct,
      sequences_scale_score,
      analogies_scale_score,
      quantitative_reasoning_scale_score,
      total_non_verbal_scale_score,
      verbal_reasoning_words_scale_score,
      verbal_reasoning_context_scale_score,
      total_verbal_scale_score,
      total_score_scale_score,
      sequences_standard_err,
      analogies_standard_err,
      quantitative_reasoning_standard_err,
      total_non_verbal_standard_err,
      verbal_reasoning_words_standard_err,
      verbal_reasoning_context_standard_err,
      total_verbal_standard_err,
      total_score_standard_err,
      sequences_age_stanine,
      analogies_age_stanine,
      quantitative_reasoning_age_stanine,
      total_non_verbal_age_stanine,
      verbal_reasoning_words_age_stanine,
      verbal_reasoning_context_age_stanine,
      total_verbal_age_stanine,
      total_score_age_stanine,
      sequences_age_percentile,
      analogies_age_percentile,
      quantitative_reasoning_age_percentile,
      total_non_verbal_age_percentile,
      verbal_reasoning_words_age_percentile,
      verbal_reasoning_context_age_percentile,
      total_verbal_age_percentile,
      total_score_age_percentile,
      sequences_grade_stanine,
      analogies_grade_stanine,
      quantitative_reasoning_grade_stanine,
      total_non_verbal_grade_stanine,
      verbal_reasoning_words_grade_stanine,
      verbal_reasoning_context_grade_stanine,
      total_verbal_grade_stanine,
      total_score_grade_stanine,
      sequences_grade_percentile,
      analogies_grade_percentile,
      quantitative_reasoning_grade_percentile,
      total_non_verbal_grade_percentile,
      verbal_reasoning_words_grade_percentile,
      verbal_reasoning_context_grade_percentile,
      total_verbal_grade_percentile,
      total_score_grade_percentile,
      sequences_local_stanine,
      analogies_local_stanine,
      quantitative_reasoning_local_stanine,
      total_non_verbal_local_stanine,
      verbal_reasoning_words_local_stanine,
      verbal_reasoning_context_local_stanine,
      total_verbal_local_stanine,
      total_score_local_stanine,
      sequences_local_percentile,
      analogies_local_percentile,
      quantitative_reasoning_local_percentile,
      total_non_verbal_local_percentile,
      verbal_reasoning_words_local_percentile,
      verbal_reasoning_context_local_percentile,
      total_verbal_local_percentile,
      total_score_local_percentile,
      sequences_responses,
      analogies_responses,
      quantitative_reasoning_responses,
      verbal_reasoning_words_responses,
      verbal_reasoning_context_responses,
      site_id
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = [];
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, ",");
    $studentID = trim($lineArray[26]);
    if($studentID != ""){
      array_push($records, buildInsertLine($db, $siteID, $studentID, $lineArray));
    }

    if(count($records) == MAX_IMPORT_LIMIT){
      $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
      $stmt = $db->stmt_init();
      $stmt->prepare($temporaryTableInsertSQL);
      $stmt->execute();
      $stmt->close();

      $records = [];
    }
  }

  if(count($records)){
    $temporaryTableInsertSQL = $temporaryTableColumnsSQL . implode(",", $records) . ";";
    $stmt = $db->stmt_init();
    $stmt->prepare($temporaryTableInsertSQL);
    $stmt->execute();
    $stmt->close();
  }
}

function buildInsertLine($db, $siteID, $studentID, $line){

  $organizationID = $line[1];
  $districtName = $line[2];
  $districtLevel = nullCheck($line[3]);
  $districtNumber = $line[4];
  $grade = nullCheck($line[6]);
  $city = $line[7];
  $state = $line[8];
  $school = $line[9];
  $schoolLevel = nullCheck($line[10]);
  $schoolNumber = nullCheck($line[11]);
  $schoolSpecialCode = nullCheck($line[12]);
  $teacherLastName = $db->escape_string($line[13]);
  $teacherLevel = nullCheck($line[14]);
  $teacherSpecialCode = $line[16];
  $testName = $line[21];
  $testForm = $line[22];
  $testLevel = nullCheck($line[23]);

  $testDate = $line[24];
  $testDateTime = date_create_from_format("mdy", $testDate);
  if($testDateTime !== false){
    $testDateFormatted = "'".$testDateTime->format("Y-m-d")."'";
  }else{
    $testDateFormatted = '';
  }

  $studentRace = nullCheck($line[34]);

  $studentBirthday = $line[44];
  $birthdayDateTime = date_create_from_format("mdy", $studentBirthday);
  if($birthdayDateTime !== false){
    $studentBirthdayFormatted = "'".$birthdayDateTime->format("Y-m-d")."'";
  }else{
    $studentBirthdayFormatted = 'NULL';
  }

  $scoring = $line[45];
  $quarterMonth = nullCheck($line[46]);
  $normsYear = nullCheck($line[48]);
  $studentFirstName = $db->escape_string($line[51]);
  $studentLastName = $db->escape_string($line[52]);
  $studentMiddleInitial = nullCheckWithQuotes($line[53]);
  $ageInMonths = nullCheck($line[54]);
  $studentGender = $line[55];

  $drcStudentID = nullCheckWithQuotes($line[492]);
  $gisName = nullCheckWithQuotes($line[494]);

  $createDate = $line[496];
  $createDateTime = date_create_from_format("mdY", $createDate);
  if($createDateTime !== false){
    $createDateFormatted = "'".$createDateTime->format("Y-m-d")."'";
  }else{
    $createDateFormatted = 'NULL';
  }

  $cognitiveSkillsIndex = nullCheck($line[568]);
  $cognitiveSkillsIndexUpper = nullCheck($line[569]);
  $cognitiveSkillsIndexLower = nullCheck($line[570]);

  $sequencesNumCorrect = nullCheck($line[571]);
  $analogiesNumCorrect = nullCheck($line[572]);
  $quantitativeReasoningNumCorrect = nullCheck($line[573]);
  $totalNonVerbalNumCorrect = nullCheck($line[574]);
  $verbalReasoningWordsNumCorrect = nullCheck($line[575]);
  $verbalReasoningContextNumCorrect = nullCheck($line[576]);
  $totalVerbalNumCorrect = nullCheck($line[577]);
  $totalScoreNumCorrect = nullCheck($line[578]);

  $sequencesScaleScore = nullCheck($line[579]);
  $analogiesScaleScore = nullCheck($line[580]);
  $quantitativeReasoningScaleScore = nullCheck($line[581]);
  $totalNonVerbalScaleScore = nullCheck($line[582]);
  $verbalReasoningWordsScaleScore = nullCheck($line[583]);
  $verbalReasoningContextScaleScore = nullCheck($line[584]);
  $totalVerbalScaleScore = nullCheck($line[585]);
  $totalScoreScaleScore = nullCheck($line[586]);

  $sequencesStandardErr = nullCheck($line[587]);
  $analogiesStandardErr = nullCheck($line[588]);
  $quantitativeReasoningStandardErr = nullCheck($line[589]);
  $totalNonVerbalStandardErr = nullCheck($line[590]);
  $verbalReasoningWordsStandardErr = nullCheck($line[591]);
  $verbalReasoningContextStandardErr = nullCheck($line[592]);
  $totalVerbalStandardErr = nullCheck($line[593]);
  $totalScoreStandardErr = nullCheck($line[594]);

  list($sequencesAgeStanine, $sequencesAgePercentile) = parseStanineAndPercentile($line[595]);
  list($analogiesAgeStanine, $analogiesAgePercentile) = parseStanineAndPercentile($line[596]);
  list($quantitativeReasoningAgeStanine, $quantitativeReasoningAgePercentile) = parseStanineAndPercentile($line[597]);
  list($totalNonVerbalAgeStanine, $totalNonVerbalAgePercentile)= parseStanineAndPercentile($line[598]);
  list($verbalReasoningWordsAgeStanine, $verbalReasoningWordsAgePercentile) = parseStanineAndPercentile($line[599]);
  list($verbalReasoningContextAgeStanine, $verbalReasoningContextAgePercentile) = parseStanineAndPercentile($line[600]);
  list($totalVerbalAgeStanine, $totalVerbalAgePercentile) = parseStanineAndPercentile($line[601]);
  list($totalScoreAgeStanine, $totalScoreAgePercentile) = parseStanineAndPercentile($line[602]);

  list($sequencesGradeStanine, $sequencesGradePercentile) = parseStanineAndPercentile($line[603]);
  list($analogiesGradeStanine, $analogiesGradePercentile) = parseStanineAndPercentile($line[604]);
  list($quantitativeReasoningGradeStanine, $quantitativeReasoningGradePercentile) = parseStanineAndPercentile($line[605]);
  list($totalNonVerbalGradeStanine, $totalNonVerbalGradePercentile)= parseStanineAndPercentile($line[606]);
  list($verbalReasoningWordsGradeStanine, $verbalReasoningWordsGradePercentile) = parseStanineAndPercentile($line[607]);
  list($verbalReasoningContextGradeStanine, $verbalReasoningContextGradePercentile) = parseStanineAndPercentile($line[608]);
  list($totalVerbalGradeStanine, $totalVerbalGradePercentile) = parseStanineAndPercentile($line[609]);
  list($totalScoreGradeStanine, $totalScoreGradePercentile) = parseStanineAndPercentile($line[610]);

  list($sequencesLocalStanine, $sequencesLocalPercentile) = parseStanineAndPercentile($line[611]);
  list($analogiesLocalStanine, $analogiesLocalPercentile) = parseStanineAndPercentile($line[612]);
  list($quantitativeReasoningLocalStanine, $quantitativeReasoningLocalPercentile) = parseStanineAndPercentile($line[613]);
  list($totalNonVerbalLocalStanine, $totalNonVerbalLocalPercentile)= parseStanineAndPercentile($line[614]);
  list($verbalReasoningWordsLocalStanine, $verbalReasoningWordsLocalPercentile) = parseStanineAndPercentile($line[615]);
  list($verbalReasoningContextLocalStanine, $verbalReasoningContextLocalPercentile) = parseStanineAndPercentile($line[616]);
  list($totalVerbalLocalStanine, $totalVerbalLocalPercentile) = parseStanineAndPercentile($line[617]);
  list($totalScoreLocalStanine, $totalScoreLocalPercentile) = parseStanineAndPercentile($line[618]);

  $sequencesResponses = nullCheckWithQuotes($line[619]);
  $analogiesResponses = nullCheckWithQuotes($line[620]);
  $quantitativeReasoningRespones = nullCheckWithQuotes($line[621]);
  $verbalReasoningWordsResponses = nullCheckWithQuotes($line[622]);
  $verbalReasoningContextResponses = nullCheckWithQuotes($line[623]);

  $sql = "
    (
      '$organizationID',
      '$districtName',
      $districtLevel,
      '$districtNumber',
      $grade,
      '$city',
      '$state',
      '$school',
      $schoolLevel,
      $schoolNumber,
      $schoolSpecialCode,
      '$teacherLastName',
      $teacherLevel,
      '$teacherSpecialCode',
      '$testName',
      '$testForm',
      $testLevel,
      $testDateFormatted,
      '$studentID',
      $studentRace,
      $studentBirthdayFormatted,
      '$scoring',
      $quarterMonth,
      $normsYear,
      '$studentFirstName',
      '$studentLastName',
      $studentMiddleInitial,
      $ageInMonths,
      '$studentGender',
      $drcStudentID,
      $gisName,
      $createDateFormatted,
      $cognitiveSkillsIndex,
      $cognitiveSkillsIndexUpper,
      $cognitiveSkillsIndexLower,
      $sequencesNumCorrect,
      $analogiesNumCorrect,
      $quantitativeReasoningNumCorrect,
      $totalNonVerbalNumCorrect,
      $verbalReasoningWordsNumCorrect,
      $verbalReasoningContextNumCorrect,
      $totalVerbalNumCorrect,
      $totalScoreNumCorrect,
      $sequencesScaleScore,
      $analogiesScaleScore,
      $quantitativeReasoningScaleScore,
      $totalNonVerbalScaleScore,
      $verbalReasoningWordsScaleScore,
      $verbalReasoningContextScaleScore,
      $totalVerbalScaleScore,
      $totalScoreScaleScore,
      $sequencesStandardErr,
      $analogiesStandardErr,
      $quantitativeReasoningStandardErr,
      $totalNonVerbalStandardErr,
      $verbalReasoningWordsStandardErr,
      $verbalReasoningContextStandardErr,
      $totalVerbalStandardErr,
      $totalScoreStandardErr,
      $sequencesAgeStanine,
      $analogiesAgeStanine,
      $quantitativeReasoningAgeStanine,
      $totalNonVerbalAgeStanine,
      $verbalReasoningWordsAgeStanine,
      $verbalReasoningContextAgeStanine,
      $totalVerbalAgeStanine,
      $totalScoreAgeStanine,
      $sequencesAgePercentile,
      $analogiesAgePercentile,
      $quantitativeReasoningAgePercentile,
      $totalNonVerbalAgePercentile,
      $verbalReasoningWordsAgePercentile,
      $verbalReasoningContextAgePercentile,
      $totalVerbalAgePercentile,
      $totalScoreAgePercentile,
      $sequencesGradeStanine,
      $analogiesGradeStanine,
      $quantitativeReasoningGradeStanine,
      $totalNonVerbalGradeStanine,
      $verbalReasoningWordsGradeStanine,
      $verbalReasoningContextGradeStanine,
      $totalVerbalGradeStanine,
      $totalScoreGradeStanine,
      $sequencesGradePercentile,
      $analogiesGradePercentile,
      $quantitativeReasoningGradePercentile,
      $totalNonVerbalGradePercentile,
      $verbalReasoningWordsGradePercentile,
      $verbalReasoningContextGradePercentile,
      $totalVerbalGradePercentile,
      $totalScoreGradePercentile,
      $sequencesLocalStanine,
      $analogiesLocalStanine,
      $quantitativeReasoningLocalStanine,
      $totalNonVerbalLocalStanine,
      $verbalReasoningWordsLocalStanine,
      $verbalReasoningContextLocalStanine,
      $totalVerbalLocalStanine,
      $totalScoreLocalStanine,
      $sequencesLocalPercentile,
      $analogiesLocalPercentile,
      $quantitativeReasoningLocalPercentile,
      $totalNonVerbalLocalPercentile,
      $verbalReasoningWordsLocalPercentile,
      $verbalReasoningContextLocalPercentile,
      $totalVerbalLocalPercentile,
      $totalScoreLocalPercentile,
      $sequencesResponses,
      $analogiesResponses,
      $quantitativeReasoningRespones,
      $verbalReasoningWordsResponses,
      $verbalReasoningContextResponses,
      $siteID
    )";
  return $sql;
}

function parseStanineAndPercentile($data){
  $stanineAndPercentile = nullCheck($data);
  if($stanineAndPercentile == 'NULL'){
    return ['NULL', 'NULL'];
  }

  $temp = explode(" ", $stanineAndPercentile);
  return [$temp[0], $temp[1]];
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $dropIDColumn = "ALTER TABLE {$temporaryTableName} DROP id";
  $dropIDStmt = $db->stmt_init();
  $dropIDStmt->prepare($dropIDColumn);
  $dropIDStmt->execute();
  $dropIDStmt->close();

  $insertSQL = "INSERT INTO abre_inview
                  SELECT 0, t.* FROM {$temporaryTableName} t
                ON DUPLICATE KEY UPDATE
                  organization_id = t.organization_id,
                  school_district = t.school_district,
                  school_district_level = t.school_district_level,
                  school_district_number = t.school_district_number,
                  grade = t.grade,
                  city = t.city,
                  `state` = t.state,
                  school_name = t.school_name,
                  school_level = t.school_level,
                  school_number = t.school_number,
                  school_special_codes = t.school_special_codes,
                  teacher_last_name = t.teacher_last_name,
                  teacher_level = t.teacher_level,
                  teacher_code = t.teacher_code,
                  test_name = t.test_name,
                  test_form = t.test_form,
                  test_level = t.test_level,
                  student_race_code = t.student_race_code,
                  birth_date = t.birth_date,
                  scoring = t.scoring,
                  quarter_month = t.quarter_month,
                  norms_year = t.norms_year,
                  student_first_name = t.student_first_name,
                  student_last_name = t.student_last_name,
                  student_middle_initial = t.student_middle_initial,
                  age_in_months = t.age_in_months,
                  student_gender = t.student_gender,
                  drc_student_id = t.drc_student_id,
                  gis_name = t.gis_name,
                  create_date = t.create_date,
                  cognitive_skills_index = t.cognitive_skills_index,
                  cognitive_skills_index_upper = t.cognitive_skills_index_upper,
                  cognitive_skills_index_lower = t.cognitive_skills_index_lower,
                  sequences_num_correct = t.sequences_num_correct,
                  analogies_num_correct = t.analogies_num_correct,
                  quantitative_reasoning_num_correct = t.quantitative_reasoning_num_correct,
                  total_non_verbal_num_correct = t.total_non_verbal_num_correct,
                  verbal_reasoning_words_num_correct = t.verbal_reasoning_words_num_correct,
                  verbal_reasoning_context_num_correct = t.verbal_reasoning_context_num_correct,
                  total_verbal_num_correct = t.total_verbal_num_correct,
                  total_score_num_correct = t.total_score_num_correct,
                  sequences_scale_score = t.sequences_scale_score,
                  analogies_scale_score = t.analogies_scale_score,
                  quantitative_reasoning_scale_score = t.quantitative_reasoning_scale_score,
                  total_non_verbal_scale_score = t.total_non_verbal_scale_score,
                  verbal_reasoning_words_scale_score = t.verbal_reasoning_words_scale_score,
                  verbal_reasoning_context_scale_score = t.verbal_reasoning_context_scale_score,
                  total_verbal_scale_score = t.total_verbal_scale_score,
                  total_score_scale_score = t.total_score_scale_score,
                  sequences_standard_err = t.sequences_standard_err,
                  analogies_standard_err = t.analogies_standard_err,
                  quantitative_reasoning_standard_err = t.quantitative_reasoning_standard_err,
                  total_non_verbal_standard_err = t.total_non_verbal_standard_err,
                  verbal_reasoning_words_standard_err = t.verbal_reasoning_words_standard_err,
                  verbal_reasoning_context_standard_err = t.verbal_reasoning_context_standard_err,
                  total_verbal_standard_err = t.total_verbal_standard_err,
                  total_score_standard_err = t.total_score_standard_err,
                  sequences_age_stanine = t.sequences_age_stanine,
                  analogies_age_stanine = t.analogies_age_stanine,
                  quantitative_reasoning_age_stanine = t.quantitative_reasoning_age_stanine,
                  total_non_verbal_age_stanine = t.total_non_verbal_age_stanine,
                  verbal_reasoning_words_age_stanine = t.verbal_reasoning_words_age_stanine,
                  verbal_reasoning_context_age_stanine = t.verbal_reasoning_context_age_stanine,
                  total_verbal_age_stanine = t.total_verbal_age_stanine,
                  total_score_age_stanine = t.total_score_age_stanine,
                  sequences_age_percentile = t.sequences_age_percentile,
                  analogies_age_percentile = t.analogies_age_percentile,
                  quantitative_reasoning_age_percentile = t.quantitative_reasoning_age_percentile,
                  total_non_verbal_age_percentile = t.total_non_verbal_age_percentile,
                  verbal_reasoning_words_age_percentile = t.verbal_reasoning_words_age_percentile,
                  verbal_reasoning_context_age_percentile = t.verbal_reasoning_context_age_percentile,
                  total_verbal_age_percentile = t.total_verbal_age_percentile,
                  total_score_age_percentile = t.total_score_age_percentile,
                  sequences_grade_stanine = t.sequences_grade_stanine,
                  analogies_grade_stanine = t.analogies_grade_stanine,
                  quantitative_reasoning_grade_stanine = t.quantitative_reasoning_grade_stanine,
                  total_non_verbal_grade_stanine = t.total_non_verbal_grade_stanine,
                  verbal_reasoning_words_grade_stanine = t.verbal_reasoning_words_grade_stanine,
                  verbal_reasoning_context_grade_stanine = t.verbal_reasoning_context_grade_stanine,
                  total_verbal_grade_stanine = t.total_verbal_grade_stanine,
                  total_score_grade_stanine = t.total_score_grade_stanine,
                  sequences_grade_percentile = t.sequences_grade_percentile,
                  analogies_grade_percentile = t.analogies_grade_percentile,
                  quantitative_reasoning_grade_percentile = t.quantitative_reasoning_grade_percentile,
                  total_non_verbal_grade_percentile = t.total_non_verbal_grade_percentile,
                  verbal_reasoning_words_grade_percentile = t.verbal_reasoning_words_grade_percentile,
                  verbal_reasoning_context_grade_percentile = t.verbal_reasoning_context_grade_percentile,
                  total_verbal_grade_percentile = t.total_verbal_grade_percentile,
                  total_score_grade_percentile = t.total_score_grade_percentile,
                  sequences_local_stanine = t.sequences_local_stanine,
                  analogies_local_stanine = t.analogies_local_stanine,
                  quantitative_reasoning_local_stanine = t.quantitative_reasoning_local_stanine,
                  total_non_verbal_local_stanine = t.total_non_verbal_local_stanine,
                  verbal_reasoning_words_local_stanine = t.verbal_reasoning_words_local_stanine,
                  verbal_reasoning_context_local_stanine = t.verbal_reasoning_context_local_stanine,
                  total_verbal_local_stanine = t.total_verbal_local_stanine,
                  total_score_local_stanine = t.total_score_local_stanine,
                  sequences_local_percentile = t.sequences_local_percentile,
                  analogies_local_percentile = t.analogies_local_percentile,
                  quantitative_reasoning_local_percentile = t.quantitative_reasoning_local_percentile,
                  total_non_verbal_local_percentile = t.total_non_verbal_local_percentile,
                  verbal_reasoning_words_local_percentile = t.verbal_reasoning_words_local_percentile,
                  verbal_reasoning_context_local_percentile = t.verbal_reasoning_context_local_percentile,
                  total_verbal_local_percentile = t.total_verbal_local_percentile,
                  total_score_local_percentile = t.total_score_local_percentile,
                  sequences_responses = t.sequences_responses,
                  analogies_responses = t.analogies_responses,
                  quantitative_reasoning_responses = t.quantitative_reasoning_responses,
                  verbal_reasoning_words_responses = t.verbal_reasoning_words_responses,
                  verbal_reasoning_context_responses = t.verbal_reasoning_context_responses";
  $stmt = $db->stmt_init();
  $stmt->prepare($insertSQL);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>
