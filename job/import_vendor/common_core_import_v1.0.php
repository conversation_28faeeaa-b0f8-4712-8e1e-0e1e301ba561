<?php

require(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require_once(dirname(__FILE__) . '/../../vendor/autoload.php');

use GuzzleHttp\Client;
  /**
 * @deprecated
 */
function runJob($db, $target, $config) {
  try {
    $client = new Client([
      'base_uri' => $config->commonStandardsApi->apiUrl,
      'headers' => [
        'Api-Key' => $config->commonStandardsApi->apiKey
      ]
    ]);

    $jurisdictionID = addJursidiction($db, $client, $config->commonStandardsImport->jurisdiction);
    if ($jurisdictionID === null) {
      throw new Exception("Jursidiction ({$config->commonStandardsImport->jurisdiction}) not found in downloaded list");
    }

    $subjectResult = addSubjects($db, $client, $jurisdictionID, $config->commonStandardsImport->subjects);
    
    addStandards($db, $client, $subjectResult['standardSets']);
  } catch(Exception $ex) {
    $error = $ex;
  }

  $details = [];
  $cronName = "Common Standards Import - $target";
  if (isset($error) && !is_null($error)) {
    $details["error"] = $error;
    $status = CRON_FAILURE;
  } else {
    $status = CRON_SUCCESS;
  }

  if (count($subjectResult['notFound'])) {
    $details['notFound'] = $subjectResult['notFound'];
  }

  Logger::logCronResult($db, 0, $cronName, $status, $details);
}

// returns false if jurisdiction is not found in the list
function addJursidiction($db, $client, $jurisdictionTitle) {
  try {
    $response = $client->request('GET', 'jurisdictions');
    $json = json_decode($response->getBody(), true);

    $jurisdiction = arrayFindFirst($json['data'], 'title', $jurisdictionTitle);
    if ($jurisdiction === null) {
      return false;
    }

    $sql = "REPLACE INTO abre_standard_jurisdiction (id, title, jurisdiction_type)
            VALUES (?, ?, ?)";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);
    $stmt->bind_param("sss", $jurisdiction['id'], $jurisdiction['title'], $jurisdiction['type']);
    $stmt->execute();
    return $jurisdiction['id'];
  } finally {
    if (isset($stmt)) {
      $stmt->close();
    }
  }
}

function addSubjects($db, $client, $jurisdictionID, $subjects) {
  $response = $client->request('GET', "jurisdictions/$jurisdictionID");
  $json = json_decode($response->getBody(), true);

  $totalStandardSets = [];
  $notFound = [];

  foreach ($subjects as $subject) {
    $standardSets = arrayFindAll($json['data']['standardSets'], 'subject', $subject->title);
    if ($standardSets === []) {
      $notFound[] = $subject;
      continue;
    }

    $standardSubject = $standardSets[0];
    // NOTE str r pos NOT str pos
    $id = substr($standardSubject['id'], 0, strrpos($standardSubject['id'], '_'));

    try {
      $sql = "REPLACE INTO abre_standard_subject (id, jurisdiction_id, title,
                  source_title, source_url, common_name)
              VALUES (?, ?, ?, ?, ?, ?)";
      $stmt = $db->stmt_init();
      $stmt->prepare($sql);
      $stmt->bind_param('ssssss', $id, $jurisdictionID, $standardSubject['subject'],
        $standardSubject['document']['title'], $standardSubject['document']['sourceURL'], 
        $subject->commonName);
      $stmt->execute();
    } finally {
      if (isset($stmt)) {
        $stmt->close();
      }
    }

    foreach ($standardSets as $set) {
      addStandardSet($db, $id, $set);
      $set['certicaPrefix'] = $subject->certicaPrefix;
      $set['commonName'] = $subject->commonName;
      $totalStandardSets []= $set;
    }
  }

  return [
    "standardSets" => $totalStandardSets,
    "notFound" => $notFound
  ];
}

function addStandardSet($db, $subjectID, $standardSet) {
  try {
    $sql = "REPLACE INTO abre_standard_standard_set (id, subject_id, title)
            VALUES (?, ?, ?)";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);
    $stmt->bind_param('sss', $standardSet['id'], $subjectID, $standardSet['title']);
    $stmt->execute();
  } finally {
    if (isset($stmt)) {
      $stmt->close();
    }
  }

  addEducationLevels($db, $standardSet['id'], $standardSet['educationLevels']);
}

function addEducationLevels($db, $standardSetID, $educationLevels) {
  try {
    $sql = "REPLACE INTO abre_standard_standard_set_education_level 
                (standard_set_id, education_level)
            VALUES (?, ?)";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);

    foreach($educationLevels as $el) {
      $stmt->bind_param('ss', $standardSetID, $el);
      $stmt->execute();
    }
  } finally {
    if (isset($insertStmt)) {
      $insertStmt->close();
    }
  } 
}

function addStandards($db, $client, $standardSets) {
  foreach ($standardSets as $ss) {
    addStandard($db, $client, $ss);
  }
}

function addStandard($db, $client, $ss) {
  $response = $client->request('GET', "standard_sets/$ss[id]");
  $json = json_decode($response->getBody(), true);

  try {
    $sql = "REPLACE INTO abre_standard_standard (id, standard_set_id, notation, 
                certica_notation, standard_description, ordering)
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);

    foreach ($json['data']['standards'] as $key => $standard) {
      if ($standard['depth'] >= 2) {
        $certicaStandard = $ss['certicaPrefix'] . $standard['statementNotation'];
        $stmt->bind_param('ssssss', $standard['id'], $ss['id'], $standard['statementNotation'], 
          $certicaStandard, $standard['description'], $standard['position']);
        $stmt->execute();
      }
    }
  } finally {
    if (isset($stmt)) {
      $stmt->close();
    }
  }
}

?>
