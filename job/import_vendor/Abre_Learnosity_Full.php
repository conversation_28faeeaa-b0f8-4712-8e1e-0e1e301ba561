<?php

/*
* Copyright 2025 Abre.io Inc.
*/

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../utils/functions.php';
require_once __DIR__ . '/../utils/logging.php';

use LearnositySdk\Request\DataApi;
use Google\Cloud\Storage\StorageClient;

/**
 * Helper function for GCS upload to site-specific folder
 * @param array $data The data to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Current date in Ymd format
 * @param string|null $siteID Site identifier (optional)
 * @param string $endpoint The Learnosity endpoint being processed
 * @return void
 */
function _uploadToSiteGCS($data, $bucket, $currentDate, $siteID, $endpoint)
{
    try {
        if (empty($data)) {
            error_log("No data to upload for endpoint: $endpoint");
            return;
        }

        // Create a temporary file for streaming
        $tempFile = tmpfile();
        
        // Write opening bracket for JSON array
        fwrite($tempFile, '[');
        
        // Process data in smaller chunks of 100 records
        $chunkSize = 100;
        $chunks = array_chunk($data, $chunkSize);
        $isFirstChunk = true;
        
        foreach ($chunks as $chunk) {
            if (!$isFirstChunk) {
                fwrite($tempFile, ',');
            }
            
            $jsonChunk = json_encode($chunk);
            if ($jsonChunk === false) {
                error_log("Failed to encode JSON chunk for endpoint: $endpoint");
                continue;
            }
            
            // Remove the opening and closing brackets from the chunk
            $jsonChunk = trim($jsonChunk, '[]');
            fwrite($tempFile, $jsonChunk);
            
            $isFirstChunk = false;
            
            // Free up memory
            unset($chunk);
            unset($jsonChunk);
            gc_collect_cycles();
        }
        
        // Write closing bracket for JSON array
        fwrite($tempFile, ']');
        rewind($tempFile);
        
        $fileName = "Learnosity_{$endpoint}.json";
        $bucket->upload($tempFile, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);
        fclose($tempFile);

        error_log("Successfully uploaded site-specific data for endpoint: $endpoint to GCS");
    } catch (Exception $e) {
        error_log("Error uploading site-specific data to GCS for endpoint $endpoint: " . $e->getMessage());
    }
}

/**
 * Helper function for GCS upload to global folder
 * @param array $data The data to upload
 * @param object $bucket GCS bucket object
 * @param string $currentDate Current date in Ymd format
 * @param string $endpoint The Learnosity endpoint being processed
 * @return void
 */
function _uploadToGlobalGCS($data, $bucket, $currentDate, $endpoint)
{
    try {
        if (empty($data)) {
            error_log("No data to upload for endpoint: $endpoint");
            return;
        }

        // Create a temporary file for streaming
        $tempFile = tmpfile();
        
        // Write opening bracket for JSON array
        fwrite($tempFile, '[');
        
        // Process data in smaller chunks of 100 records
        $chunkSize = 100;
        $chunks = array_chunk($data, $chunkSize);
        $isFirstChunk = true;
        
        foreach ($chunks as $chunk) {
            if (!$isFirstChunk) {
                fwrite($tempFile, ',');
            }
            
            $jsonChunk = json_encode($chunk);
            if ($jsonChunk === false) {
                error_log("Failed to encode JSON chunk for endpoint: $endpoint");
                continue;
            }
            
            // Remove the opening and closing brackets from the chunk
            $jsonChunk = trim($jsonChunk, '[]');
            fwrite($tempFile, $jsonChunk);
            
            $isFirstChunk = false;
            
            // Free up memory
            unset($chunk);
            unset($jsonChunk);
            gc_collect_cycles();
        }
        
        // Write closing bracket for JSON array
        fwrite($tempFile, ']');
        rewind($tempFile);
        
        $fileName = "Learnosity_{$endpoint}.json";
        $bucket->upload($tempFile, [
            'name' => "$currentDate/filename/Learnosity_{$endpoint}/$fileName"
        ]);
        fclose($tempFile);

        error_log("Successfully uploaded global data for endpoint: $endpoint to GCS");
    } catch (Exception $e) {
        error_log("Error uploading global data to GCS for endpoint $endpoint: " . $e->getMessage());
    }
}

/**
 * Helper function to fetch paginated data from Learnosity API
 * @param object $dataApi Learnosity DataApi instance
 * @param string $uri API endpoint URI
 * @param array $securityPacket Security credentials
 * @param string $consumerSecret Consumer secret
 * @param string $mintime Minimum time to fetch data from
 * @param array $additionalParams Additional parameters for the API request
 * @return array Collected data
 */
function _fetchPaginatedData($dataApi, $uri, $securityPacket, $consumerSecret, $mintime, $additionalParams = [])
{
    $allData = [];
    $nextValue = "";
    $batchSize = 50;
    $maxRetries = 3;
    $retryDelay = 5; // seconds
    
    while(true) {
        $request = array_merge([
            'mintime' => $mintime,
            'limit' => $batchSize
        ], $additionalParams);
        
        if ($nextValue !== '') {
            $request['next'] = $nextValue;
        }

        $retryCount = 0;
        while ($retryCount < $maxRetries) {
            try {
                $result = $dataApi->request(
                    $uri,
                    $securityPacket,
                    $consumerSecret,
                    $request,
                    'get'
                );

                $response = $result->json();
                
                // Check for API errors
                if (isset($response['error'])) {
                    error_log("API Error for $uri: " . json_encode($response['error']));
                    throw new Exception("API Error: " . json_encode($response['error']));
                }
                
                // Check if zero records
                if (isset($response['meta']['records'])) {
                    $numberOfRecords = $response['meta']['records'];
                    error_log("Records found for $uri: $numberOfRecords");
                    if($numberOfRecords == 0) {
                        error_log("No records found for $uri, ending pagination");
                        break 2; // Break both loops
                    }
                }

                // Add data to collection
                if (isset($response['data']) && is_array($response['data'])) {
                    $allData = array_merge($allData, $response['data']);
                    error_log("Collected " . count($allData) . " records so far for $uri");
                }

                // Check for next page after processing current data
                if (isset($response['meta']['next'])) {
                    $nextValue = $response['meta']['next'];
                    error_log("Next page available for $uri");
                } else {
                    $nextValue = "";
                    error_log("No more pages for $uri");
                    break 2; // No more pages, exit both loops
                }

                // Add a small delay to avoid rate limiting
                usleep(100000); // 100ms delay
                break; // Success, exit retry loop

            } catch (Exception $e) {
                $retryCount++;
                if ($retryCount >= $maxRetries) {
                    error_log("Max retries reached for endpoint $uri: " . $e->getMessage());
                    break 2; // Break both loops
                }
                error_log("Retry $retryCount of $maxRetries for endpoint $uri: " . $e->getMessage());
                sleep($retryDelay);
            }
        }
    }
    
    error_log("Final record count for $uri: " . count($allData));
    return $allData;
}

/**
 * Main job function to process all Learnosity endpoints
 * @param object $db Database connection
 * @param string|null $siteID Site identifier (optional)
 * @param object $config Configuration object
 */
function runJob($db, $siteID = null, $config)
{
    $cronName = 'LearnosityFull';
    $startTime = time();
    $maxExecutionTime = 10800; // 3 hour timeout
    
    try {
        // Use a default siteID for logging if none provided
        $logSiteID = !empty($siteID) ? $siteID : 'global';
        $uuid = Logger::logCronStart($db, $logSiteID, $cronName);
        
        // Initialize Google Cloud Storage
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucket = $storage->bucket("prd-landing-zone");
        $currentDate = date("Ymd");

        // Get Learnosity credentials from config
        $consumerKey = $config->learnosityFull->consumerKey;
        $consumerSecret = $config->learnosityFull->consumerSecret;
        
        $securityPacket = [
            'consumer_key' => $consumerKey,
            'domain'       => 'abre.io'
        ];
        
        $dataApi = new DataApi();
        
        // Always use 2018-01-01 for GCS operations like regular Learnosity job
        $startDate = "2018-01-01";
        error_log("Starting GCS data collection from $startDate");
        
        // Define all endpoints to process
        $baseUrl = 'https://data.learnosity.com/v2023.1.LTS';
        $endpoints = [
            // Item Banks endpoints
            'activities'     => [
                'uri'    => "$baseUrl/itembank/activities",
                'params' => [],
            ],
            'items'          => [
                'uri'    => "$baseUrl/itembank/items",
                'params' => [],
            ],
            'questions'      => [
                'uri'    => "$baseUrl/itembank/questions",
                'params' => [],
            ],
            'tags'           => [
                'uri'    => "$baseUrl/itembank/tagging/tags",
                'params' => [
                    'reference_type' => 'academic_benchmarks'
                ],
            ],
            'tagHierarchies' => [
                'uri'    => "$baseUrl/itembank/tag_hierarchies",
                'params' => [],
            ],
            // Sessions endpoints
            'responses'      => [
                'uri'    => "$baseUrl/sessions/responses",
                'params' => [],
            ],
            'scores'         => [
                'uri'    => "$baseUrl/sessions/scores",
                'params' => [],
            ],
            'responseScores' => [
                'uri'    => "$baseUrl/sessions/responses/scores",
                'params' => [],
            ],
        ];
        
        // Process each endpoint
        foreach ($endpoints as $endpoint => $config) {
            // Check for timeout
            if (time() - $startTime > $maxExecutionTime) {
                throw new Exception("Job timed out after " . $maxExecutionTime . " seconds");
            }
            
            error_log("Starting to process endpoint: $endpoint");
            error_log("Using URI: " . $config['uri']);
            
            try {
                $data = _fetchPaginatedData(
                    $dataApi,
                    $config['uri'],
                    $securityPacket,
                    $consumerSecret,
                    $startDate,
                    $config['params']
                );
                
                if (!empty($data)) {
                    error_log("Successfully collected " . count($data) . " records for endpoint: $endpoint");
                    if (!empty($siteID)) {
                        _uploadToSiteGCS($data, $bucket, $currentDate, $siteID, $endpoint);
                    }
                    _uploadToGlobalGCS($data, $bucket, $currentDate, $endpoint);
                    error_log("Successfully processed endpoint: $endpoint");
                } else {
                    error_log("No valid data found for endpoint: $endpoint");
                }
            } catch (Exception $e) {
                error_log("Error processing endpoint $endpoint: " . $e->getMessage());
            }
            
            // Add delay between endpoints to avoid rate limiting
            sleep(1);
        }
        
    } catch(Exception $ex) {
        $error = $ex->getMessage();
    }
    
    // Log completion
    $details = [];
    if(isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }
    
    Logger::logCronFinish($db, $logSiteID, $cronName, $status, $details, $uuid);
}
