<?php
/*
* Copyright 2016-2024 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

use GuzzleHttp\Client;
use Google\Cloud\Storage\StorageClient;

function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID) {
    try {
        if (empty($data)) {
            error_log("No data to upload for type: $type");
            return;
        }

        // Convert data to JSON
        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log("Failed to encode JSON for type: $type");
            return;
        }

        // First upload
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);
        
        $fileName = "Renaissance_DNA_{$type}.json";
        $bucket->upload($tempFile1, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);
        fclose($tempFile1);

        // Second upload
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);
        
        $modifiedFile = "Renaissance_DNA_{$type}-{$siteID}.json";
        $bucket->upload($tempFile2, [
            'name' => "$currentDate/filename/Renaissance_DNA_{$type}/$modifiedFile"
        ]);
        fclose($tempFile2);

        error_log("Successfully uploaded $type data to GCS");
    } catch (Exception $e) {
        error_log("Error uploading $type data to GCS: " . $e->getMessage());
    }
}

function runJob($db, $siteID, $config)
{
    $cronName = 'Illuminate Renaissance DNA';

    try {
        //Configuration
        $uuid = Logger::logCronStart($db, $siteID, $cronName);
        $domain = $config->illuminate->domain;
        $token = $config->illuminate->clientID;
        $token_secret = $config->illuminate->clientSecret;
        $currentYear = date("Y");
        $currentDay = date("Y-m-d");
        $twoWeeksAgo = date("Y-m-d", strtotime("-2 weeks"));
        $previousYear = $currentYear - 1;
        $startDate = "$previousYear-07-01";

        // Initialize GCS
        $currentDate = date("Ymd");
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);

        // Initialize data collection arrays
        $assessmentsData = [];
        $assessmentAggregateStudentResponsesData = [];
        $assessmentAggregateStudentResponsesStandardData = [];
        $poolAssessmentAggregateStudentResponsesData = [];
        $poolAssessmentAggregateStudentResponsesStandardData = [];

        //Pre-Check: Check if there are any records in abre_illuminate_assessments_student_results_aggregate table
        $query = "SELECT COUNT(*) FROM abre_illuminate_assessments_student_results_aggregate WHERE site_id = $siteID";
        $result = $db->query($query);
        $count = $result->fetch_row()[0];
        if ($count > 0) {
            $modifiedAfter = $twoWeeksAgo;
        } else {
            $modifiedAfter = "$previousYear-07-01";
        }

        //FUNCTION - Get All Assessments
        function getApiData($domain, $accessToken, $endpoint, $page, $endparams)
        {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => "$domain/rest_server.php/Api/$endpoint/?page=$page&limit=1000$endparams",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Authorization: Bearer ' . $accessToken
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($response, true);
            return $response;
        }

        // Function to insert data into a table
        function insertData($db, $tableName, $fields)
        {
            // Prepare the insert statement
            $sql = "INSERT INTO $tableName (" . implode(", ", array_keys($fields)) . ") VALUES ";
            $placeholders = implode(", ", array_fill(0, count($fields), "?"));
            $sql .= "($placeholders) ON DUPLICATE KEY UPDATE ";
            $updates = [];
            foreach ($fields as $key => $value) {
                $updates[] = "$key = VALUES($key)";
            }
            $sql .= implode(", ", $updates);

            // SQL Statement
            $stmt = $db->stmt_init();
            $stmt->prepare($sql);

            // Bind the parameters
            $types = '';
            $values = array();
            foreach ($fields as $param) {
                $types .= $param['type'];
                if (isset($param['value'])) {
                    $values[] = $param['value'];
                } else {
                    $values[] = '';
                }
            }
            $bindParams = array($types);
            foreach ($values as $i => $value) {
                $bindParams[] = &$values[$i]; // Use references
            }
            call_user_func_array(array($stmt, 'bind_param'), $bindParams);

            // Execute the statement
            $stmt->execute();
            $stmt->close();
        }

        //Step 1: Get Access Token
        $client = new Client();
        $response = $client->request("POST", "$domain/?OAuth2_AccessToken", [
            "form_params" => [
                "client_id" => $token,
                "client_secret" => $token_secret,
                "grant_type" => "client_credentials",
            ]
        ]);
        if ($response->getStatusCode() == 200) {
            $accessTokenJSON = json_decode($response->getBody());
            $accessToken = $accessTokenJSON->access_token;
        } else {
            throw new Exception("Bad Illuminate Renaissance DNA API credentials");
        }

        //Step 2: Insert Assessments
        $totalPages = getApiData($domain, $accessToken, 'Assessments', 1, '');
        $totalPages = $totalPages['num_pages'];
        for ($x = 1; $x <= $totalPages; $x++) {
            $results = getApiData($domain, $accessToken, 'Assessments', $x, '');
            foreach ($results['results'] as $result) {

                // Define the fields to insert into the database
                $fields = [
                    'assessment_id' => ['value' => $result['assessment_id'], 'type' => 'i'],
                    'local_assessment_id' => ['value' => $result['local_assessment_id'], 'type' => 'i'],
                    'title' => ['value' => $result['title'], 'type' => 's'],
                    'description' => ['value' => $result['description'], 'type' => 's'],
                    'author_id' => ['value' => $result['author_id'], 'type' => 'i'],
                    'district_author_id' => ['value' => $result['district_author_id'], 'type' => 's'],
                    'author_first' => ['value' => $result['author_first'], 'type' => 's'],
                    'author_last' => ['value' => $result['author_last'], 'type' => 's'],
                    'created_at' => ['value' => $result['created_at'], 'type' => 's'],
                    'updated_at' => ['value' => $result['updated_at'], 'type' => 's'],
                    'scope_id' => ['value' => $result['scope_id'], 'type' => 'i'],
                    'scope' => ['value' => $result['scope'], 'type' => 's'],
                    'subject_id' => ['value' => $result['subject_id'], 'type' => 'i'],
                    'subject' => ['value' => $result['subject'], 'type' => 's'],
                    'grade_levels' => ['value' => $result['grade_levels'], 'type' => 's'],
                    'site_id' => ['value' => $siteID, 'type' => 'i']
                ];

                // Function to insert data into a table
                insertData($db, "abre_illuminate_assessments", $fields);

                // Add assessment to data collection
                $assessmentsData[] = $result;
            }
        }

        // Upload assessments data to GCS
        if (!empty($assessmentsData)) {
            _uploadToGCS($assessmentsData, 'assessments', $bucket, $currentDate, $siteID);
        }
        error_log("done with assessments");

        //Step 3: Insert Assessment Aggregate Student Responses
        $endparams = "&start_date=$startDate&modified_after=$modifiedAfter";
        $totalPages = getApiData($domain, $accessToken, 'AssessmentAggregateStudentResponses', 1, $endparams);
        $totalPages = $totalPages['num_pages'];
        for ($x = 1; $x <= $totalPages; $x++) {
            $results = getApiData($domain, $accessToken, 'AssessmentAggregateStudentResponses', $x, $endparams);
            foreach ($results['results'] as $result) {

                // Define the fields to insert into the database
                $fields = [
                    'assessment_id' => ['value' => $result['assessment_id'], 'type' => 'i'],
                    'title' => ['value' => $result['title'], 'type' => 's'],
                    'local_student_id' => ['value' => $result['local_student_id'], 'type' => 's'],
                    'first_name' => ['value' => $result['first_name'], 'type' => 's'],
                    'last_name' => ['value' => $result['last_name'], 'type' => 's'],
                    'middle_name' => ['value' => $result['middle_name'], 'type' => 's'],
                    'version' => ['value' => $result['version'], 'type' => 'i'],
                    'version_label' => ['value' => $result['version_label'], 'type' => 's'],
                    'date_taken' => ['value' => $result['date_taken'], 'type' => 's'],
                    'points' => ['value' => $result['points'], 'type' => 'i'],
                    'points_possible' => ['value' => $result['points_possible'], 'type' => 'i'],
                    'percent_correct' => ['value' => $result['percent_correct'], 'type' => 'i'],
                    'performance_band_level' => ['value' => $result['performance_band_level'], 'type' => 'i'],
                    'performance_band_label' => ['value' => $result['performance_band_label'], 'type' => 's'],
                    'mastered' => ['value' => $result['mastered'], 'type' => 's'],
                    'site_id' => ['value' => $siteID, 'type' => 'i']
                ];

                // Function to insert data into a table
                insertData($db, "abre_illuminate_assessments_student_results_aggregate", $fields);

                // Add response to data collection
                $assessmentAggregateStudentResponsesData[] = $result;
            }
        }

        // Upload assessment aggregate student responses data to GCS
        if (!empty($assessmentAggregateStudentResponsesData)) {
            _uploadToGCS($assessmentAggregateStudentResponsesData, 'assessment_aggregate_student_responses', $bucket, $currentDate, $siteID);
        }
        error_log("done with assessment aggregate student responses");

        //Step 4: Insert Assessment Aggregate Student Responses Standard
        $endparams = "&start_date=$startDate&modified_after=$modifiedAfter";
        $totalPages = getApiData($domain, $accessToken, 'AssessmentAggregateStudentResponsesStandard', 1, $endparams);
        $totalPages = $totalPages['num_pages'];
        for ($x = 1; $x <= $totalPages; $x++) {
            $results = getApiData($domain, $accessToken, 'AssessmentAggregateStudentResponsesStandard', $x, $endparams);
            foreach ($results['results'] as $result) {

                // Define the fields to insert into the database
                $fields = [
                    'assessment_id' => ['value' => $result['assessment_id'], 'type' => 'i'],
                    'title' => ['value' => $result['title'], 'type' => 's'],
                    'local_student_id' => ['value' => $result['local_student_id'], 'type' => 's'],
                    'first_name' => ['value' => $result['first_name'], 'type' => 's'],
                    'last_name' => ['value' => $result['last_name'], 'type' => 's'],
                    'middle_name' => ['value' => $result['middle_name'], 'type' => 's'],
                    'academic_benchmark_guid' => ['value' => $result['academic_benchmark_guid'], 'type' => 's'],
                    'standard_code' => ['value' => $result['standard_code'], 'type' => 's'],
                    'standard_description' => ['value' => $result['standard_description'], 'type' => 's'],
                    'date_taken' => ['value' => $result['date_taken'], 'type' => 's'],
                    'points' => ['value' => $result['points'], 'type' => 'i'],
                    'points_possible' => ['value' => $result['points_possible'], 'type' => 'i'],
                    'percent_correct' => ['value' => $result['percent_correct'], 'type' => 'i'],
                    'performance_band_level' => ['value' => $result['performance_band_level'], 'type' => 'i'],
                    'performance_band_label' => ['value' => $result['performance_band_label'], 'type' => 's'],
                    'mastered' => ['value' => $result['mastered'], 'type' => 's'],
                    'site_id' => ['value' => $siteID, 'type' => 'i']
                ];

                // Function to insert data into a table
                insertData($db, "abre_illuminate_assessments_aggregate_student_responses_standard", $fields);

                // Add response to data collection
                $assessmentAggregateStudentResponsesStandardData[] = $result;
            }
        }

        // Upload assessment aggregate student responses standard data to GCS
        if (!empty($assessmentAggregateStudentResponsesStandardData)) {
            _uploadToGCS($assessmentAggregateStudentResponsesStandardData, 'assessment_aggregate_student_responses_standard', $bucket, $currentDate, $siteID);
        }
        error_log("done with assessment aggregate student responses standard");

        //Step 5: Insert Assessment Pool Assessment Aggregate Student Responses
        $endparams = "&start_date=$startDate&modified_after=$modifiedAfter";
        $totalPages = getApiData($domain, $accessToken, 'PoolAssessmentAggregateStudentResponses', 1, $endparams);
        $totalPages = $totalPages['num_pages'];
        for ($x = 1; $x <= $totalPages; $x++) {
            $results = getApiData($domain, $accessToken, 'PoolAssessmentAggregateStudentResponses', $x, $endparams);
            foreach ($results['results'] as $result) {

                // Define the fields to insert into the database
                $fields = [
                    'pool_assessment_id' => ['value' => $result['pool_assessment_id'], 'type' => 'i'],
                    'title' => ['value' => $result['title'], 'type' => 's'],
                    'local_student_id' => ['value' => $result['local_student_id'], 'type' => 's'],
                    'first_name' => ['value' => $result['first_name'], 'type' => 's'],
                    'last_name' => ['value' => $result['last_name'], 'type' => 's'],
                    'middle_name' => ['value' => $result['middle_name'], 'type' => 's'],
                    'date_taken' => ['value' => $result['date_taken'], 'type' => 's'],
                    'points' => ['value' => $result['points'], 'type' => 'i'],
                    'points_possible' => ['value' => $result['points_possible'], 'type' => 'i'],
                    //Appears to be a typo "round" on the API
                    'percent_correct' => ['value' => $result['round'], 'type' => 'i'],
                    'performance_band_level' => ['value' => $result['performance_band_level'], 'type' => 'i'],
                    'performance_band_label' => ['value' => $result['performance_band_label'], 'type' => 's'],
                    'mastered' => ['value' => $result['mastered'], 'type' => 's'],
                    'start_time' => ['value' => $result['start_time'], 'type' => 's'],
                    'end_time' => ['value' => $result['end_time'], 'type' => 's'],
                    'minutes_elapsed' => ['value' => $result['minutes_elapsed'], 'type' => 'i'],
                    'student_pauses' => ['value' => $result['student_pauses'], 'type' => 'i'],
                    'site_id' => ['value' => $siteID, 'type' => 'i']
                ];

                // Function to insert data into a table
                insertData($db, "abre_illuminate_pool_assessment_aggregate_student_responses", $fields);

                // Add response to data collection
                $poolAssessmentAggregateStudentResponsesData[] = $result;
            }
        }

        // Upload pool assessment aggregate student responses data to GCS
        if (!empty($poolAssessmentAggregateStudentResponsesData)) {
            _uploadToGCS($poolAssessmentAggregateStudentResponsesData, 'pool_assessment_aggregate_student_responses', $bucket, $currentDate, $siteID);
        }
        error_log("done with pool assessment aggregate student responses");

        //Step 6: Insert Assessment Pool Assessment Aggregate Student Responses Standard
        $endparams = "&start_date=$startDate&modified_after=$modifiedAfter";
        $totalPages = getApiData($domain, $accessToken, 'PoolAssessmentAggregateStudentResponsesStandard', 1, $endparams);
        $totalPages = $totalPages['num_pages'];
        for ($x = 1; $x <= $totalPages; $x++) {
            $results = getApiData($domain, $accessToken, 'PoolAssessmentAggregateStudentResponsesStandard', $x, $endparams);
            foreach ($results['results'] as $result) {

                // Define the fields to insert into the database
                $fields = [
                    'pool_assessment_id' => ['value' => $result['pool_assessment_id'], 'type' => 'i'],
                    'title' => ['value' => $result['title'], 'type' => 's'],
                    'local_student_id' => ['value' => $result['local_student_id'], 'type' => 's'],
                    'first_name' => ['value' => $result['first_name'], 'type' => 's'],
                    'last_name' => ['value' => $result['last_name'], 'type' => 's'],
                    'middle_name' => ['value' => $result['middle_name'], 'type' => 's'],
                    'academic_benchmark_guid' => ['value' => $result['academic_benchmark_guid'], 'type' => 's'],
                    'standard_code' => ['value' => $result['standard_code'], 'type' => 's'],
                    'standard_description' => ['value' => $result['standard_description'], 'type' => 's'],
                    'date_taken' => ['value' => $result['date_taken'], 'type' => 's'],
                    'points' => ['value' => $result['points'], 'type' => 'i'],
                    'points_possible' => ['value' => $result['points_possible'], 'type' => 'i'],
                    'percent_correct' => ['value' => $result['percent_correct'], 'type' => 'i'],
                    'performance_band_level' => ['value' => $result['performance_band_level'], 'type' => 'i'],
                    'performance_band_label' => ['value' => $result['performance_band_label'], 'type' => 's'],
                    'mastered' => ['value' => $result['mastered'], 'type' => 's'],
                    'site_id' => ['value' => $siteID, 'type' => 'i']
                ];

                // Function to insert data into a table
                insertData($db, "abre_illuminate_pool_assessment_aggregate_student_responses_stan", $fields);

                // Add response to data collection
                $poolAssessmentAggregateStudentResponsesStandardData[] = $result;
            }
        }

        // Upload pool assessment aggregate student responses standard data to GCS
        if (!empty($poolAssessmentAggregateStudentResponsesStandardData)) {
            _uploadToGCS($poolAssessmentAggregateStudentResponsesStandardData, 'pool_assessment_aggregate_student_responses_standard', $bucket, $currentDate, $siteID);
        }
        error_log("done with pool assessment aggregate student responses standard");

    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }
    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
