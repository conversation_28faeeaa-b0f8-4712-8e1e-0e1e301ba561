<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');

/**
 * @deprecated
 */
function runJob($db, $siteID, $config){
  try {
    $uuid = Logger::logCronStart($db, $siteID, 'IEPAnywhere Url');
    if (!$siteID) {
      $siteID = 0;
      Logger::logCronFinish($db, $siteID, 'IEPAnywhere Url', CRON_FAILURE, ["error" => "no siteID provided"], $uuid);
      exit();
    }
    $username = $config->iepanywhere->userName;
    $keyPath = $config->iepanywhere->keyPath;

    chdir('/opt/cron-js');
    exec("npm install");
    $command = "node abre_iepanywhere_url.js {$siteID} {$username} '{$keyPath}'";
    $output = [];
    exec($command, $output);
    $finalResult = json_decode($output[0]);
  } catch (Exception $e) {
    Logger::logCronFinish($db, $siteID, 'IEPAnywhere Url', CRON_FAILURE, ["error" => $e], $uuid);
    exit();
  }

  chdir(dirname(__FILE__));
  $status = $finalResult->success ? CRON_SUCCESS : CRON_FAILURE;
  Logger::logCronFinish($db, $siteID, 'IEPAnywhere Url', $status, $finalResult, $uuid);
}
?>
