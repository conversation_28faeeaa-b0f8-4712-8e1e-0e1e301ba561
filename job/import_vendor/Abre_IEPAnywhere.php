<?php
/*
* Copyright 2016-2023 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../../vendor/autoload.php');
require_once(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Crypt\RSA;
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config){
  try{

    $uuid = Logger::logCronStart($db, $siteID, 'IEPAnywhere Url');

    define("SAFE_COLUMN_COUNT", 5);
    define("MAX_IMPORT_LIMIT", 25);

    $error = null;
    $skip = null;
    $separator = "\r\n";

    $sftp = new SFTP("upload.iepanywhere.com");
    $key = new RSA();
    $key->loadKey(file_get_contents('/opt/cron-js/'.$config->iepanywhere->keyPath));
    if(!$sftp->login($config->iepanywhere->userName, $key)) {
      throw new Exception("Login to SFTP failed.");
    }

    $directory = '/download/docviewer/';
    $directories = $sftp->nlist($directory);

    // Filter out "." and ".."
    $filteredDirectories = array_filter($directories, function($dir) {
        return $dir !== '.' && $dir !== '..';
    });

    // Sort the directories by filename in descending order
    rsort($filteredDirectories);

    //Get current directory
    if (!empty($filteredDirectories)) {

        $firstDirectory = reset($filteredDirectories);
        $cronFile = "/download/docviewer/$firstDirectory/documents.csv";
  	    $cronFile = $sftp->get($cronFile);

      	if(!$cronFile){
          $skip = true;
          throw new Exception("No file found.");
        }else{
          $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, ",");
          if($fileDetails["isEmpty"]){
            $skip = true;
            throw new Exception("File is empty.");
          }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
            $skip = true;
            throw new Exception("File only contains a header row.");
          }elseif(!$fileDetails["hasValidDataRow"]){
            throw new Exception("No valid data row found.");
          }
        }

        $rowCounter = 0;
        $valuesToImport = [];
        $dbColumns = "INSERT INTO iepanywhere_link (
          StudentID, Form, RealURL, AbreURL, Active, siteID
         ) VALUES ";

        // Upload SFTP File to Google Cloud Storage Bucket
        $currentDate = date("Ymd");
        $fileName = "Abre_IEPAnywhere.csv";
        $storage = new StorageClient(['projectId' => "abre-production"]);
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($cronFile, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);

        // Upload to the other folder location in landing zone bucket
        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        $folderName = pathinfo($fileName, PATHINFO_FILENAME);
        $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
        $bucketName = "prd-landing-zone";
        $bucket = $storage->bucket($bucketName);
        $bucket->upload($cronFile, [
            'name' => "$currentDate/filename/$folderName/$modifiedFile"
        ]);

        $line = strtok($cronFile, $separator);
        $line = strtok($separator); //skip header row

        $db->query("DELETE FROM iepanywhere_link WHERE siteID = $siteID");
      	do{
          $data = str_getcsv($line, ",");

      		if(count($data) >= SAFE_COLUMN_COUNT){
            $rowCounter++;

      			$studentId = trim($db->escape_string($data[0]));
            $form = trim($db->escape_string($data[1]));
            $startDate = trim($db->escape_string($data[2]));
            $endDate = trim($db->escape_string($data[3]));
            $currentDate = date('Y-m-d');
            $startTimestamp = strtotime($startDate);
            $endTimestamp = strtotime($endDate);
            $currentTimestamp = strtotime($currentDate);

            if ($currentTimestamp >= $startTimestamp && $currentTimestamp <= $endTimestamp) {
                $active = '1';
            } else {
                $active = '0';
            }
            $realUrl = $data[4];
            preg_match('/href="([^"]+)"/', $realUrl, $matches);
            if (isset($matches[1])) {
                $realUrl = $matches[1];
            }
            $abreHashString = $studentId . $form . $siteID;
            $abreUrl = $studentId . '-' . $siteID . '-' . hash('sha256', $abreHashString);

            $valuesToImport []= "('$studentId', '$form', '$realUrl', '$abreUrl', '$active', $siteID)";

      			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
              insertRows($db, $dbColumns, $valuesToImport);
              $valuesToImport = [];
      			}
      		}

      		$line = strtok($separator);
        }while($line !== false);

        if(count($valuesToImport)){
          insertRows($db, $dbColumns, $valuesToImport);
        }

    }

  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $cronName = 'IEPAnywhere Url';
  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
