<?php

/*
* Copyright (c) Abre.io Inc.
* All rights reserved.
*/

require_once __DIR__  . '/../../vendor/autoload.php';
require_once __DIR__  . '/../utils/functions.php';
require_once __DIR__  . '/../utils/logging.php';

use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteId, $config)
{
    $cronName = 'AIMSweb Import';

    try {
        $uuid = Logger::logCronStart($db, $siteId, $cronName);

        define("SAFE_COLUMN_COUNT", 10);
        define("MAX_IMPORT_LIMIT", 25);
        $error = null;
        $skip = null;
        $separator = "\r\n";

        // Connect
        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.\n" . implode(', ', $sftp->getErrors()));
        }

        // File
        // Get the list of files in the directory
        $dir = '.';
        $extension = 'txt';
        $files = $sftp->nlist($dir);
        $files = array_filter($files, function ($file) use ($sftp, $dir, $extension) {
            return pathinfo($file, PATHINFO_EXTENSION) === $extension && $sftp->is_file("$dir/$file");
        });
        if (empty($files)) {
            throw new Exception("No CSV files found in the $dir directory.");
        }
        usort($files, function ($a, $b) use ($sftp, $dir) {
            return $b - $a;
        });
        $fileName = $files[0];

        // Get the file name without the extension
        // $fileName = str_replace('.csv', '', $fileName);
        $fileName = preg_replace('/\.(csv|txt)$/', '', $fileName);
        $fileName = "$dir/$fileName";

        // Find File
        $cronFile = $sftp->get("$fileName.$extension");
        if (!$cronFile) {
            $cronFile = $sftp->get("$fileName.csv");
            $fileType = "csv";
            $columnSeparator = ",";
        } else {
            $cronFile = $sftp->get("$fileName.txt");
            $fileType = "txt";
            $columnSeparator = "\t";
        }

        $cronFile = $sftp->get("$fileName.$extension");

        if (!$cronFile) {
            $skip = true;
            throw new Exception("No file found.");
        } else {
            $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
            if ($fileDetails["isEmpty"]) {
                $skip = true;
                throw new Exception("File is empty.");
            } elseif ($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]) {
                $skip = true;
                throw new Exception("File only contains a header row.");
            } elseif (!$fileDetails["hasValidDataRow"]) {
                throw new Exception("No valid data row found.");
            }

            // Upload SFTP File to Google Cloud Storage Bucket
            $currentDate = date("Ymd");
            $gcsFileName = "Abre_AIMSweb.$fileType";
            $storage = new StorageClient(['projectId' => "abre-production"]);
            $bucketName = "prd-landing-zone";
            $bucket = $storage->bucket($bucketName);
            $bucket->upload($cronFile, [
                'name' => "$currentDate/site-id/$siteId/$gcsFileName"
            ]);

            // Upload to the other folder location in landing zone bucket
            $fileExtension = pathinfo($gcsFileName, PATHINFO_EXTENSION);
            $folderName = pathinfo($gcsFileName, PATHINFO_FILENAME);
            $modifiedFile = $folderName . "-" . $siteId . "." . $fileExtension;
            $bucket->upload($cronFile, [
                'name' => "$currentDate/filename/$folderName/$modifiedFile"
            ]);
        }

        $fileToDbColumnMapping = [
            'Districtuid'          => 'district_uid',
            'DistrictName'         => 'district_name',
            'Schooluid'            => 'school_uid',
            'SchoolName'           => 'school_name',
            'ClassUid'             => 'class_uid',
            'ClassName'            => 'class_name',
            'Teacheruid'           => 'teacher_uid',
            'TeacherFirstName'     => 'teacher_first_name',
            'TeacherMiddleName'    => 'teacher_middle_name',
            'TeacherLastName'      => 'teacher_last_name',
            'Studentuid'           => 'studentID',
            'SSID'                 => 'SSID',
            'SourceID'             => false,
            'StudentFirstName'     => 'student_first_name',
            'StudentMiddleName'    => 'student_middle_name',
            'StudentLastName'      => 'student_last_name',
            'StudentGrade'         => 'student_grade',
            'Period'               => 'period',
            'Type'                 => 'type',
            'IsBatteryScore'       => 'is_battery_score',
            'RelatedForm'          => 'related_form',
            'MeasureName'          => 'measure_name',
            'MeasureGrade'         => 'measure_grade',
            'FormCode'             => 'form_code',
            'Score'                => 'score',
            'VerticalScore'        => 'vertical_score',
            'Errors'               => 'errors',
            'Accuracy'             => 'accuracy',
            'ScaledScore'          => false,
            'CSEM'                 => false,
            'AdministrationDate'   => 'administration_date',
            'SchoolYear'           => 'school_year',
            'OffGrade'             => 'off_grade',
            'NormGroup'            => 'norm_group',
            'Lexile'               => 'lexile',
            'Quantile'             => 'quantile',
            'FallWinRoi'           => 'fall_winter_roi',
            'WinSprRoi'            => 'winter_spring_roi',
            'FallSprRoi'           => 'fall_spring_roi',
            'FallWinSgp'           => 'fall_winter_sgp',
            'WinSprSgp'            => 'winter_spring_sgp',
            'FallSprSgp'           => 'fall_spring_sgp',
            'Natl'                 => 'national_percentile',
            'Dist'                 => 'district_percentile',
            'Sch'                  => 'school_percentile',
            'RiskStatus'           => 'risk_status',
            'Probability'          => false,
            'F-Index'              => false,
            'ConsistencyIndex'     => false,
            'ResponsePatternIndex' => false,
        ];

        $numbers = [
            'fall_winter_roi',
            'winter_spring_roi',
            'fall_spring_roi',
            'fall_winter_sgp',
            'winter_spring_sgp',
            'fall_spring_sgp',
        ];

        $currentSchoolYearId = getCurrentSchoolYearID($db);

        $tableName = 'Abre_AIMSweb';
        $deleteSql = "
            DELETE FROM `$tableName`
            WHERE `siteID` = $siteId
            AND `school_year_id` = $currentSchoolYearId";
        $db->query($deleteSql);

        $dbColumns = [];
        foreach ($fileToDbColumnMapping as $value) {
            if ($value) {
                $dbColumns[] = $value;
            }
        }
        $dbColumns[] = 'siteID';
        $dbColumns[] = 'school_year_id';
        $dbColumns[] = 'last_modified_cdc';

        $dbColumns = "INSERT INTO `$tableName` (`"
            . implode("`, `", $dbColumns)
            . "`) VALUES ";

        $rowCounter = 0;
        $valuesToImport = [];

        // Skip header row
        $line = strtok($cronFile, $separator);
        // Get ths first line, subsequent calls to strtok do not need the $separator param
        $line = strtok($separator);
        $lines = 0;
        do {
            // Split Columns
            $data = str_getcsv($line, $columnSeparator);

            $escapedData = array_map(function ($datum) use ($db) {
                return trim($db->escape_string($datum));
            }, $data);

            $fileHeaderValuePairs = array_combine(array_keys($fileToDbColumnMapping), $escapedData);
            $insertColumnsAndValues = [];
            foreach ($fileToDbColumnMapping as $header => $column) {
                if (!$column) {
                    continue;
                }

                $value = $fileHeaderValuePairs[$header];

                if (in_array($column, $numbers, true)) {
                    $value = $value === '' ? '0' : $value;
                } else {
                    $value = "'$value'";
                }

                $insertColumnsAndValues[$column] = $value;
            }

            $insertColumnsAndValues['siteID'] = $siteId;
            $insertColumnsAndValues['school_year_id'] = $currentSchoolYearId;
            $insertColumnsAndValues['last_modified_cdc'] = "'" . date('Y-m-d H:i:s') . "'";

            if (count($escapedData) >= SAFE_COLUMN_COUNT) {
                $rowCounter++;
                $valuesToImport[] = "(" . implode(", ", array_values($insertColumnsAndValues)) . ")";
            }

            if (count($valuesToImport) === MAX_IMPORT_LIMIT) {
                insertRows($db, $dbColumns, $valuesToImport);
                $valuesToImport = [];
            }

            $line = strtok($separator);
            $lines++;
        } while ($line !== false);

        if (count($valuesToImport)) {
            insertRows($db, $dbColumns, $valuesToImport);
        }
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];

    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        // Delete all older files on the SFTP server (except for most recent)
        foreach (array_slice($files, 1) as $oldFile) {
            $sftp->delete("$dir/$oldFile");
        }

        $details = [
            "rowsInserted" => $rowCounter,
            "lines" => $lines,
        ];
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteId, $cronName, $status, $details, $uuid);
}
