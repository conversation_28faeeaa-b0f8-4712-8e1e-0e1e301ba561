<?php

require(dirname(__FILE__) . '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
require(dirname(__FILE__) . '/../../vendor/autoload.php');

use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config){

  $cronName = 'CogAT Import v1.0';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    $sftp = new SFTP($config->sftp->ip);
    if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
      throw new Exception("Login to SFTP failed.");
    }
    $localTempFile = tempnam(sys_get_temp_dir(), 'cogat');
    $sftp->get('Abre_Cogat_v1.0.txt', $localTempFile);
    importCogATTestData($db, $siteID, $localTempFile);
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  // gather results
  $details = [];

  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}


function importCogATTestData($db, $siteID, $tempFile){
  try{
    $temporaryTableName = createTemporaryTable($db);
    loadDataIntoTemporaryTable($tempFile, $db, $temporaryTableName);
    insertTemporaryTable($siteID, $db, $temporaryTableName);
  }finally{
    if(isset($temporaryTableName)){
      dropTemporaryTable($db, $temporaryTableName);
    }
  }
}

function createTemporaryTable($db){
  $tableCreated = false;
  $tries = 0;
  while(!$tableCreated && $tries < 3){
    $tries++;
    $temporaryTableName = "temp" . uniqid();
    $createTemporaryTableSQL = "CREATE TABLE {$temporaryTableName} (
      `student_id` char(32) NOT NULL,
      `test_date` date NOT NULL,
      `verbal_universal_scale_score` smallint(6) DEFAULT NULL,
      `quantitative_universal_scale_score` smallint(6) DEFAULT NULL,
      `nonverbal_universal_scale_score` smallint(6) DEFAULT NULL,
      `composite_universal_scale_score` smallint(6) DEFAULT NULL,
      `verbal_standard_age_score` smallint(6) DEFAULT NULL,
      `quantitative_standard_age_score` smallint(6) DEFAULT NULL,
      `nonverbal_standard_age_score` smallint(6) DEFAULT NULL,
      `composite_standard_age_score` smallint(6) DEFAULT NULL,
      `verbal_grade_percentile_rank` smallint(6) DEFAULT NULL,
      `quantitative_grade_percentile_rank` smallint(6) DEFAULT NULL,
      `nonverbal_grade_percentile_rank` smallint(6) DEFAULT NULL,
      `composite_grade_percentile_rank` smallint(6) DEFAULT NULL,
      `verbal_age_percentile_rank` smallint(6) DEFAULT NULL,
      `quantitative_age_percentile_rank` smallint(6) DEFAULT NULL,
      `nonverbal_age_percentile_rank` smallint(6) DEFAULT NULL,
      `composite_age_percentile_rank` smallint(6) DEFAULT NULL,
      `ability_profile` varchar(15) default null
    )";
    $stmt = $db->stmt_init();
    $stmt->prepare($createTemporaryTableSQL);
    $tableCreated = $stmt->execute();
    $stmt->close();
  }
  return $temporaryTableName;
}

function loadDataIntoTemporaryTable($fileName, $db, $temporaryTableName){
  $temporaryTableInsertSQL = "
    INSERT INTO {$temporaryTableName}
    (
      student_id,
      test_date,
      verbal_universal_scale_score,
      quantitative_universal_scale_score,
      nonverbal_universal_scale_score,
      composite_universal_scale_score,
      verbal_standard_age_score,
      quantitative_standard_age_score,
      nonverbal_standard_age_score,
      composite_standard_age_score,
      verbal_grade_percentile_rank,
      quantitative_grade_percentile_rank,
      nonverbal_grade_percentile_rank,
      composite_grade_percentile_rank,
      verbal_age_percentile_rank,
      quantitative_age_percentile_rank,
      nonverbal_age_percentile_rank,
      composite_age_percentile_rank,
      ability_profile
    )
    VALUES";

  $handle = fopen($fileName, "r");
  if($handle === false){
    throw new Exception("Cannot read CSV file");
  }
  if(fgets($handle) === false){ // header line
    throw new Exception("file is empty");
  }

  //concatenate all records
  $records = array();
  while(($line = fgets($handle)) !== false){
    $lineArray = str_getcsv($line, "\t");
    $studentID = trim($lineArray[16]);
    if($studentID != ""){
      array_push($records, buildInsertLine($studentID, $lineArray));
    }
  }
  $temporaryTableInsertSQL .= implode(",", $records) . ";";

  $stmt = $db->stmt_init();
  $stmt->prepare($temporaryTableInsertSQL);
  $stmt->execute();
  $stmt->close();
}

function buildInsertLine($studentID, $line){
  if(trim($line[30]) == ""){
    $testDate = "NULL";
  }else{
    $testDate = trim($line[30]);
    $month = substr($testDate, 0, 2);
    $day = substr($testDate, 2, 2);
    $year = substr($testDate, 4, 4);
    $testDate = "{$year}-{$month}-{$day}";
  }

  $verbalUniversalScaleScore = nullCheck($line[180]);
  $quantitativeUniversalScaleScore = nullCheck($line[181]);
  $nonverbalUniversalScaleScore = nullCheck($line[182]);
  $compositeUniversalScaleScore = nullCheck($line[186]);

  $verbalStandardAgeScore = nullCheck($line[210]);
  $quantitativeStandardAgeScore = nullCheck($line[211]);
  $nonverbalStandardAgeScore = nullCheck($line[212]);
  $compositeStandardAgeScore = nullCheck($line[216]);

  $verbalGradePercentileRank = nullCheck($line[240]);
  $quantitativeGradePercentileRank = nullCheck($line[241]);
  $nonverbalGradePercentileRank = nullCheck($line[242]);
  $compositeGradePercentileRank = nullCheck($line[246]);

  $verbalAgePercentileRank = nullCheck($line[270]);
  $quantitativeAgePercentileRank = nullCheck($line[271]);
  $nonverbalAgePercentileRank = nullCheck($line[272]);
  $compositeAgePercentileRank = nullCheck($line[276]);

  $abilityProfile = nullCheckWithQuotes($line[480]);

  $sql = "
    (
      '$studentID',
      '$testDate',

      $verbalUniversalScaleScore,
      $quantitativeUniversalScaleScore,
      $nonverbalUniversalScaleScore,
      $compositeUniversalScaleScore,

      $verbalStandardAgeScore,
      $quantitativeStandardAgeScore,
      $nonverbalStandardAgeScore,
      $compositeStandardAgeScore,

      $verbalGradePercentileRank,
      $quantitativeGradePercentileRank,
      $nonverbalGradePercentileRank,
      $compositeGradePercentileRank,

      $verbalAgePercentileRank,
      $quantitativeAgePercentileRank,
      $nonverbalAgePercentileRank,
      $compositeAgePercentileRank,

      $abilityProfile
    )";
  return $sql;
}

function insertTemporaryTable($siteID, $db, $temporaryTableName){
  $replaceIntoSQL = "
    REPLACE INTO abre_cogat
    (
      site_id,
      student_id,
      test_date,
      verbal_universal_scale_score,
      quantitative_universal_scale_score,
      nonverbal_universal_scale_score,
      composite_universal_scale_score,
      verbal_standard_age_score,
      quantitative_standard_age_score,
      nonverbal_standard_age_score,
      composite_standard_age_score,
      verbal_grade_percentile_rank,
      quantitative_grade_percentile_rank,
      nonverbal_grade_percentile_rank,
      composite_grade_percentile_rank,
      verbal_age_percentile_rank,
      quantitative_age_percentile_rank,
      nonverbal_age_percentile_rank,
      composite_age_percentile_rank,
      ability_profile
    )
    SELECT
      ?,
      student_id,
      test_date,
      verbal_universal_scale_score,
      quantitative_universal_scale_score,
      nonverbal_universal_scale_score,
      composite_universal_scale_score,
      verbal_standard_age_score,
      quantitative_standard_age_score,
      nonverbal_standard_age_score,
      composite_standard_age_score,
      verbal_grade_percentile_rank,
      quantitative_grade_percentile_rank,
      nonverbal_grade_percentile_rank,
      composite_grade_percentile_rank,
      verbal_age_percentile_rank,
      quantitative_age_percentile_rank,
      nonverbal_age_percentile_rank,
      composite_age_percentile_rank,
      ability_profile
    FROM {$temporaryTableName};
  ";
  $stmt = $db->stmt_init();
  $stmt->prepare($replaceIntoSQL);
  $stmt->bind_param('i', $siteID);
  $stmt->execute();
  $stmt->close();
}

function dropTemporaryTable($db, $temporaryTableName){
  $dropTemporaryTableSQL = "DROP TABLE $temporaryTableName";
  $stmt = $db->stmt_init();
  $stmt->prepare($dropTemporaryTableSQL);
  $stmt->execute();
  $stmt->close();
}
?>
