<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use Google\Cloud\Storage\StorageClient;

/*
  Documentation for available MasteryConnect endpoints
  https://docs.google.com/document/d/19TxyeRzF6nyJDY6ej2dZozbE87JdUygw/edit?pli=1
*/

// Helper function for GCS upload - moved outside runJob
function _uploadToGCS($data, $type, $bucket, $currentDate, $siteID) {
    try {
        if (empty($data)) {
            error_log("No data to upload for type: $type");
            return;
        }

        // Convert data to JSON
        $jsonEncoded = json_encode($data);
        if ($jsonEncoded === false) {
            error_log("Failed to encode JSON for type: $type");
            return;
        }

        // First upload
        $tempFile1 = tmpfile();
        fwrite($tempFile1, $jsonEncoded);
        rewind($tempFile1);
        
        $fileName = "Abre_MasteryConnect_{$type}.json";
        $bucket->upload($tempFile1, [
            'name' => "$currentDate/site-id/$siteID/$fileName"
        ]);
        fclose($tempFile1);

        // Second upload
        $tempFile2 = tmpfile();
        fwrite($tempFile2, $jsonEncoded);
        rewind($tempFile2);
        
        $folderName = "Abre_MasteryConnect_{$type}";
        $bucket->upload($tempFile2, [
            'name' => "$currentDate/filename/$folderName/$folderName-{$siteID}.json"
        ]);
        fclose($tempFile2);

        error_log("Successfully uploaded $type data to GCS");
    } catch (Exception $e) {
        error_log("Error uploading $type data to GCS: " . $e->getMessage());
    }
}

function runJob($db, $siteID, $config){

  $cronName = 'MasteryConnect';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    $frameworksEnabled = 1;
    $standardsEnabled = 1;
    $teachersEnabled = 1;
    $studentsEnabled = 1;
    $assessmentsEnabled = 1;
    $scoresEnabled = 1;

    $consumerKey = $config->masteryconnect->consumerKey;
    $consumerSecret = $config->masteryconnect->consumerSecret;
    $accessToken = $config->masteryconnect->accessToken;
    $accessTokenSecret = $config->masteryconnect->accessTokenSecret;

    // Initialize Google Cloud Storage
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $currentDate = date("Ymd");

    // Initialize arrays to store data for GCS upload
    $frameworksData = [];
    $standardsData = [];
    $teachersData = [];
    $studentsData = [];
    $assessmentsData = [];
    $scoresData = [];

    /*
      GET FRAMEWORKS
    */

    if($frameworksEnabled){
      $url = 'https://app.masteryconnect.com/api/frameworks.json';
      $queryParams = [];

      // Generate OAuth nonce and timestamp
      $oauthNonce = md5(uniqid(rand(), true));
      $oauthTimestamp = time();

      // Generate OAuth signature
      $oauthParams = [
          'oauth_consumer_key' => $consumerKey,
          'oauth_nonce' => $oauthNonce,
          'oauth_signature_method' => 'HMAC-SHA1',
          'oauth_timestamp' => $oauthTimestamp,
          'oauth_token' => $accessToken,
          'oauth_version' => '1.0',
      ];
      ksort($oauthParams);

      $baseString = 'GET&' . rawurlencode($url) . '&' . rawurlencode(http_build_query(array_merge($oauthParams, $queryParams)));
      $signingKey = rawurlencode($consumerSecret) . '&' . rawurlencode($accessTokenSecret);
      $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

      // Prepare cURL URL with query parameters
      $url .= '?' . http_build_query($queryParams);

      // Prepare cURL headers
      $headers = [
          'Authorization: OAuth oauth_consumer_key="' . $consumerKey . '",'
              . 'oauth_nonce="' . $oauthNonce . '",'
              . 'oauth_signature="' . rawurlencode($oauthSignature) . '",'
              . 'oauth_signature_method="HMAC-SHA1",'
              . 'oauth_timestamp="' . $oauthTimestamp . '",'
              . 'oauth_token="' . $accessToken . '",'
              . 'oauth_version="1.0"',
      ];

      // Initialize cURL session and set options
      $curl = curl_init();
      curl_setopt($curl, CURLOPT_URL, $url);
      curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($curl, CURLOPT_ENCODING, '');
      curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
      curl_setopt($curl, CURLOPT_TIMEOUT, 0);
      curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
      curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
      curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
      //curl_setopt($curl, CURLOPT_VERBOSE, true);
      curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

      // Execute the request
      $json = curl_exec($curl);

      // Loop Through Response and Insert Into DB
      $response = json_decode($json, TRUE);
      $stmt = $db->stmt_init();
      if(!empty($response)){
        foreach($response as $item){

            $frameworkId = $item['id'];
            $pathwayName = $item['pathway_name'];
            $subject = $item['subject'];

            $sql = "INSERT INTO abre_masteryconnect_frameworks (
                                        framework_id,
                                        pathway_name,
                                        subject
                                      )
                                      VALUES
                                      (?, ?, ?)";
            $stmt->prepare($sql);
            $stmt->bind_param("iss",
                                  $frameworkId,
                                  $pathwayName,
                                  $subject
                              );
            $stmt->execute();

            $frameworksData[] = $item; // Add item to frameworks data array
        }
        $stmt->close();
        curl_close($curl);

        // Upload frameworks data to GCS
        if (!empty($frameworksData)) {
          _uploadToGCS($frameworksData, 'frameworks', $bucket, $currentDate, $siteID);
        }
      }
    }

    /*
      GET STANDARDS
    */

    if($standardsEnabled){
      $frameworksToSync = "";
      $stmt = $db->stmt_init();
      $sql = "SELECT framework_id FROM abre_masteryconnect_frameworks WHERE sync = 1";
      $stmt->prepare($sql);
      $stmt->execute();
      $stmt->bind_result($frameworkId);
      while($stmt->fetch()){
        $frameworksToSync = $frameworksToSync . $frameworkId . ',';
      }
      $frameworksToSync = rtrim($frameworksToSync, ',');
      $stmt->close();

      // API endpoint
      $db->query("DELETE FROM abre_masteryconnect_standards");
      $frameworksToSyncArray = explode(",", $frameworksToSync);
      foreach($frameworksToSyncArray as $frameworkId){

        $url = "https://app.masteryconnect.com/api/frameworks/$frameworkId/standards.json";
        $queryParams = [];

        // Generate OAuth nonce and timestamp
        $oauthNonce = md5(uniqid(rand(), true));
        $oauthTimestamp = time();

        // Generate OAuth signature
        $oauthParams = [
            'oauth_consumer_key' => $consumerKey,
            'oauth_nonce' => $oauthNonce,
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $oauthTimestamp,
            'oauth_token' => $accessToken,
            'oauth_version' => '1.0',
        ];
        ksort($oauthParams);

        $baseString = 'GET&' . rawurlencode($url) . '&' . rawurlencode(http_build_query(array_merge($oauthParams, $queryParams)));
        $signingKey = rawurlencode($consumerSecret) . '&' . rawurlencode($accessTokenSecret);
        $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        // Prepare cURL URL with query parameters
        $url .= '?' . http_build_query($queryParams);

        // Prepare cURL headers
        $headers = [
            'Authorization: OAuth oauth_consumer_key="' . $consumerKey . '",'
                . 'oauth_nonce="' . $oauthNonce . '",'
                . 'oauth_signature="' . rawurlencode($oauthSignature) . '",'
                . 'oauth_signature_method="HMAC-SHA1",'
                . 'oauth_timestamp="' . $oauthTimestamp . '",'
                . 'oauth_token="' . $accessToken . '",'
                . 'oauth_version="1.0"',
        ];

        // Initialize cURL session and set options
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, '');
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        //curl_setopt($curl, CURLOPT_VERBOSE, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $json = curl_exec($curl);

        // Loop Through Response and Insert Into DB
        $response = json_decode($json, TRUE);
        $stmt = $db->stmt_init();
        if(!empty($response)){
          foreach($response as $item){

            $name = $item['name'];
            $subject = $item['subject'];
            $framework = $item['framework'];
            $standards = $item['standards'];

            // Loop Through Standards
            foreach ($standards as $standard){

              $standardId = $standard['id'];
              $standardName = $standard['name'];
              $standardShortDescription = $standard['short_description'];

              $sql = "INSERT INTO abre_masteryconnect_standards (
                                          framework_id,
                                          name,
                                          subject,
                                          framework,
                                          standard_id,
                                          standard_name,
                                          standard_short_description
                                        )
                                        VALUES
                                        (?, ?, ?, ?, ?, ?, ?)";
              $stmt->prepare($sql);
              $stmt->bind_param("isssiss",
                                    $frameworkId,
                                    $name,
                                    $subject,
                                    $framework,
                                    $standardId,
                                    $standardName,
                                    $standardShortDescription
                                );
              $stmt->execute();

              $standardsData[] = $item; // Add item to standards data array
            }

          }
          $stmt->close();
          curl_close($curl);
        }

        // Upload standards data to GCS
        if (!empty($standardsData)) {
          _uploadToGCS($standardsData, 'standards', $bucket, $currentDate, $siteID);
        }
      }
    }

    /*
      GET TEACHERS
    */

    if($teachersEnabled){
      for ($i = 1; $i <= 100000; $i++) {
        $url = 'https://app.masteryconnect.com/api/teachers.json';
        $queryParams = ['page' => $i, 'per_page' => 100];

        // Generate OAuth nonce and timestamp
        $oauthNonce = md5(uniqid(rand(), true));
        $oauthTimestamp = time();

        // Generate OAuth signature
        $oauthParams = [
            'oauth_consumer_key' => $consumerKey,
            'oauth_nonce' => $oauthNonce,
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $oauthTimestamp,
            'oauth_token' => $accessToken,
            'oauth_version' => '1.0',
        ];
        ksort($oauthParams);

        $baseString = 'GET&' . rawurlencode($url) . '&' . rawurlencode(http_build_query(array_merge($oauthParams, $queryParams)));
        $signingKey = rawurlencode($consumerSecret) . '&' . rawurlencode($accessTokenSecret);
        $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        // Prepare cURL URL with query parameters
        $url .= '?' . http_build_query($queryParams);

        // Prepare cURL headers
        $headers = [
            'Authorization: OAuth oauth_consumer_key="' . $consumerKey . '",'
                . 'oauth_nonce="' . $oauthNonce . '",'
                . 'oauth_signature="' . rawurlencode($oauthSignature) . '",'
                . 'oauth_signature_method="HMAC-SHA1",'
                . 'oauth_timestamp="' . $oauthTimestamp . '",'
                . 'oauth_token="' . $accessToken . '",'
                . 'oauth_version="1.0"',
        ];

        // Initialize cURL session and set options
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, '');
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        //curl_setopt($curl, CURLOPT_VERBOSE, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $json = curl_exec($curl);

        // Loop Through Response and Insert Into DB
        $response = json_decode($json, TRUE);
        if(empty($response)){ break; }
        if($i == 1){ $db->query("DELETE FROM abre_masteryconnect_teachers WHERE site_id = $siteID"); }
        $stmt = $db->stmt_init();
        foreach($response as $item){

            $userId = $item['id'];
            $schoolId = $item['school_id'];
            $firstName = $item['first_name'];
            $lastName = $item['last_name'];
            $email = $item['email'];

            $sql = "INSERT INTO abre_masteryconnect_teachers (
                                        user_id,
                                        school_id,
                                        first_name,
                                        last_name,
                                        email,
                                        site_id
                                      )
                                      VALUES
                                      (?, ?, ?, ?, ?, ?)";
            $stmt->prepare($sql);
            $stmt->bind_param("sssssi",
                                  $userId,
                                  $schoolId,
                                  $firstName,
                                  $lastName,
                                  $email,
                                  $siteID
                              );
            $stmt->execute();

            $teachersData[] = $item; // Add item to teachers data array
        }
        $stmt->close();
        curl_close($curl);
      }

      // Upload teachers data to GCS
      if (!empty($teachersData)) {
        _uploadToGCS($teachersData, 'teachers', $bucket, $currentDate, $siteID);
      }
    }

    /*
      GET STUDENTS
    */

    if($studentsEnabled){
      for ($i = 1; $i <= 100000; $i++) {
        $url = 'https://app.masteryconnect.com/api/students.json';
        $queryParams = ['page' => $i, 'per_page' => 100];

        // Generate OAuth nonce and timestamp
        $oauthNonce = md5(uniqid(rand(), true));
        $oauthTimestamp = time();

        // Generate OAuth signature
        $oauthParams = [
            'oauth_consumer_key' => $consumerKey,
            'oauth_nonce' => $oauthNonce,
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $oauthTimestamp,
            'oauth_token' => $accessToken,
            'oauth_version' => '1.0',
        ];
        ksort($oauthParams);

        $baseString = 'GET&' . rawurlencode($url) . '&' . rawurlencode(http_build_query(array_merge($oauthParams, $queryParams)));
        $signingKey = rawurlencode($consumerSecret) . '&' . rawurlencode($accessTokenSecret);
        $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        // Prepare cURL URL with query parameters
        $url .= '?' . http_build_query($queryParams);

        // Prepare cURL headers
        $headers = [
            'Authorization: OAuth oauth_consumer_key="' . $consumerKey . '",'
                . 'oauth_nonce="' . $oauthNonce . '",'
                . 'oauth_signature="' . rawurlencode($oauthSignature) . '",'
                . 'oauth_signature_method="HMAC-SHA1",'
                . 'oauth_timestamp="' . $oauthTimestamp . '",'
                . 'oauth_token="' . $accessToken . '",'
                . 'oauth_version="1.0"',
        ];

        // Initialize cURL session and set options
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, '');
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        //curl_setopt($curl, CURLOPT_VERBOSE, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $json = curl_exec($curl);

        // Loop Through Response and Insert Into DB
        $response = json_decode($json, TRUE);
        if(empty($response)){ break; }
        if($i == 1){ $db->query("DELETE FROM abre_masteryconnect_students WHERE site_id = $siteID"); }
        $stmt = $db->stmt_init();
        foreach($response as $item){

            $userId = $item['id'];
            $firstName = $item['first_name'];
            $lastName = $item['last_name'];
            $email = $item['email'];
            $schoolNumber = $item['student_number'];
            $schoolId = $item['school_id'];

            $sql = "INSERT INTO abre_masteryconnect_students (
                                        user_id,
                                        first_name,
                                        last_name,
                                        email,
                                        student_number,
                                        school_id,
                                        site_id
                                      )
                                      VALUES
                                      (?, ?, ?, ?, ?, ?, ?)";
            $stmt->prepare($sql);
            $stmt->bind_param("ssssssi",
                                  $userId,
                                  $firstName,
                                  $lastName,
                                  $email,
                                  $schoolNumber,
                                  $schoolId,
                                  $siteID
                              );
            $stmt->execute();

            $studentsData[] = $item; // Add item to students data array
        }
        $stmt->close();
        curl_close($curl);
      }

      // Upload students data to GCS
      if (!empty($studentsData)) {
        _uploadToGCS($studentsData, 'students', $bucket, $currentDate, $siteID);
      }
    }

    /*
      GET ASSESSMENTS
    */

    if($assessmentsEnabled){
      for ($i = 1; $i <= 100000; $i++) {
        $url = 'https://app.masteryconnect.com/api/assessments.json';
        $queryParams = ['page' => $i, 'per_page' => 500];

        // Generate OAuth nonce and timestamp
        $oauthNonce = md5(uniqid(rand(), true));
        $oauthTimestamp = time();

        // Generate OAuth signature
        $oauthParams = [
            'oauth_consumer_key' => $consumerKey,
            'oauth_nonce' => $oauthNonce,
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $oauthTimestamp,
            'oauth_token' => $accessToken,
            'oauth_version' => '1.0',
        ];
        ksort($oauthParams);

        $baseString = 'GET&' . rawurlencode($url) . '&' . rawurlencode(http_build_query(array_merge($oauthParams, $queryParams)));
        $signingKey = rawurlencode($consumerSecret) . '&' . rawurlencode($accessTokenSecret);
        $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        // Prepare cURL URL with query parameters
        $url .= '?' . http_build_query($queryParams);

        // Prepare cURL headers
        $headers = [
            'Authorization: OAuth oauth_consumer_key="' . $consumerKey . '",'
                . 'oauth_nonce="' . $oauthNonce . '",'
                . 'oauth_signature="' . rawurlencode($oauthSignature) . '",'
                . 'oauth_signature_method="HMAC-SHA1",'
                . 'oauth_timestamp="' . $oauthTimestamp . '",'
                . 'oauth_token="' . $accessToken . '",'
                . 'oauth_version="1.0"',
        ];

        // Initialize cURL session and set options
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, '');
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        //curl_setopt($curl, CURLOPT_VERBOSE, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $json = curl_exec($curl);

        // Loop Through Response and Insert Into DB
        $response = json_decode($json, TRUE);
        if(empty($response)){ break; }
        if($i == 1){ $db->query("DELETE FROM abre_masteryconnect_assessments WHERE site_id = $siteID"); }
        $stmt = $db->stmt_init();
        foreach($response as $item){

          $assessmentId = $item['id'];
          $classroomId = $item['classroom_id'];
          $materialId = $item['material_id'];
          $totalScore = $item['total_score'];
          $title = $item['title'];
          $sequence = $item['sequence'];
          $givenOn = $item['given_on'];
          $standardId = $item['standard_id'];
          $benchmarkId = $item['benchmark_id'];
          $sectionId = $item['section_id'];
          $scoringType = $item['scoring_type'];

          $sql = "INSERT INTO abre_masteryconnect_assessments (
                                      assessment_id,
                                      classroom_id,
                                      material_id,
                                      total_score,
                                      title,
                                      sequence,
                                      given_on,
                                      standard_id,
                                      benchmark_id,
                                      section_id,
                                      scoring_type,
                                      site_id
                                    )
                                    VALUES
                                    (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
          $stmt->prepare($sql);
          $stmt->bind_param("siiisisiiiii",
                                $assessmentId,
                                $classroomId,
                                $materialId,
                                $totalScore,
                                $title,
                                $sequence,
                                $givenOn,
                                $standardId,
                                $benchmarkId,
                                $sectionId,
                                $scoringType,
                                $siteID
                            );
          $stmt->execute();

          $assessmentsData[] = $item; // Add item to assessments data array
        }
        $stmt->close();
        curl_close($curl);
      }

      // Upload assessments data to GCS
      if (!empty($assessmentsData)) {
        _uploadToGCS($assessmentsData, 'assessments', $bucket, $currentDate, $siteID);
      }
    }

    /*
      GET SCORES
    */

    if($scoresEnabled){
      $sql = "SELECT sync_date FROM abre_masteryconnect_scores ORDER BY sync_date DESC LIMIT 1";
      $result = $db->query($sql);
      //If never synced
      $previousYear = date('Y') - 1;
      $lastSyncDate = "$previousYear-06-01";
      //If synced
      if ($result && $result->num_rows > 0) {
          $row = $result->fetch_assoc();
          $lastSyncDate = $row['sync_date'];
      }
      $todayDate = date('Y-m-d');

      // API endpoint with query parameters
      for ($i = 1; $i <= 100000; $i++) {
        $url = 'https://app.masteryconnect.com/api/scores.json';
        $queryParams = ['page' => $i, 'per_page' => 500, 'date[start]' => $lastSyncDate];

        // Generate OAuth nonce and timestamp
        $oauthNonce = md5(uniqid(rand(), true));
        $oauthTimestamp = time();

        // Generate OAuth signature
        $oauthParams = [
            'oauth_consumer_key' => $consumerKey,
            'oauth_nonce' => $oauthNonce,
            'oauth_signature_method' => 'HMAC-SHA1',
            'oauth_timestamp' => $oauthTimestamp,
            'oauth_token' => $accessToken,
            'oauth_version' => '1.0',
        ];
        ksort($oauthParams);

        $baseStringParams = array_merge($oauthParams, $queryParams);
        ksort($baseStringParams);
        $baseString = 'GET&' . rawurlencode($url) . '&' . rawurlencode(http_build_query($baseStringParams));
        $signingKey = rawurlencode($consumerSecret) . '&' . rawurlencode($accessTokenSecret);
        $oauthSignature = base64_encode(hash_hmac('sha1', $baseString, $signingKey, true));

        // Prepare cURL URL with query parameters
        $url .= '?' . http_build_query($queryParams);

        // Prepare cURL headers
        $headers = [
            'Authorization: OAuth ' .
                'oauth_consumer_key="' . rawurlencode($consumerKey) . '",' .
                'oauth_nonce="' . rawurlencode($oauthNonce) . '",' .
                'oauth_signature="' . rawurlencode($oauthSignature) . '",' .
                'oauth_signature_method="HMAC-SHA1",' .
                'oauth_timestamp="' . rawurlencode($oauthTimestamp) . '",' .
                'oauth_token="' . rawurlencode($accessToken) . '",' .
                'oauth_version="1.0"',
        ];

        // Initialize cURL session and set options
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, '');
        curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
        curl_setopt($curl, CURLOPT_TIMEOUT, 0);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
        //curl_setopt($curl, CURLOPT_VERBOSE, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $json = curl_exec($curl);

        // Loop Through Response and Insert Into DB
        $response = json_decode($json, TRUE);
        if(empty($response)){ break; }
        $stmt = $db->stmt_init();
        foreach($response as $item){

          $scoreId = $item['id'];
          $studentId = $item['student_id'];
          $value = $item['value'];
          $current = $item['current'];
          $assessmentId = $item['assessment_id'];
          $archived = $item['archived'];
          $state = $item['state'];

          $sql = "INSERT INTO abre_masteryconnect_scores (
                                      sync_date,
                                      score_id,
                                      student_id,
                                      value,
                                      current,
                                      assessment_id,
                                      archived,
                                      state,
                                      site_id
                                    )
                                    VALUES
                                    (?, ?, ?, ?, ?, ?, ?, ?, ?)";
          $stmt->prepare($sql);
          $stmt->bind_param("sisisissi",
                                $todayDate,
                                $scoreId,
                                $studentId,
                                $value,
                                $current,
                                $assessmentId,
                                $archived,
                                $state,
                                $siteID
                            );
          $stmt->execute();

          $scoresData[] = $item; // Add item to scores data array
        }
        $stmt->close();
        curl_close($curl);
      }

      // Upload scores data to GCS
      if (!empty($scoresData)) {
        _uploadToGCS($scoresData, 'scores', $bucket, $currentDate, $siteID);
      }
    }

  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  
  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);

}

?>
