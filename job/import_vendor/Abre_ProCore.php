<?php
/*
* Copyright 2016-2023 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config){

  $cronName = 'ProCore Sync';

  try{

    $uuid = Logger::logCronStart($db, $siteID, $cronName);

    define("SAFE_COLUMN_COUNT", 17);
    define("MAX_IMPORT_LIMIT", 1);

    $currentSchoolYearID = getCurrentSchoolYearID($db);

    $error = null;
    $skip = null;
    $separator = "\r\n";

  	$sftp = new SFTP($config->sftp->ip);
  	if(!$sftp->login($config->sftp->userName, $config->sftp->password)){
  		throw new Exception("Login to SFTP failed.");
  	}

  	$cronFile = $sftp->get($config->sftp->file);

  	if(!$cronFile){
      $skip = true;
      throw new Exception("No file found.");
    }else{
      $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, ",");
      if($fileDetails["isEmpty"]){
        $skip = true;
        throw new Exception("File is empty.");
      }elseif($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]){
        $skip = true;
        throw new Exception("File only contains a header row.");
      }elseif(!$fileDetails["hasValidDataRow"]){
        throw new Exception("No valid data row found.");
      }
    }

    $rowCounter = 0;
    $valuesToImport = [];
    $dbColumns = "INSERT INTO abre_procore (
      district_name, school_name, student_id, grade_level, subject_name, subject_code,
      exam_session_id, exam_title, test_form, test_date, raw_score, percentage_score,
      total_point_value, state_score, nce, scaled_score, performance_level, site_id
     ) VALUES ";

    // Upload SFTP File to Google Cloud Storage Bucket
    $currentDate = date("Ymd");
    $fileName = "Abre_ProCore.csv";
    $storage = new StorageClient(['projectId' => "abre-production"]);
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($cronFile, [
        'name' => "$currentDate/site-id/$siteID/$fileName"
    ]);

    // Upload to the other folder location in landing zone bucket
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $folderName = pathinfo($fileName, PATHINFO_FILENAME);
    $modifiedFile = $folderName . "-" . $siteID . "." . $fileExtension;
    $bucketName = "prd-landing-zone";
    $bucket = $storage->bucket($bucketName);
    $bucket->upload($cronFile, [
        'name' => "$currentDate/filename/$folderName/$modifiedFile"
    ]);

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); //skip header row

  	do{
      $data = str_getcsv($line, ",");

  		if(count($data) >= SAFE_COLUMN_COUNT){
        $rowCounter++;

  			$districtName = trim($db->escape_string($data[0]));
        $schoolName = trim($db->escape_string($data[1]));
        $studentId = trim($db->escape_string($data[2]));
        $gradeLevel = trim($db->escape_string($data[3]));
        $subjectName = trim($db->escape_string($data[4]));
        $subjectCode = trim($db->escape_string($data[5]));
        $examSessionId = trim($db->escape_string($data[6]));
        $examTitle = trim($db->escape_string($data[7]));
        $testForm = trim($db->escape_string($data[8]));
        $testDate = trim($db->escape_string($data[9]));
        $rawScore = trim($db->escape_string($data[10]));
        $percentageScore = trim($db->escape_string($data[11]));
        $totalPointValue = trim($db->escape_string($data[12]));
        $stateScore = trim($db->escape_string($data[13]));
        $nce = trim($db->escape_string($data[14]));
        $scaledScore = trim($db->escape_string($data[15]));
        $performanceLevel = trim($db->escape_string($data[16]));

        $valuesToImport []= "('$districtName', '$schoolName', '$studentId', '$gradeLevel', '$subjectName', '$subjectCode',
                                '$examSessionId', '$examTitle', '$testForm', '$testDate', '$rawScore', '$percentageScore',
                                '$totalPointValue', '$stateScore', '$nce', '$scaledScore', '$performanceLevel', $siteID)";

  			if(count($valuesToImport) == MAX_IMPORT_LIMIT){
          insertRows($db, $dbColumns, $valuesToImport);
          $valuesToImport = [];
  			}
  		}

  		$line = strtok($separator);
    }while($line !== false);

    if(count($valuesToImport)){
      insertRows($db, $dbColumns, $valuesToImport);
    }
  }catch(Exception $ex){
    $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

  Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);

}

?>
