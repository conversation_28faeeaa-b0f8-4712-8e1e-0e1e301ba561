<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__) . '/../vendor/autoload.php');
require_once(dirname(__FILE__) . '/utils/functions.php');
require_once(dirname(__FILE__) . '/utils/logging.php');

use phpseclib\Net\SFTP;
use Google\Cloud\Storage\StorageClient;

function runJob($db, $siteID, $config)
{
    ignore_user_abort(true);
    set_time_limit(0);
    $cronName = 'Abre One Roster 1.1 - Fort Worth';

    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

        define("MAX_IMPORT_LIMIT", 250);

        if (!$siteID) {
            $siteID = 0;
            throw new InvalidArgumentException("no siteID provided");
        }

        $jobStartTime = (new DateTime("now", new DateTimeZone('UTC')))->format("Y-m-d H:i:s");
        $schoolYearID = getCurrentSchoolYearID($db);
        $currentEndingSchoolYear = getCurrentEndSchoolYear($db);

        $error = null;
        $skip = null;
        $separator = "\r\n";

        $sftp = new SFTP($config->sftp->ip);
        if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
            throw new Exception("Login to SFTP failed.");
        }

        $tempDirectory = tempdir();

        $oneRosterZip = $sftp->get('OneRoster.zip', "$tempDirectory/OneRoster.zip");
        if (!$oneRosterZip) {
            $skip = true;
            throw new Exception("No file found.");
        } else {
            $zipArchive = new ZipArchive();
            $isUnzippable = $zipArchive->open("$tempDirectory/OneRoster.zip");
            if ($isUnzippable === true) {
                $zipArchive->extractTo("$tempDirectory");
                $zipArchive->close();

                // Upload files to Google Cloud Storage
                $currentDate = date("Ymd");
                $storage = new StorageClient(['projectId' => "abre-production"]);
                $bucketName = "prd-landing-zone";
                $bucket = $storage->bucket($bucketName);

                // Get list of CSV files in temp directory
                $files = glob("$tempDirectory/*.csv");
                foreach ($files as $file) {
                    $baseFileName = basename($file);

                    // Format filename for upload
                    $unzippedFileName = strtolower(pathinfo($baseFileName, PATHINFO_FILENAME));

                    // Create type-specific folder name
                    $typeFolder = "one_roster_" . $unzippedFileName;

                    // Upload to filename/<type_folder>/
                    $modifiedFile = $typeFolder . "-" . $siteID . ".csv";
                    $bucket->upload(
                        fopen($file, 'r'),
                        [
                            'name' => "$currentDate/filename/$typeFolder/$modifiedFile"
                        ]
                    );

                    // Upload to site-id/<site-id>/
                    $siteIdFile = $typeFolder . ".csv";
                    $bucket->upload(
                        fopen($file, 'r'),
                        [
                            'name' => "$currentDate/site-id/$siteID/$siteIdFile"
                        ]
                    );
                }
            } else {
                // Do something on error
            }
        }

        //read manifest file
        $manifest = [];
        if (file_exists("$tempDirectory/manifest.csv")) {
            if (($file = fopen("$tempDirectory/manifest.csv", "r")) !== false) {
                fgetcsv($file); //header
                while (($data = fgetcsv($file)) !== false) {
                    $manifest[$data[0]] = $data[1];
                }
            }

            //error_log(print_r($manifest, true));
            fclose($file);
        } else {
            //manifest doesn't exist
        }

        //verify zip file version
        if (!isset($manifest["oneroster.version"]) || $manifest["oneroster.version"] != 1.1) {
            throw new Exception("One Roster file is not version 1.1");
        }

        /**
         * import orgs if exists
         * only handle bulk import at this time
         * TODO: support delta?
         * column(required?)
         * [sourceId(Y), status(N), dateLastModified(N), name(Y), type(Y), identifier(N), parentSourceId(Y)]
         */
        $orgs = [];
        if (isset($manifest["file.orgs"]) && $manifest["file.orgs"] == "bulk") {
            if (file_exists("$tempDirectory/orgs.csv")) {
                if (($file = fopen("$tempDirectory/orgs.csv", "r")) !== false) {
                    fgetcsv($file); //header
                    while (($data = fgetcsv($file)) !== false) {
                        //validate all required fields have values
                        if ($data[0] != "" && $data[3] != "" && $data[4] != "") {
                            if ($data[4] == "school") {
                                $orgs[$data[0]] = [
                                    "id" => $data[0],
                                    "name" => $data[3],
                                    "identifier" => $data[0]
                                ];
                            }
                        }
                    }
                }

                //error_log(print_r($orgs, true));
                fclose($file);
            }
        }

        /**
         * import academic sessions if exists
         * only handle bulk import at this time
         * TODO: support delta?
         * column(required?)
         * [sourceId(Y), status(N), dateLastModified(N), title(Y), type(Y), startDate(Y), endDate(Y), parentSourceId(N), schoolYear(Y)]
         */
        $academicSessions = [];
        if (isset($manifest["file.academicSessions"]) && $manifest["file.academicSessions"] == "bulk") {
            if (file_exists("$tempDirectory/academicSessions.csv")) {
                if (($file = fopen("$tempDirectory/academicSessions.csv", "r")) !== false) {
                    fgetcsv($file); //header
                    while (($data = fgetcsv($file)) !== false) {
                        //validate all required fields have values
                        if ($data[0] != "" && $data[3] != "" && $data[4] != "" && $data[5] != "" && $data[6] != "" && $data[8] != "") {
                            // $data[8] == $currentEndingSchoolYear
                            //   &&
                            //if ($data[4] != "schoolYear") { (TODO: Possibly add this back in to filter out school years)
                            $academicSessions[$data[0]] = [
                                "id" => $data[0],
                                "type" => $data[4],
                                "title" => $data[3],
                                "start" => $data[5],
                                "end" => $data[6],
                                "year" => $data[8]
                            ];
                            //}
                        }
                    }
                }

                //error_log(print_r($academicSessions, true));
                fclose($file);
            }
        }

        /**
         * import courses if exists
         * only handle bulk import at this time
         * TODO: support delta?
         * column(required?)
         * [sourceId(Y), status(N), dateLastModified(N), schoolYearSourceId(N), title(Y),
         *  courseCode(N), grades(N), orgSourceId(Y), subjects(N), subjectCodese(N)]
         */
        $courses = [];
        if (isset($manifest["file.courses"]) && $manifest["file.courses"] == "bulk") {
            if (file_exists("$tempDirectory/courses.csv")) {
                if (($file = fopen("$tempDirectory/courses.csv", "r")) !== false) {
                    fgetcsv($file); //header
                    while (($data = fgetcsv($file)) !== false) {
                        //validate all required fields have values
                        if ($data[0] != "" && $data[4] != "" && $data[7] != "") {
                            $courses[$data[0]] = [
                                "id" => $data[0],
                                "schoolYearId" => $data[3],
                                "title" => $data[4],
                                "courseCode" => $data[5],
                                "orgId" => $data[7],
                            ];
                        }
                    }
                }

                //error_log(print_r($courses, true));
                fclose($file);
            }
        }

        /**
         * import classes if exists
         * only handle bulk import at this time
         * TODO: support delta?
         * column(required?)
         * [sourceId(Y), status(N), dateLastModified(N), title(Y), grades(N)
         *  courseSourceId(Y), classCode(N), classType(Y), location(N), schoolSouceId(Y),
         *  termSourceIds(Y), subjects(N), subjectCodes(N), periods(N)]
         */
        $classes = [];
        if (isset($manifest["file.classes"]) && $manifest["file.classes"] == "bulk") {
            if (file_exists("$tempDirectory/classes.csv")) {
                if (($file = fopen("$tempDirectory/classes.csv", "r")) !== false) {
                    fgetcsv($file); //header
                    while (($data = fgetcsv($file)) !== false) {
                        //validate all required fields have values
                        if ($data[0] != "" && $data[3] != "" && $data[5] != "" && $data[7] != "" && $data[9] != "" && $data[10] != "") {

                            //these are foreign key columns that reference data from other files
                            if (!isset($courses[$data[5]]) || !isset($orgs[$data[9]])) {
                                //we found a course id or org id that was not previous defined. throw error?
                                continue;
                            }

                            $terms = explode(",", $data[10]);
                            foreach ($terms as $index => $term) {
                                if (!isset($academicSessions[$term])) {
                                    //we found a term id that did not have a valid definition. throw error?
                                    unset($terms[$index]);
                                }
                            }
                            $classes[$data[0]] = [
                                "id" => $data[0],
                                "title" => $data[3],
                                "courseID" => $data[5],
                                "courseCode" => $data[6],
                                "orgId" => $data[9],
                                "termIds" => $terms,
                                "periods" => explode(",", $data[13])
                            ];
                        }
                    }
                }

                //error_log(print_r($classes, true));
                fclose($file);
            }
        }

        /**
         * import users if exists
         * only handle bulk import at this time
         * TODO: support delta?
         * column(required?)
         * [sourceId(Y), status(N), dateLastModified(N), enabledUser(Y), orgSourceIds(Y)
         *  role(Y), username(N), userIds(N), givenName(Y), lastName(Y),
         *  middleName(N), email(N), sms(N), phone(N), agentSourceIds(N), grades(N), password(N)]
         */
        $users = [];
        if (isset($manifest["file.users"]) && $manifest["file.users"] == "bulk") {
            if (file_exists("$tempDirectory/users.csv")) {
                if (($file = fopen("$tempDirectory/users.csv", "r")) !== false) {
                    fgetcsv($file); //header
                    while (($data = fgetcsv($file)) !== false) {
                        //validate all required fields have values
                        if ($data[0] != "" && $data[3] != "" && $data[4] != "" && $data[5] != "" && $data[6] != "" && $data[8] != "" && $data[9] != "") {
                            if (strtolower($data[3]) == "true") {

                                //these are foreign key columns that reference data from other files
                                $orgIds = explode(",", $data[4]);
                                foreach ($orgIds as $index => $orgId) {
                                    if (!isset($orgs[$orgId])) {
                                        //we found an org id that did not have a valid definition. throw error?
                                        unset($orgIds[$index]);
                                    }
                                }

                                if ($data[5] == "administrator" || $data[5] == "teacher") {
                                    $users["staff"][$data[0]] = [
                                        "id" => $data[0],
                                        "orgIds" => $orgIds,
                                        "role" => $data[5],
                                        "username" => $data[6],
                                        "firstName" => $data[8],
                                        "lastName" => $data[9],
                                        "middleName" => $data[10],
                                        "localId" => $data[11],
                                        "email" => $data[6]
                                    ];
                                } elseif ($data[5] == "student") {
                                    $users["student"][$data[0]] = [
                                        "id" => $data[0],
                                        "orgIds" => $orgIds,
                                        "role" => $data[5],
                                        "username" => $data[6],
                                        "firstName" => $data[8],
                                        "lastName" => $data[9],
                                        "middleName" => $data[10],
                                        "localId" => $data[11],
                                        "email" => $data[12],
                                        "currentGrade" => $data[16],
                                        "password" => $data[17]
                                    ];
                                }
                            }
                        }
                    }
                }

                //error_log(print_r($users, true));
                fclose($file);
            }
        }

        /**
         * import enrollments if exists
         * only handle bulk import at this time
         * TODO: support delta?
         * column(required?)
         * [sourceId(Y), status(N), dateLastModified(N), classSourceId(Y), schoolSourceId(Y)
         *  userSourceId(Y), role(Y), primary(N), beginDate(N), endDate(N)]
         */
        $enrollments = [];
        if (isset($manifest["file.enrollments"]) && $manifest["file.enrollments"] == "bulk") {
            if (file_exists("$tempDirectory/enrollments.csv")) {
                if (($file = fopen("$tempDirectory/enrollments.csv", "r")) !== false) {
                    fgetcsv($file); //header
                    while (($data = fgetcsv($file)) !== false) {
                        //validate all required fields have values
                        if ($data[0] != "" && $data[3] != "" && $data[4] != "" && $data[5] != "" && $data[6] != "") {
                            //verify classes ref id and org ref id are valid
                            if (!isset($classes[$data[3]]) || !isset($orgs[$data[4]])) {
                                continue;
                            }
                            //verify the user was in users.csv
                            if (($data[6] == "administrator" || $data[6] == "teacher")) {
                                if (!isset($users["staff"][$data[5]])) {
                                    continue;
                                }
                                $enrollments["staff"][$data[0]] = [
                                    "id" => $data[0],
                                    "classId" => $data[3],
                                    "schoolId" => $data[4],
                                    "userId" => $data[5],
                                    "primary" => $data[7]
                                ];
                            }
                            if ($data[6] == "student") {
                                if (!isset($users["student"][$data[5]])) {
                                    continue;
                                }
                                $enrollments["student"][$data[0]] = [
                                    "id" => $data[0],
                                    "classId" => $data[3],
                                    "schoolId" => $data[4],
                                    "userId" => $data[5]
                                ];
                            }
                        }
                    }
                }

                //error_log(print_r($enrollments, true));
                fclose($file);
            }
        }

        //---------------------------
        /**
         * Insert Data
         */
        $valuesToImport = [];
        $dbColumns = "";
        if (count($orgs)) {
            $dbColumns = "INSERT INTO abre_schools (code, name, site_id, school_year_id) VALUES ";

            foreach ($orgs as $id => $org) {
                $code = $org["identifier"] != "" ? $org["identifier"] : $org["id"];
                $codeEscaped = trim($db->escape_string($code));

                $nameEscaped = trim($db->escape_string($org["name"]));

                $valuesToImport[] = "(
          '$codeEscaped', '$nameEscaped', $siteID, $schoolYearID
        )";

                if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
                    $valuesToImport = [];
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $dbColumns, $valuesToImport, "ON DUPLICATE KEY UPDATE abre_schools.code = VALUES(code), abre_schools.name = VALUES(name), abre_schools.site_id = VALUES(site_id)");
            }
        }

        $valuesToImport = [];
        $dbColumns = "";
        if (count($academicSessions)) {
            $deleteSql = "DELETE FROM abre_term WHERE site_id = ? AND term_year_id = ? AND is_imported = 1";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $termColumns = "INSERT INTO abre_term
                        (site_id, term_definition_id, term_year_id, start_date, end_date, is_imported)
                      VALUES ";
            $termDefinitions = [];
            foreach ($academicSessions as $id => $session) {

                $customSourceID = $session["id"] . "-$siteID";
                if (!isset($termDefinitions[$customSourceID])) {

                    $existingDefinitionID = null;
                    $sessionSelectSql = "SELECT id FROM abre_term_definition WHERE term_input = ?";
                    $sessionSelectStmt = $db->stmt_init();
                    $sessionSelectStmt->prepare($sessionSelectSql);
                    $sessionSelectStmt->bind_param("s", $customSourceID);
                    $sessionSelectStmt->execute();
                    $sessionSelectStmt->bind_result($existingDefinitionID);
                    $sessionSelectStmt->fetch();
                    $sessionSelectStmt->close();

                    if ($existingDefinitionID === null) {
                        $insertTerm = "INSERT INTO abre_term_definition (term_input, display_short, display_long, is_hidden)
                            VALUES (?, ?, ?, 0)";
                        $insertStmt = $db->stmt_init();
                        $insertStmt->prepare($insertTerm);
                        $insertStmt->bind_param("sss", $customSourceID, $session["title"], $session["title"]);
                        $insertStmt->execute();
                        $newTermID = $insertStmt->insert_id;
                        $insertStmt->close();
                        $termDefinitions[$customSourceID] = $newTermID;
                    } else {
                        $termDefinitions[$customSourceID] = $existingDefinitionID;
                    }
                }

                $escapedTermID = trim($db->escape_string($termDefinitions[$customSourceID]));
                $escapedStartDate = trim($db->escape_string($session["start"]));
                $escapedEndDate = trim($db->escape_string($session["end"]));
                $valuesToImport[] = "(
          $siteID, $escapedTermID, $schoolYearID, '$escapedStartDate', '$escapedEndDate', 1
        )";

                if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $termColumns, $valuesToImport);
                    $valuesToImport = [];
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $termColumns, $valuesToImport);
            }
        }

        /**
         * SchoolCode => schoolSourcedId
         * CourseCode => classCode or courseSourceId if empty
         * SectionCode => sourceId
         * StaffID => lookup from enrollments (could be multiple)
         * TermCode => termSourcedIds (can be a list)
         * period => periods can be a list ("1,2,3")
         */
        $valuesToImport = [];
        $dbColumns = "";
        $teacherCache = [];
        if (count($classes)) {
            $deleteSql = "DELETE FROM Abre_Courses WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $dbColumns = "INSERT INTO Abre_Courses
                      (SchoolCode, CourseCode, SectionCode, StaffID, TermCode, Period, siteID, school_year_id)
                    VALUES ";

            foreach ($classes as $class) {
                $classOrg = $class["orgId"];
                $classOrg = trim($db->escape_string($classOrg));

                $schoolCode = $orgs[$classOrg]["identifier"] != "" ? $orgs[$classOrg]["identifier"] : $orgs[$classOrg]["id"];
                $schoolCode = trim($db->escape_string($schoolCode));

                $courseCode = $courses[$class["courseID"]]["courseCode"] != "" ? $courses[$class["courseID"]]["courseCode"] : $courses[$class["courseID"]]["id"];
                $courseCode = trim($db->escape_string($courseCode));

                $sectionCode = $class["id"];
                $sectionCode = trim($db->escape_string($sectionCode));

                $classTeachers = [];
                foreach ($enrollments["staff"] as $staffMember) {
                    if ($staffMember["classId"] == $class["id"] && strtolower($staffMember["primary"]) == "true") {
                        $staffID = $users["staff"][$staffMember["userId"]]["localId"] != "" ? $users["staff"][$staffMember["userId"]]["localId"] : $users["staff"][$staffMember["userId"]]["id"];

                        $teacherCache[$class["id"]] = [
                            "id" => $staffID,
                            "firstName" => $users["staff"][$staffMember["userId"]]["firstName"],
                            "lastName" => $users["staff"][$staffMember["userId"]]["lastName"]
                        ];
                        $classTeachers[] = trim($db->escape_string($staffID));
                    }
                }
                foreach ($class["termIds"] as $term) {
                    foreach ($class["periods"] as $period) {
                        foreach ($classTeachers as $teacher) {
                            $escapedTerm = trim($db->escape_string($term . "-$siteID"));
                            $escapedPeriod = trim($db->escape_string($period));
                            $valuesToImport[] = "(
                '$schoolCode', '$courseCode', '$sectionCode', '$teacher',
                '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
              )";

                            if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                                insertRows($db, $dbColumns, $valuesToImport);
                                $valuesToImport = [];
                            }
                        }
                    }
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $dbColumns, $valuesToImport);
            }
        }

        $valuesToImport = [];
        $emailsToImport = [];
        $dbColumns = "";
        if (count($users["student"])) {
            $deleteSql = "DELETE FROM Abre_Students WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $deleteSql = "DELETE FROM Abre_AD WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $dbColumns = "INSERT INTO Abre_Students
                    (StudentId, FirstName, MiddleName, LastName, Email, SchoolCode,
                      SchoolName, CurrentGrade, okay_to_publish, siteID, school_year_id)
                    VALUES ";
            $adColumns = "INSERT INTO Abre_AD (Email, StudentID, siteID, school_year_id) VALUES ";

            foreach ($users["student"] as $id => $student) {
                $studentID = $student["localId"] != "" ? $student["localId"] : $student["id"];
                $studentID = trim($db->escape_string($studentID));
                $firstName = trim($db->escape_string($student["firstName"]));
                $middleName = trim($db->escape_string($student["middleName"]));
                $lastName = trim($db->escape_string($student["lastName"]));
                $email = trim($db->escape_string($student["email"]));
                $currentGrade = trim($db->escape_string($student["currentGrade"]));

                $emailsToImport[] = "('$email', '$studentID', $siteID, $schoolYearID)";
                if (count($emailsToImport) == MAX_IMPORT_LIMIT) {
                    insertRows($db, $adColumns, $emailsToImport);
                    $emailsToImport = [];
                }

                foreach ($student["orgIds"] as $building) {
                    $schoolCode = $orgs[$building]["identifier"] != "" ? $orgs[$building]["identifier"] : $orgs[$building]["id"];
                    $schoolCode = trim($db->escape_string($schoolCode));

                    $buildingName = trim($db->escape_string($orgs[$building]["name"]));

                    $valuesToImport[] = "(
            '$studentID', '$firstName', '$middleName', '$lastName', '$email', '$schoolCode',
            '$buildingName', '$currentGrade', 1, $siteID, $schoolYearID
          )";
                    if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                        insertRows($db, $dbColumns, $valuesToImport);
                        $valuesToImport = [];
                    }
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $dbColumns, $valuesToImport);
            }
            if (count($emailsToImport)) {
                insertRows($db, $adColumns, $emailsToImport);
            }
        }

        $valuesToImport = [];
        $dbColumns = "";
        if (count($users["staff"])) {
            $dbColumns = "INSERT INTO Abre_Staff
                    (StaffID, FirstName, MiddleName, LastName, EMail1,
                      is_imported, imported_on, siteID, school_year_id)
                    VALUES ";

            $emptyStringEncrypted = encrypt("", $config->dbKey->iv, $config->dbKey->key);
            $insertSql = "INSERT INTO directory
                    (updatedtime, superadmin, admin, picture, firstname, lastname,
                      middlename, address, city, state, zip, email, phone, extension,
                      cellphone, ss, dob, gender, ethnicity, title, contract, classification,
                      location, grade, subject, doh, senioritydate, effectivedate, rategroup,
                      step, educationlevel, salary, hours, probationreportdate,
                      statebackgroundcheck, federalbackgroundcheck, stateeducatorid,
                      licensetype1, licenseissuedate1, licenseexpirationdate1, licenseterm1,
                      licensetype2, licenseissuedate2, licenseexpirationdate2, licenseterm2,
                      licensetype3, licenseissuedate3, licenseexpirationdate3, licenseterm3,
                      licensetype4, licenseissuedate4, licenseexpirationdate4, licenseterm4,
                      licensetype5, licenseissuedate5, licenseexpirationdate5, licenseterm5,
                      licensetype6, licenseissuedate6, licenseexpirationdate6, licenseterm6,
                      permissions, role, contractdays, siteID
                    )
                    VALUES (CURRENT_TIMESTAMP, 0, 0, '', ?, ?, '', ?, ?, ?, ?, ?, ?, '',
                      ?, ?, ?, ?, ?, '', ?, '', '', '', '', ?, ?, ?, ?, ?, ?, ?, ?,
                      ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                      ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                    );";
            $insertStmt = $db->stmt_init();
            $insertStmt->prepare($insertSql);

            foreach ($users["staff"] as $id => $staff) {
                $staffID = $staff["localId"] != "" ? $staff["localId"] : $staff["id"];
                $staffID = trim($db->escape_string($staffID));

                $firstName = trim($db->escape_string($staff["firstName"]));
                $middleName = trim($db->escape_string($staff["middleName"]));
                $lastName = trim($db->escape_string($staff["lastName"]));
                $email = trim($db->escape_string($staff["email"]));

                $valuesToImport[] = "(
          '$staffID', '$firstName', '$middleName', '$lastName', '$email', 1, NOW(), $siteID, $schoolYearID
        )";

                if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                    $values = implode(",", $valuesToImport);
                    $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                          FirstName = VALUES(FirstName),
                          MiddleName = VALUES(MiddleName),
                          LastName = VALUES(LastName),
                          EMail1 = VALUES(EMail1),
                          is_imported = 1,
                          imported_on = NOW(),
                          is_archived = 0";
                    $db->query($importQuery);
                    $valuesToImport = [];
                }

                $quotedSchools = array_map(function ($school) use ($orgs) {
                    $schoolID = $orgs[$school]["identifier"] != "" ? $orgs[$school]["identifier"] : $orgs[$school]["id"];
                    return "'$schoolID'";
                }, $staff["orgIds"]);
                $schoolsForSql = "(" . join(",", $quotedSchools) . ")";

                $schoolDeleteSql = "DELETE FROM abre_staff_schools
                            WHERE school_code NOT IN $schoolsForSql
                              AND is_imported = 1 AND staff_id = ? AND site_id = ?";
                $deleteSchoolsStmt = $db->stmt_init();
                $deleteSchoolsStmt->prepare($schoolDeleteSql);
                $deleteSchoolsStmt->bind_param("si", $staffID, $siteID);
                $deleteSchoolsStmt->execute();
                $deleteSchoolsStmt->close();

                $schoolsToImport = [];
                foreach ($staff["orgIds"] as $school) {
                    $schoolID = $orgs[$school]["identifier"] != "" ? $orgs[$school]["identifier"] : $orgs[$school]["id"];
                    $schoolsToImport[] = "('$staffID', '$schoolID', $siteID, 1, NOW())";
                }
                $schoolsImportString = implode(",", $schoolsToImport);

                $schoolInsertSql = "INSERT INTO abre_staff_schools
                              (staff_id, school_code, site_id, is_imported, imported_on)
                            VALUES $schoolsImportString
                            ON DUPLICATE KEY UPDATE
                              is_imported = 1,
                              imported_on = NOW()";
                $insertSchoolStmt = $db->stmt_init();
                $insertSchoolStmt->prepare($schoolInsertSql);
                $insertSchoolStmt->execute();
                $insertSchoolStmt->close();

                if ($staff["email"] != "") {
                    $selectSql = "SELECT COUNT(*) FROM directory WHERE email = ? AND siteID = ?";
                    $selectStmt = $db->stmt_init();
                    $selectStmt->prepare($selectSql);
                    $selectStmt->bind_param("si", $staff["email"], $siteID);
                    $selectStmt->execute();
                    $selectStmt->bind_result($existingRow);
                    $selectStmt->fetch();
                    $selectStmt->close();

                    if (!$existingRow) {
                        $insertStmt->bind_param(
                            "sssssssssssssssssssssssssssssssssssssssssssssssssssssi",
                            $staff["firstName"],
                            $staff["lastName"],
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $staff["email"],
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $emptyStringEncrypted,
                            $siteID
                        );
                        $insertStmt->execute();
                    }
                }
            }
            $insertStmt->close();

            if (count($valuesToImport)) {
                $values = implode(",", $valuesToImport);
                $importQuery = "$dbColumns $values ON DUPLICATE KEY UPDATE
                        FirstName = VALUES(FirstName),
                        MiddleName = VALUES(MiddleName),
                        LastName = VALUES(LastName),
                        EMail1 = VALUES(EMail1),
                        is_imported = 1,
                        imported_on = NOW(),
                        is_archived = 0";
                $db->query($importQuery);
                $valuesToImport = [];
            }

            //remove building relationships for staff no longer in import file
            $deleteOldBuildings = "DELETE FROM abre_staff_schools
                             WHERE site_id = ? AND is_imported = 1
                               AND imported_on < ?";
            $deleteOldStmt = $db->stmt_init();
            $deleteOldStmt->prepare($deleteOldBuildings);
            $deleteOldStmt->bind_param("is", $siteID, $jobStartTime);
            $deleteOldStmt->execute();
            $deleteOldStmt->close();

            //archive staff that are no longer in the import file
            $archiveOldStaff = "UPDATE Abre_Staff SET is_archived = 1
                          WHERE siteID = ? AND is_imported = 1
                            AND imported_on < ? AND school_year_id = ?";
            $archiveStmt = $db->stmt_init();
            $archiveStmt->prepare($archiveOldStaff);
            $archiveStmt->bind_param("isi", $siteID, $jobStartTime, $schoolYearID);
            $archiveStmt->execute();
            $archiveStmt->close();

            //ensure the archive flags in the staff table are consistent with the import
            //flags in the directory
            $directoryArchiveUpdate = "UPDATE directory d
                                  JOIN (
                                    SELECT EMail1, siteID, MIN(is_archived) isArchived
                                      FROM Abre_Staff
                                    WHERE siteID = ? AND is_imported = 1 AND school_year_id = ?
                                    GROUP BY EMail1, siteID
                                  ) staff
                                  ON staff.EMail1 = d.email AND staff.siteID = d.siteID
                                  SET d.archived = staff.isArchived";
            $directoryArchiveStmt = $db->stmt_init();
            $directoryArchiveStmt->prepare($directoryArchiveUpdate);
            $directoryArchiveStmt->bind_param("ii", $siteID, $schoolYearID);
            $directoryArchiveStmt->execute();
            $directoryArchiveStmt->close();
        }

        $valuesToImport = [];
        $dbColumns = "";
        if (count($enrollments["staff"])) {
            $deleteSql = "DELETE FROM Abre_StaffSchedules WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $dbColumns = "INSERT INTO Abre_StaffSchedules
                    (StaffID, SchoolCode, CourseCode, SectionCode, TermCode, Period,
                      CourseName, TeacherName, siteID, school_year_id)
                    VALUES ";

            foreach ($enrollments["staff"] as $id => $staff) {
                $staffID = $users["staff"][$staff["userId"]]["localId"] != "" ? $users["staff"][$staff["userId"]]["localId"] : $users[$staff["userId"]]["id"];
                $staffID = trim($db->escape_string($staffID));

                $schoolCode = $orgs[$staff["schoolId"]]["identifier"] != "" ? $orgs[$staff["schoolId"]]["identifier"] : $orgs[$staff["schoolId"]]["id"];
                $schoolCode = trim($db->escape_string($schoolCode));

                $courseCode = $courses[$classes[$staff["classId"]]["courseID"]]["courseCode"] != ""
                    ? $courses[$classes[$staff["classId"]]["courseID"]]["courseCode"]
                    : $courses[$classes[$staff["classId"]]["courseID"]]["id"];
                $courseCode = trim($db->escape_string($courseCode));

                $sectionCode = trim($db->escape_string($classes[$staff["classId"]]["id"]));
                $courseName = trim($db->escape_string($classes[$staff["classId"]]["title"]));
                $teacherName = trim($db->escape_string($users["staff"][$staff["userId"]]["firstName"] . " " . $users["staff"][$staff["userId"]]["lastName"]));
                foreach ($classes[$staff["classId"]]["termIds"] as $termCode) {
                    foreach ($classes[$staff["classId"]]["periods"] as $period) {
                        $escapedTermCode = trim($db->escape_string($termCode . "-$siteID"));
                        $escapedPeriod = trim($db->escape_string($period));

                        $valuesToImport[] = "(
              '$staffID', '$schoolCode', '$courseCode', '$sectionCode', '$escapedTermCode',
              '$escapedPeriod', '$courseName', '$teacherName', $siteID, $schoolYearID
            )";

                        if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                            insertRows($db, $dbColumns, $valuesToImport);
                            $valuesToImport = [];
                        }
                    }
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $dbColumns, $valuesToImport);
            }
        }

        $valuesToImport = [];
        $dbColumns = "";
        if (count($enrollments["student"])) {
            $deleteSql = "DELETE FROM Abre_StudentSchedules WHERE siteID = ? AND school_year_id = ?";
            $deleteStmt = $db->stmt_init();
            $deleteStmt->prepare($deleteSql);
            $deleteStmt->bind_param("ii", $siteID, $schoolYearID);
            $deleteStmt->execute();
            $deleteStmt->close();

            $dbColumns = "INSERT INTO Abre_StudentSchedules
                    (StudentID, FirstName, LastName, SchoolCode, CourseCode, SectionCode,
                      CourseName, StaffId, TeacherName, TermCode, Period, siteID, school_year_id)
                    VALUES ";

            foreach ($enrollments["student"] as $id => $student) {
                $studentID = $users["student"][$student["userId"]]["localId"] != "" ? $users["student"][$student["userId"]]["localId"] : $users["student"][$student["userId"]]["id"];
                $studentID = trim($db->escape_string($studentID));

                $schoolCode = $orgs[$student["schoolId"]]["identifier"] != "" ? $orgs[$student["schoolId"]]["identifier"] : $orgs[$student["schoolId"]]["id"];
                $schoolCode = trim($db->escape_string($schoolCode));

                $courseCode = $courses[$classes[$student["classId"]]["courseID"]]["courseCode"] != ""
                    ? $courses[$classes[$student["classId"]]["courseID"]]["courseCode"]
                    : $courses[$classes[$student["classId"]]["courseID"]]["id"];
                $courseCode = trim($db->escape_string($courseCode));

                $sectionCode = trim($db->escape_string($classes[$student["classId"]]["id"]));
                $courseName = trim($db->escape_string($classes[$student["classId"]]["title"]));
                $firstName = trim($db->escape_string($users["student"][$student["userId"]]["firstName"]));
                $lastName = trim($db->escape_string($users["student"][$student["userId"]]["lastName"]));

                $staffID = "";
                $teacherName = "";
                if (isset($teacherCache[$student["classId"]])) {
                    $staffID = trim($db->escape_string($teacherCache[$student["classId"]]["id"]));
                    $teacherName = trim($db->escape_string($teacherCache[$student["classId"]]["firstName"] . " " . $teacherCache[$student["classId"]]["lastName"]));
                } else {
                    foreach ($enrollments["staff"] as $staffMember) {
                        if ($staffMember["classId"] == $student["classId"] && strtolower($staffMember["primary"]) == "true") {
                            $staffID = $users["staff"][$staffMember["userId"]]["localId"] != "" ? $users["staff"][$staffMember["userId"]]["localId"] : $users["staff"][$staffMember["userId"]]["id"];
                            $staffID = trim($db->escape_string($staffID));

                            $teacherName = trim($db->escape_string($users["staff"][$staffMember["userId"]]["firstName"] . " " . $users["staff"][$staffMember["userId"]]["lastName"]));
                        }
                    }
                }

                foreach ($classes[$student["classId"]]["termIds"] as $termCode) {
                    foreach ($classes[$student["classId"]]["periods"] as $period) {
                        $escapedTerm = trim($db->escape_string($termCode . "-$siteID"));
                        $escapedPeriod = trim($db->escape_string($period));

                        $valuesToImport[] = "(
              '$studentID', '$firstName', '$lastName', '$schoolCode', '$courseCode', '$sectionCode',
              '$courseName', '$staffID', '$teacherName', '$escapedTerm', '$escapedPeriod', $siteID, $schoolYearID
            )";
                        if (count($valuesToImport) == MAX_IMPORT_LIMIT) {
                            insertRows($db, $dbColumns, $valuesToImport);
                            $valuesToImport = [];
                        }
                    }
                }
            }
            if (count($valuesToImport)) {
                insertRows($db, $dbColumns, $valuesToImport);
            }
        }
    } catch (Exception $ex) {
        $error = $ex->getMessage();
    }

    $details = [];
    if (isset($error) && !is_null($error)) {
        $details["error"] = $error;
        $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
    } else {
        $status = CRON_SUCCESS;
    }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}
