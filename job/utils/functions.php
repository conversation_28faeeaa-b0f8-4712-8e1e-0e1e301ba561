<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/
require_once(dirname(__FILE__). '/../../vendor/autoload.php');
use phpseclib\Net\SFTP;

function getSemesterDates($db, $site){
  try{
    $sql = "SELECT options FROM settings WHERE siteID = '$site'";
    $query = $db->query($sql);
    if($query){
      $row = $query->fetch_assoc();
      $options = json_decode($row["options"]);
      $ret = [
              "semester1Start" => $options->semester1Start,
              "semester1End" => $options->semester1End,
              "semester2Start" => $options->semester2Start,
              "semester2End" => $options->semester2End
             ];
    }
  }catch(Exception $e){
    $ret = [];
  }finally{
    return $ret;
  }
}

function getSiteTimeZone($db, $site){
  try{
    $sql = "SELECT time_zone FROM settings WHERE siteID = '$site'";
    $query = $db->query($sql);
    if($query){
      $row = $query->fetch_assoc();
      $ret = $row["time_zone"];
    }
  }catch(Exception $e){
    $ret = null;
  }finally{
    return $ret;
  }
}

function getTermInformation($termCode, $semesterDates, $schoolYear){

  $beginningYear = $schoolYear - 1;

  if($termCode == "Year"){
    $type = "schoolYear";
    $startDate = "$beginningYear-08-01";
    $endDate = "$schoolYear-07-31";

    return ["type" => $type, "startDate" => $startDate, "endDate" => $endDate];
  }else{
    $type = "semester";

    $info = getExportDates($termCode, $semesterDates, $beginningYear);
    $info["type"] = $type;

    return $info;
  }
}

function getExportDates($termCode, $semesterDates, $year){
  if($termCode == "Sem1"){
    return getExportSemester1Dates($semesterDates, $year);
  }else{
    return getExportSemester2Dates($semesterDates, $year+1);
  }
}

function getExportSemester1Dates($semesterDates, $year){
  if($semesterDates["semester1Start"] != "" && $semesterDates["semester1End"] != ""){
    $startDate = date("Y-m-d", strtotime($semesterDates["semester1Start"]));
    $endDate = date("Y-m-d", strtotime($semesterDates["semester1End"]));
  }else{
    $startDate = "$year-08-01";
    $endDate = "$year-12-31";
  }
  return ["startDate" => $startDate, "endDate" => $endDate];
}

function getExportSemester2Dates($semesterDates, $year){
  if($semesterDates["semester2Start"] != "" && $semesterDates["semester2End"] != ""){
    $startDate = date("Y-m-d", strtotime($semesterDates["semester2Start"]));
    $endDate = date("Y-m-d", strtotime($semesterDates["semester2End"]));
  }else{
    $startDate = "$year-01-01";
    $endDate = "$year-07-31";
  }
  return ["startDate" => $startDate, "endDate" => $endDate];
}

function uploadToAbreSftp($sftpConfig, $uploadFilename, $file, &$warnings) {
  try {
    $sftp = new SFTP($sftpConfig->ip);
    if(!$sftp->login($sftpConfig->userName, $sftpConfig->password)){
      throw new Exception("Login to SFTP Server Failed");
    }

    $sftp->put($uploadFilename, $file, SFTP::SOURCE_LOCAL_FILE);
  } catch(Exception $ex) {
    $warnings[] = $ex->getMessage();
  }
}

//Encryption function
function encrypt($string, $ivEncoded, $encryptionKey){
  $iv = base64_decode($ivEncoded);
  return rtrim(base64_encode(openssl_encrypt($string, 'aes-256-cbc', $encryptionKey, $options = 0, $iv)));
}

function ResizePicture($rawimage, $maxsize){

  $image = "data:image/jpeg;base64, ".$rawimage;

  $mime = getimagesize($image);
  $width = $mime[0];

  $imageCreated = imagecreatefromjpeg($image);

  if($width < $maxsize){
    ob_start();
    imagejpeg($imageCreated);
    $image =  ob_get_contents();
    ob_end_clean();
  }else{
    $imageScaled = imagescale($imageCreated, $maxsize);
    ob_start();
    imagejpeg($imageScaled);
    $image =  ob_get_contents();
    ob_end_clean();
  }

  imagedestroy($imageCreated);

  $image = base64_encode($image);

  return $image;
}

function getVersion($fileName){
  preg_match('/_v(\d+\.\d+)/', $fileName, $matches);
  return $matches[1];
}

//trims data and returns NULL if empty
function nullCheck($data){
  return (trim($data) == "") ? 'NULL' : trim($data);
}

function nullCheckWithQuotes($data){
  $data = trim($data);
  return ($data == "") ? "NULL" : "'$data'";
}

/**
 * Takes in an array of required inputs and validates them.
 * Currently, we only want to ensure required fields are not empty.
 * However, this function will eventually take an array of values and their
 * associated validator (regexp/etc) to determine validity.
 * @param requiredFields array of values to validate
 * @return boolean
*/
function validateRequiredFields($requiredFields){
  foreach($requiredFields as $value){
    if($value == ""){
      return false;
    }
  }
  return true;
}

/**
 * Inserts rows into the database
 * @param db database connection
 * @param insertStmt sql insert statement syntax - should contain
 *  "INSERT INTO <table name> (<columns>) VALUES"
 * @param valuesArray array of strings to insert in the format of "(value1, value2, etc)"
 * @return void
 */
function insertRows($db, $insertStmt, $valuesArray, $optionalSql = ""){
  $values = implode(",", $valuesArray);
  $importQuery = "$insertStmt $values $optionalSql";

  $db->query($importQuery);
}

/**
 * Returns information about the contents of the passed in file.
 * @param file the contents of a file
 * @param separator string of characters to split lines
 * @param validColumnCount number of columns to consider the row a valid data set
 * @return array containing information about the contents of the provided file
 *  [
 *    isEmpty => 1/0
 *    hasHeaderRow => 1/0
 *    hasDataRow => 1/0
 *    hasValidDataRow => 1/0
 *  ]
 */
function getFileStructure($file, $lineSeparator, $validColumnCount, $columnSeparator = "\t"){
  $headerRow = strtok($file, $lineSeparator);
  $firstDataRow = strtok($lineSeparator);

  $fileDetails = [];
  if($headerRow !== false){
    $fileDetails["isEmpty"] = 0;
    $fileDetails["hasHeaderRow"] = 1;
  }else{
    $fileDetails["isEmpty"] = 1;
    $fileDetails["hasHeaderRow"] = 0;
  }
  $fileDetails["hasDataRow"] = $firstDataRow !== false;

  $data = str_getcsv($firstDataRow, $columnSeparator);
  $columnCount = count($data);
  $fileDetails["hasValidDataRow"] = $columnCount >= $validColumnCount;

  return $fileDetails;
}

// searches every object in $haystack for a property $prop with value equal to $needle
// and returns the first instance. if none is found, returns null
// NOTE this function does ZERO input validation
function arrayFindFirst($haystack, $prop, $needle){
  foreach($haystack as $value){
    if($value[$prop] == $needle){
      return $value;
    }
  }
  return null;
}

// searches every object in $haystack for a property $prop with value equal to $needle
// and returns an array containing every instance.
// NOTE this function does ZERO input validation
function arrayFindAll($haystack, $prop, $needle){
  return array_reduce($haystack, function($carry, $item) use ($prop, $needle){
    if($item[$prop] == $needle){
      $carry[] = $item;
    }
    return $carry;
  }, []);
}

function getCurrentSchoolYearID($db){
  try{
    $sql = "SELECT id
            FROM abre_term_year WHERE active_year = 1";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);
    $stmt->execute();
    $stmt->bind_result($schoolYearID);
    if(!$stmt->fetch()){
      throw new Exception("School Year Error: No active year in the database");
    }
    $stmt->close();

    return $schoolYearID;
  }catch(Exception $e){
    error_log($e->getMessage());
  }
}

function getCurrentEndSchoolYear($db){
  try{
    $sql = "SELECT end_year
            FROM abre_term_year WHERE active_year = 1";
    $stmt = $db->stmt_init();
    $stmt->prepare($sql);
    $stmt->execute();
    $stmt->bind_result($schoolYear);
    if(!$stmt->fetch()){
      throw new Exception("School Year Error: No active year in the database");
    }
    $stmt->close();

    return $schoolYear;
  }catch(Exception $e){
    error_log($e->getMessage());
  }
}

/**
 * Creates a random unique temporary directory, with specified parameters,
 * that does not already exist (like tempnam(), but for dirs).
 *
 * Created dir will begin with the specified prefix, followed by random
 * numbers.
 *
 * @param string|null $dir Base directory under which to create temp dir.
 *     If null, the default system temp dir (sys_get_temp_dir()) will be
 *     used.
 * @param string $prefix String with which to prefix created dirs.
 * @param int $mode Octal file permission mask for the newly-created dir.
 *     Should begin with a 0.
 * @param int $maxAttempts Maximum attempts before giving up (to prevent
 *     endless loops).
 * @return string|bool Full path to newly-created dir, or false on failure.
 */
function tempdir($dir = null, $prefix = 'tmp_', $mode = 0700, $maxAttempts = 1000){
  /* Use the system temp dir by default. */
  if(is_null($dir)){
    $dir = sys_get_temp_dir();
  }

  /* Trim trailing slashes from $dir. */
  $dir = rtrim($dir, DIRECTORY_SEPARATOR);

  /* If we don't have permission to create a directory, fail, otherwise we will
    * be stuck in an endless loop.
    */
  if(!is_dir($dir) || !is_writable($dir)){
    return false;
  }

  /* Make sure characters in prefix are safe. */
  if(strpbrk($prefix, '\\/:*?"<>|') !== false){
    return false;
  }

  /* Attempt to create a random directory until it works. Abort if we reach
    * $maxAttempts. Something screwy could be happening with the filesystem
    * and our loop could otherwise become endless.
    */
  $attempts = 0;
  do{
    $path = sprintf('%s%s%s%s', $dir, DIRECTORY_SEPARATOR, $prefix, mt_rand(100000, mt_getrandmax()));
  }while(!mkdir($path, $mode) && $attempts++ < $maxAttempts);

  return $path;
}

?>
