<?php

class Mtss {

    public function getMtssIndicatorConfig(int $siteId, mysqli $db){

    try{
        $sql = "SELECT `failing_course_measure`, `assessments`
                FROM `MTSS_indicator_config` WHERE site_id = ?";
        $stmt = $db->stmt_init();
        $stmt->prepare($sql);
        $stmt->bind_param("i", $siteId);
        $stmt->execute();
        $stmt->bind_result($failingCourseMeasureJson,$assessmentJson);
        if(!$stmt->fetch()){
            throw new Exception("Error retrieving assessment data");
        }
        $stmt->close();

        $assessmentConfig = json_decode($assessmentJson, true);
        $lowCourseMeasureArray = json_decode($failingCourseMeasureJson, true);
        $lowPerformingCoursesSql = $lowCourseMeasureArray['d'] == 1 ? " OR letter_grade LIKE '%D%'" : "";

        return [$lowPerformingCoursesSql,$assessmentConfig];

        }catch(Exception $e){
        error_log($e->getMessage());
        }
    }

    public function getIreadyData(int $siteId, int $schoolYearId, mysqli $db){

        try{
            $sql = "SELECT ire.student_id AS `Student ID`, 
                    MAX(ire.percentile) AS `iReady ELA Percentile`, 
                    CASE
                        WHEN MAX(ire.percentile) >= t.percentile_low_risk THEN 'Low Risk'
                        WHEN MAX(ire.percentile) >= t.percentile_medium_risk THEN 'Medium Risk'
                        WHEN MAX(ire.percentile) <= t.percentile_high_risk THEN 'High Risk'
                    END AS `iReady ELA Risk Score`,
                    MAX(irm.percentile) AS `iReady Math Percentile`,
                    CASE
                        WHEN MAX(irm.percentile) >= t.percentile_low_risk THEN 'Low Risk'
                        WHEN MAX(irm.percentile) >= t.percentile_medium_risk THEN 'Medium Risk'
                        WHEN MAX(irm.percentile) <= t.percentile_high_risk THEN 'High Risk'
                    END AS `iReady Math Risk Score`
                    FROM vendor_iready_diagnostic_results_ela AS ire 
                    LEFT JOIN MTSS_thresholds AS t ON t.site_id = ire.site_id
                    LEFT JOIN vendor_iready_diagnostic_results_math AS irm ON ire.student_id = irm.student_id 
                    WHERE ire.site_id = ? AND ire.completion_date != '0000-00-00' AND CAST(? + 2019 AS CHAR) = SUBSTR(ire.academic_year, -4, 4) 
                    AND MONTH(ire.completion_date) IN (8, 9, 10) GROUP BY ire.student_id";
            $stmt = $db->stmt_init();
            $stmt->prepare($sql);
            $stmt->bind_param("ii", $siteId, $schoolYearId);
            $stmt->execute();
            $stmt->bind_result($studentId, $elaPercentile, $elaRisk, $mathPercentile, $mathRisk);

            $iReadyData = [];
            while($stmt->fetch()){
                $iReadyData[$studentId] = [
                    "iready_ela_percentile" => $elaPercentile,
                    "iready_ela_risk" => $elaRisk,
                    "iready_math_percentile" => $mathPercentile,
                    "iready_math_risk" => $mathRisk
                ];
            }

            $stmt->close();
        
            return $iReadyData;
    
    
            }catch(Exception $e){
                error_log($e->getMessage());
            }
        }

    public function getMapData(int $siteId, mysqli $db){

        try{
            $sql = "SELECT mp.StudentID AS `Student ID`,
                        @mathPercentile := (
                            SELECT
                                TestPercentile 
                            FROM
                                Abre_MAPData 
                            WHERE
                                StudentID = mp.StudentID
                                AND siteID = mp.siteID
                                AND Discipline = 'Mathematics'
                            ORDER BY STR_TO_DATE(TestStartDate, '%m/%d/%Y') DESC 
                            LIMIT 1
                        ) AS 'MAP Math Percentile',
                        CASE
                            WHEN @mathPercentile >= t.percentile_low_risk THEN 'Low Risk'
                            WHEN @mathPercentile >= t.percentile_medium_risk THEN 'Medium Risk'
                            WHEN @mathPercentile < t.percentile_medium_risk THEN 'High Risk'
                        END AS 'Math Risk Level',
                        @elaPercentile := (
                            SELECT
                                TestPercentile 
                            FROM
                                Abre_MAPData 
                            WHERE
                                StudentID = mp.StudentID
                                AND siteID = mp.siteID
                                AND (Discipline = 'Language Arts' OR Discipline = 'Reading')
                            ORDER BY STR_TO_DATE(TestStartDate, '%m/%d/%Y') DESC 
                            LIMIT 1
                        ) AS 'MAP ELA/Reading Percentile',
                        CASE
                            WHEN @elaPercentile >= t.percentile_low_risk THEN 'Low Risk'
                            WHEN @elaPercentile >= t.percentile_medium_risk THEN 'Medium Risk'
                            WHEN @elaPercentile < t.percentile_medium_risk THEN 'High Risk'
                        END AS 'MAP ELA Risk Level'
                        FROM Abre_MAPData mp 
                        LEFT JOIN  MTSS_thresholds AS t ON t.site_id = mp.siteID
                        WHERE  mp.siteID = ?
                        GROUP BY  mp.StudentID";
            $stmt = $db->stmt_init();
            $stmt->prepare($sql);
            $stmt->bind_param("i", $siteId);
            $stmt->execute();
            $stmt->bind_result($studentId, $mathPercentile, $mathRiskScore, $elaPercentile, $elaRiskScore);
            
            $mapData = [];
            while($stmt->fetch()){
                $mapData[$studentId] = [
                    "map_math_percentile" => $mathPercentile,
                    "map_math_risk" => $mathRiskScore,
                    "map_ela_percentile" => $elaPercentile,
                    "map_ela_risk" => $elaRiskScore
                ];
            }
        
            return $mapData;
    
            }catch(Exception $e){
                error_log($e->getMessage());
            }
        }

public function getPositveBehaviorOffenses(int $siteId, int $schoolYearId, mysqli $db){

    try{
        $positiveBehaviors = [];
        $sql = "SELECT Offence 
                FROM conduct_offences 
                    WHERE siteID = ? AND school_year_id = ? AND positive_behavior = 1";
        $stmt = $db->stmt_init();
        $stmt->prepare($sql);
        $stmt->bind_param("ii", $siteId, $schoolYearId);
        $stmt->execute();
        $stmt->bind_result($offence);
       
        while ($stmt->fetch()) {
            array_push($positiveBehaviors, $offence);
        }
        $stmt->close();

        if (!empty($positiveBehaviors)) {
            $values = "'" . implode("', '", $positiveBehaviors) . "'";
            $offenseSql = "AND Offence NOT IN ($values)";
        } else {
            $offenseSql = "";
        }

        return $offenseSql;

        }catch(Exception $e){
        error_log($e->getMessage());
        }
    }
}
