<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

use GuzzleHttp\Client;
use Exception;

class VendorLinkAPI
{
    private $clientID;
    private $clientSecret;
    private $baseUri;
    private $subscriptionKey;
    private $accessToken;
    private $accessTokenExpiration;

    public function __construct($clientID, $clientSecret, $baseUri, $subscriptionKey, $accessToken, $accessTokenExpiration)
    {
        $this->clientID = $clientID;
        $this->clientSecret = $clientSecret;
        $this->baseUri = $baseUri;
        $this->subscriptionKey = $subscriptionKey;
        $this->accessToken = $accessToken;
        $this->accessTokenExpiration = $accessTokenExpiration;
    }

    public static function init($clientID, $clientSecret, $baseUri, $subscriptionKey)
    {
        try {
            $client = new Client();
            $response = $client->request("POST", "https://auth.progressbook.com/auth/connect/token", [
                "form_params" => [
                    "client_id" => $clientID,
                    "client_secret" => $clientSecret,
                    "scope" => "vendorlink",
                    "grant_type" => "client_credentials",
                ]
            ]);

            if ($response->getStatusCode() == 200) {
                $accessTokenJSON = json_decode($response->getBody());
                $accessToken = $accessTokenJSON->access_token;
                $expiresIn = $accessTokenJSON->expires_in;
                $accessTokenExpiration = time() + $expiresIn; // Set expiration time

                return new VendorLinkAPI($clientID, $clientSecret, $baseUri, $subscriptionKey, $accessToken, $accessTokenExpiration);
            } else {
                throw new Exception("Bad VendorLink API credentials");
            }
        } catch (Exception $e) {
            error_log($e);
        }
    }

    public function get($endpoint, $params = [])
    {
        try {
            $this->checkAccessToken();  // Check if the token is expired before making a request

            $response = $this->getClient()->request("GET", $endpoint, ["query" => $params]);
            $ret = json_decode($response->getBody(), true);
        } catch (Exception $e) {
            error_log($e);
        }
        return $ret;
    }

    public function post($endpoint, $params = [])
    {
        try {
            $this->checkAccessToken();  // Check if the token is expired before making a request

            $response = $this->getClient()->request("POST", $endpoint, ["json" => $params]);
            $ret = json_decode($response->getBody(), true);
        } catch (Exception $e) {
            error_log($e);
        }
        return $ret;
    }

    public function put($endpoint, $params = [])
    {
        try {
            $this->checkAccessToken();  // Check if the token is expired before making a request

            $response = $this->getClient()->request("PUT", $endpoint, ["json" => $params]);
            $ret = json_decode($response->getBody(), true);
        } catch (Exception $e) {
            error_log($e);
        }
        return $ret;
    }

    private function checkAccessToken()
    {
        // If the token is expired or about to expire, refresh it
        if (time() >= $this->accessTokenExpiration - 60) {
            $this->refreshAccessToken();
        }
    }

    private function refreshAccessToken()
    {
        try {
            $client = new Client();
            $response = $client->request("POST", "https://auth.progressbook.com/auth/connect/token", [
                "form_params" => [
                    "client_id" => $this->clientID,
                    "client_secret" => $this->clientSecret,
                    "scope" => "vendorlink",
                    "grant_type" => "client_credentials",
                ]
            ]);

            if ($response->getStatusCode() == 200) {
                $accessTokenJSON = json_decode($response->getBody());
                $this->accessToken = $accessTokenJSON->access_token;
                $expiresIn = $accessTokenJSON->expires_in;
                $this->accessTokenExpiration = time() + $expiresIn; // Update the expiration time
            } else {
                throw new Exception("Failed to refresh access token.");
            }
        } catch (Exception $e) {
            error_log($e);
        }
    }

    private function getClient()
    {
        return new Client([
            "base_uri" => $this->baseUri,
            "headers" => [
                "Authorization" => "Bearer $this->accessToken",
                "Ocp-Apim-Subscription-Key" => $this->subscriptionKey,
            ],
        ]);
    }
}
