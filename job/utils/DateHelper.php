<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

require_once(dirname(__FILE__) . '/functions.php');

class DateHelper{

  /**
   * Converts a time string into a DateTime object. This works with both
   * db TimeStamps (in UTC) and db DateTimes (in EDT), provided the correct $originalTimeZone
   * is set
   * @param timeString a valid DateTime formatted time string
   * @param newTimeZone a DateTimeZone representing the timezone to convert to
   * @param originalTimeZone a DateTimeZone representing the timezone of the
   *  source time
   * @return DateTime
   */
  public static function convertStringToDateTime($timeString, $newTimeZone, $originalTimeZone){
    $dt = new \DateTime($timeString, $originalTimeZone);
    $dt->setTimezone($newTimeZone);

    return $dt;
  }

  /**
   * Helper method that utilizes convertStringToDateTime to convert a valid DateTime
   * string to the user's local timezone
   * @param db a database connection
   * @param timeString a valid DateTime formatted time string
   * @param siteID site identifier
   * @return DateTime a DateTime object in the user's timezone
   */
  public static function getDateInLocalTimeZone($db, $timeString, $siteID){
    $localTimeZone = self::getUserTimeZone($db, $siteID);

    return self::convertStringToDateTime($timeString, $localTimeZone, new \DateTimeZone('UTC'));
  }

  /**
   * Helper method that utilizes convertStringToDateTime to convert a valid DateTime
   * string to UTC
   * @param db a database connection
   * @param timeString a valid DateTime formatted time string
   * @param siteID site identifier
   * @return DateTime a DateTime object in UTC
   */
  public static function getLocalDateInUTC($db, $timeString, $siteID){
    $localTimeZone = self::getUserTimeZone($db, $siteID);

    return self::convertStringToDateTime($timeString, new \DateTimeZone('UTC'), $localTimeZone);
  }

  /**
   * Gets the user's local timezone
   * @param db a database connection
   * @param siteID site identifier
   */
  public static function getUserTimeZone($db, $siteID){
    $districtTimeZone = getSiteTimeZone($db, $siteID);

    return new \DateTimeZone($districtTimeZone);
  }

  /**
   * Determines date format to display depending on time relative to a week ago
   * @param db a database connection
   * @param date DateTime to format
   * @return string with formatted date information
   */
  public static function getFormattedDate($db, $date, $siteID){
    $sevenDaysAgo = new \DateTime("-7 days", self::getUserTimeZone($db, $siteID));

    return $date < $sevenDaysAgo
      ? $date->format("F j")." at ".$date->format("g:i A")
      : $date->format("l")." at ".$date->format("g:i A");
  }

  /**
   * Gets the "now" as a DateTime for the current user (this respects the user's
   * timezone).
   * @param db a database connection
   * @param siteID site identifier
   * @returns a DateTime of "now" offset appropriately for the user
   */
  public static function getNowForUser($db, $siteID){
    return new \DateTime("now", self::getUserTimeZone($db, $siteID));
  }

}
