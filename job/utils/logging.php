<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/

const CRON_FAILURE = 0;
const CRON_NOT_RUN = 100;
const CRON_SUCCESS = 200;

/**
 * Provides static methods for logging cron results
 */
class Logger {
    /**
     * Logs the cron result to the cron_result table
     * @param db the db in which to save results
     * @param siteID the site id that this cron ran for
     * @param cronName the name of this cron (used for display)
     * @param status int of cron result status (should use the CRON_* consts declared here)
     * @param details an associative array (or object) of details that will be serialized into json
     *                and stored in the result row
     */
    public static function logCronResult($db, $siteID, $cronName, $status, $details) {
        $detailsJson = json_encode($details);

        if ($status == CRON_FAILURE) {
        $statusMessage = "Failure";
        } elseif ($status == CRON_NOT_RUN) {
        $statusMessage = "Not Run";
        } elseif ($status == CRON_SUCCESS) {
        $statusMessage = "Success";
        }

        $sql = "INSERT INTO cron_result (site_id, cron_name, result, details)
                VALUES (?, ?, ?, ?)";
        $stmt = $db->stmt_init();
        $stmt->prepare($sql);
        $stmt->bind_param("isss", $siteID, $cronName, $statusMessage, $detailsJson);
        $stmt->execute();
        $stmt->close();
    }

    public static function logCronStart($db, $siteID, $cronName) {

        $uuid = uniqid('', true);
        $result = "Running";

        $sql = "INSERT INTO cron_result (site_id, cron_name, result, start_on, uuid)
                VALUES (?, ?, ?, NOW(), ?)";
        $stmt = $db->stmt_init();
        $stmt->prepare($sql);
        $stmt->bind_param("isss", $siteID, $cronName, $result, $uuid);
        $stmt->execute();
        $stmt->close();
        return $uuid;
    }

    public static function logCronFinish($db, $siteID, $cronName, $status, $details, $uuid) {

        $detailsJson = json_encode($details);

        if ($status == CRON_FAILURE) {
            $statusMessage = "Failure";
        }

        if ($status == CRON_NOT_RUN) {
            $statusMessage = "Not Run";
        }

        if ($status == CRON_SUCCESS) {
            $statusMessage = "Success";
        }

        $sql = "UPDATE cron_result
                SET result = ?, details = ?, end_on = NOW()
                WHERE site_id = ? AND cron_name = ? AND uuid = ? 
                AND start_on >= NOW() - INTERVAL 24 HOUR";
        $stmt = $db->stmt_init();
        $stmt->prepare($sql);
        $stmt->bind_param("ssiss", $statusMessage, $detailsJson, $siteID, $cronName, $uuid);
        $stmt->execute();
        $stmt->close();
    }
}
