<?php
/*
* Copyright (C) 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the Affero General Public License version 3
* as published by the Free Software Foundation.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU Affero General Public License for more details.
*
* You should have received a copy of the Affero General Public License
* version 3 along with this program.  If not, see https://www.gnu.org/licenses/agpl-3.0.en.html.
*/
use GuzzleHttp\Client;
use Exception;

class OneRosterAPI{
  private $clientID;
  private $clientSecret;
  private $baseUri;
  private $accessToken;

  public function __construct($clientID, $clientSecret, $baseUri, $accessToken) {
    $this->clientID = $clientID;
    $this->clientSecret = $clientSecret;
    $this->baseUri = $baseUri;
    $this->accessToken = $accessToken;
  }

  public static function init($service, $clientID, $clientSecret, $baseUri, $authUri){
    try{
      $client = new Client();
      if($service == "GG4L"){
        $response = $client->request("POST", $authUri, [
          "form_params" => [
            "grant_type" => "client_credentials"
          ],
          "headers" => [
            "Authorization" => "Basic " . base64_encode(rawurlencode($clientID) . ":" . rawurlencode($clientSecret)),
          ]
        ]);
      }elseif($service == "SIS"){
        $response = $client->request("POST", $authUri, [
          "form_params" => [
            "client_id" => $clientID,
            "client_secret" => $clientSecret,
            "grant_type" => "client_credentials"
          ]
        ]);
      }

      if($response->getStatusCode() == 200){
        $accessTokenJSON = json_decode($response->getBody());
        $accessToken = $accessTokenJSON->access_token;
      }else{
        throw new Exception("Bad OneRoster API credentials");
      }

      return new OneRosterAPI($clientID, $clientSecret, $baseUri, $accessToken);
    }catch(Exception $e){
      error_log($e);
    }finally{
      if(isset($db)){
        $db->close();
      }
    }
  }

  public function get($endpoint, $params = []){
    try{
      $response = $this->getClient()->request("GET", $endpoint, ["query" => $params]);
      $ret = json_decode($response->getBody(), true);
    }catch(Exception $e){
      error_log($e);
      return false;
    }
     return $ret;
  }

  public function post($endpoint, $params = []){
    try{
      $response = $this->getClient()->request("POST", $endpoint, ["json" => $params]);
      $ret = json_decode($response->getBody(), true);
    }catch(Exception $e){
      error_log($e);
      return false;
    }
     return $ret;
  }

  public function put($endpoint, $params = []){
    try{
      $response = $this->getClient()->request("PUT", $endpoint, ["json" => $params]);
      $ret = json_decode($response->getBody(), true);
    }catch(Exception $e){
      error_log($e);
      return false;
    }
     return $ret;
  }

  private function getClient(){
    return new Client([
      "base_uri" => $this->baseUri,
      "headers" => [
        "Authorization" => "Bearer $this->accessToken",
      ],
    ]);
  }

 }
?>