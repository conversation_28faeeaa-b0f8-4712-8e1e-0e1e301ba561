<?php
/*
* Copyright 2016-2021 Abre.io Inc.
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

require_once(dirname(__FILE__). '/../../vendor/autoload.php');
require_once(dirname(__FILE__). '/../utils/functions.php');
require_once(dirname(__FILE__) . '/../utils/logging.php');
use phpseclib\Net\SFTP;

function runJob($db, $siteID, $config)
{
    $cronName = 'GG4L Demographics SSID';
    try {
        $uuid = Logger::logCronStart($db, $siteID, $cronName);

    //File
    $fileName = "GG4L_Demographics_SSID";

    //Define
    define("SAFE_COLUMN_COUNT", 16);
    define("MAX_IMPORT_LIMIT", 2);
    $currentSchoolYearID = getCurrentSchoolYearID($db);
    $error = null;
    $skip = null;
        $separator = "\r\n";

    //Connect
    $sftp = new SFTP($config->sftp->ip);
    if (!$sftp->login($config->sftp->userName, $config->sftp->password)) {
        throw new Exception("Login to SFTP failed.");
    }

    //Find File
    $cronFile = $sftp->get("$fileName.txt");
    if (!$cronFile) {
        $cronFile = $sftp->get("$fileName.csv");
        $fileType = "csv";
        $columnSeparator = ",";
    } else {
        $cronFile = $sftp->get("$fileName.txt");
        $fileType = "txt";
        $columnSeparator = "\t";
    }

    if (!$cronFile) {
        $skip = true;
        throw new Exception("No file found.");
    } else {
        $fileDetails = getFileStructure($cronFile, $separator, SAFE_COLUMN_COUNT, $columnSeparator);
        if ($fileDetails["isEmpty"]) {
            $skip = true;
            throw new Exception("File is empty.");
        } elseif ($fileDetails["hasHeaderRow"] && !$fileDetails["hasDataRow"]) {
            $skip = true;
            throw new Exception("File only contains a header row.");
        } elseif (!$fileDetails["hasValidDataRow"]) {
            throw new Exception("No valid data row found.");
        }
    }

    // Prepare the update query (using placeholders for values)
    $updateQuery = "UPDATE Abre_Students
                    SET Gender = ?,
                        EthnicityCode = ?,
                        EthnicityDescription = ?,
                        DateOfBirth = ?,
                        SSID = ?,
                        IEP = ?,
                        Gifted = ?,
                        EconomicallyDisadvantaged = ?,
                        Title1 = ?,
                        Title3 = ?,
                        ELL = ?,
                        Status504 = ?,
                        Username = ?,
                        Password = ?,
                        okay_to_publish = ?
                    WHERE siteID = ?
                    AND school_year_id = ?
                    AND StudentId = ?";

    // Prepare the statement
    $stmt = $db->prepare($updateQuery);

    $rowCounter = 0;

    $line = strtok($cronFile, $separator);
    $line = strtok($separator); // skip header row

    do {
        if ($fileType == "txt") {
            $data = str_getcsv($line, $columnSeparator);
        } else {
            $data = str_getcsv($line, $columnSeparator);
        }

        if (count($data) >= SAFE_COLUMN_COUNT) {
            $rowCounter++;

            $studentId = trim($db->escape_string($data[0]));
            $ssid = trim($db->escape_string($data[1]));
            $gender = trim($db->escape_string($data[2]));
            $ethnicityCode = trim($db->escape_string($data[3]));
            $ethnicityDescription = trim($db->escape_string($data[4]));
            $dateOfBirth = trim($db->escape_string($data[5]));
            $iep = trim($db->escape_string($data[6]));
            $gifted = trim($db->escape_string($data[7]));
            $economicallyDisadvantaged = trim($db->escape_string($data[8]));
            $title1 = trim($db->escape_string($data[9]));
            $title3 = trim($db->escape_string($data[10]));
            $ell = trim($db->escape_string($data[11]));
            $status504 = trim($db->escape_string($data[12]));
            $userName = trim($db->escape_string($data[13]));
            $password = trim($db->escape_string($data[14]));
            $okToPublish = trim($db->escape_string($data[15]));

            if (preg_match('/^\d{4}\/\d{2}\/\d{2}$/',$dateOfBirth))  {
                $dateOfBirth = str_replace("/", "-", $dateOfBirth);
            }

            // Bind parameters to the prepared statement
            $stmt->bind_param("ssssssssssssssiiis",
                $gender,
                $ethnicityCode,
                $ethnicityDescription,
                $dateOfBirth,
                $ssid,
                $iep,
                $gifted,
                $economicallyDisadvantaged,
                $title1,
                $title3,
                $ell,
                $status504,
                $userName,
                $password,
                $okToPublish,
                $siteID,
                $currentSchoolYearID,
                $studentId
            );

            // Execute the prepared statement
            $stmt->execute();
        }

        $line = strtok($separator);
    } while ($line !== false);

    // Close the prepared statement
    $stmt->close();

  }catch(Exception $ex){
        $error = $ex->getMessage();
  }

  $details = [];
  if(isset($error) && !is_null($error)){
    $details["error"] = $error;
    $status = isset($skip) && $skip ? CRON_NOT_RUN : CRON_FAILURE;
  }else{
    $details = [
      "rowsInserted" => $rowCounter
    ];
    $status = CRON_SUCCESS;
  }

    Logger::logCronFinish($db, $siteID, $cronName, $status, $details, $uuid);
}

?>
