const client = require('ssh2-sftp-client'),
  db = require('./db.js'),
  temp = require('tmp'),
  fs = require('fs'),
  _ = require('lodash');

var sftp = new client(); //new instance of sftp client connection

var remoteFileUsed;
var itemsFound;

const args = process.argv.slice(2); //get arguments
if (args.length < 3) { //cancel if invalid arguments
  console.log(JSON.stringify({
    success: false,
    error: "no siteID, username, or private key file provided"
  }));
  process.exit();
}
var siteID = args[0];
var userName = args[1];
var keyPath = args[2];

var tempPath = createTempFile(); //create temp file to prepare for download

sftp.connect({ //make connection
  host: 'upload.iepanywhere.com',
  port: 22,
  username: userName,
  privateKey: fs.readFileSync(keyPath)
}).then(() => { //get directory list ordered by date
  return sftp.list('/download/docviewer');
}).then(data => { //get most recent folder and download csv
  var sorted = _.sortBy(data, x => x.name);
  var latestDir = sorted[sorted.length - 1].name;
  remoteFileUsed = latestDir + '/documents.csv';
  return sftp.fastGet(`/download/docviewer/${latestDir}/documents.csv`, tempPath);
}).then(() => { //discard connection
  sftp.end();
}).catch((error) => { //catch sftp connection errors
  sftp.end();
  console.log(JSON.stringify({
    success: false,
    remoteFileUsed,
    error: error.message
  }));
  process.exit();
}).then(() => {//make database connection
  db().then(conn => //create temp table
    conn.query(`CREATE TEMPORARY TABLE iepanywheretemp
                (StudentID INT NOT NULL,
                Form TEXT NOT NULL,
                StartDate TEXT NOT NULL,
                EndDate TEXT NOT NULL,
                Url TEXT NOT NULL)`)
      .then(() =>  //load csv file into temp table
        conn.query(`LOAD DATA LOCAL INFILE '${tempPath}'
                  INTO TABLE iepanywheretemp
                  FIELDS TERMINATED BY ','
                  IGNORE 1 LINES`)
      ).then(() =>
        conn.query(`SELECT COUNT(*) FROM iepanywheretemp`).then((data) => {
          itemsFound = data[0]['COUNT(*)'];
        })
      ).then(() => //extract url from garbage samegoal puts around it
        conn.query(`UPDATE iepanywheretemp
                  SET Url = SUBSTRING_INDEX(SUBSTRING(Url FROM POSITION('href=""' IN Url) + 7), '""', 1)`)
      ).then(() => //set records in real table for current set to outdated
        conn.query(`UPDATE iepanywhere_form SET Active = 0 WHERE SiteID = ${siteID}`) //add site id
      ).then(() => //upsert new set of records into real table
        conn.query(`REPLACE INTO iepanywhere_form
                  (StudentID, Form, Url, Active, SiteID)
                  SELECT StudentID, Form, Url, 1, ${siteID}
                  FROM iepanywheretemp`)
      ).then(() => //update abre links
        conn.query(`INSERT INTO
                    iepanywhere_link (StudentID, Form, RealURL, Active, SiteID, AbreURL)
                  SELECT
                    StudentID, Form, Url, Active, SiteID, CONCAT(StudentID, '-', SiteID, '-', SHA2(CONCAT(StudentID, Form, SiteID), 256))
                  FROM
                  iepanywhere_form ON DUPLICATE KEY
                  UPDATE
                    RealURL =
                  values(RealURL),
                    Active =
                  values(Active)`)
      ).then(() => { //report success
        conn.end();
        console.log(JSON.stringify({
          success: true,
          remoteFileUsed,
          itemsFound,
        }));
      }).catch(error => {
        console.log(JSON.stringify({
          success: false,
          remoteFileUsed,
          itemsFound,
          error: error.message
        }));
        process.exit();
      })
  )
}).catch((error) => { //catch sql errors
  console.log(JSON.stringify({
    success: false,
    remoteFileUsed,
    itemsFound,
    error: error.message
  }));
  process.exit();
});

function createTempFile() {
  var tempObj = temp.fileSync();
  return tempObj.name;
}
