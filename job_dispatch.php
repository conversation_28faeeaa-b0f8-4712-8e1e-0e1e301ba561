<?php

$configJson = file_get_contents('php://input');
$config = json_decode($configJson);

try {
    $db = setupDb($config->environment->db);
    switch ($config->file) {
        //third party data imports
        case "Abre_NWEA_MAP_Download":
            require_once('job/import_vendor/Abre_NWEA_MAP_Download.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_IEPAnywhere":
            require_once('job/import_vendor/Abre_IEPAnywhere.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "TerraNova":
            require_once('job/import_vendor/Abre_TerraNova.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Iowa_v1.0":
            require_once('job/import_vendor/Abre_Iowa_Run_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "CogAT_v1.0":
            require_once('job/import_vendor/Abre_CogAT_Run_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "iReady_Math_v1.0":
            require_once('job/import_vendor/Abre_iReady_Math_Run_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "iReady_ELA_v1.0":
            require_once('job/import_vendor/Abre_iReady_ELA_Run_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "iReady_Math_v2.0":
            require_once('job/import_vendor/v2.0/Abre_iReady_Math.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "iReady_ELA_v2.0":
            require_once('job/import_vendor/v2.0/Abre_iReady_ELA.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "common_core_import":
            require_once('job/import_vendor/common_core_import_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "OELPA_v1.0":
            require_once('job/import_vendor/Abre_OELPA_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "PSSA_v1.0":
            require_once('job/import_vendor/Abre_PSSA_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Keystone_v1.0":
            require_once('job/import_vendor/Abre_Keystone_v1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "PSAT":
            require_once('job/import_vendor/Abre_PSAT.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "ProCore":
            require_once('job/import_vendor/Abre_ProCore.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Public_School_Works":
            require_once('job/import_vendor/Abre_Public_School_Works.php');
            runJob($db, $config->target, $config->configs);
            break;
        //SIS data imports
        case "Abre_AD":
            require_once('job/sis/Abre_AD.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance":
            require_once('job/sis/Abre_Attendance.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance_v1.0":
            require_once('job/sis/v1.0/Abre_Attendance.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance_v2.0":
            require_once('job/sis/Abre_Attendance_2.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance_Cumulative":
            require_once('job/sis/Abre_Attendance_Cumulative.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance_Cumulative_v2.0":
            require_once('job/sis/Abre_Attendance_Cumulative_2.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance_Cumulative_v3.0":
            require_once('job/sis/Abre_Attendance_Cumulative_3.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Attendance_State_Metrics":
            require_once('job/sis/Abre_Attendance_State_Metrics.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Courses":
            require_once('job/sis/Abre_Courses.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Gifted":
            require_once('job/sis/Abre_Gifted.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Gifted_v1.0":
            require_once('job/sis/v1.0/Abre_Gifted.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_EL":
            require_once('job/sis/Abre_EL.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_KRA":
            require_once('job/sis/Abre_KRA.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_OGT":
            require_once('job/sis/Abre_OGT.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_ParentContacts":
            require_once('job/sis/Abre_ParentContacts.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_ParentContacts_v1.0":
            require_once('job/sis/v1.0/Abre_ParentContacts.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_SAT":
            require_once('job/sis/Abre_SAT.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Staff":
            require_once('job/sis/Abre_Staff.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StaffSchedules":
            require_once('job/sis/Abre_StaffSchedules.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Student_Counselors":
            require_once('job/sis/Abre_Student_Counselors.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentACT":
            require_once('job/sis/Abre_StudentACT.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentAP":
            require_once('job/sis/Abre_StudentAP.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentAssessments":
            require_once('job/sis/Abre_StudentAssessments.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentDiscipline":
            require_once('job/sis/Abre_StudentDiscipline.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentGrades":
            require_once('job/sis/Abre_StudentGrades.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Students":
            require_once('job/sis/Abre_Students.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Students_v1.0":
            require_once('job/sis/v1.0/Abre_Students.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentSchedules":
            require_once('job/sis/Abre_StudentSchedules.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentUnweightedGPA":
            require_once('job/sis/Abre_StudentUnweightedGPA.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentWeightedGPA":
            require_once('job/sis/Abre_StudentWeightedGPA.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Transcript":
            require_once('job/sis/Abre_Transcript.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Assignments":
            require_once('job/sis/Abre_Assignments.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Student_Files_Migration":
            require_once('job/migrate_student_files.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Grades":
            require_once('job/sis/Abre_Grades.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Grades_2.0":
            require_once('job/sis/Abre_Grades_2.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentCredits":
            require_once('job/sis/Abre_StudentCredits.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentCredits_2.0":
            require_once('job/sis/Abre_StudentCredits_2.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Behavior":
            require_once('job/sis/Abre_Behavior.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Discipline":
            require_once('job/sis/Abre_Discipline.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Discipline_2.0":
            require_once('job/sis/Abre_Discipline_2.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentDemographics":
            require_once('job/sis/Abre_StudentDemographics.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_StudentProgram":
            require_once('job/sis/Abre_StudentProgram.php');
            runJob($db, $config->target, $config->configs);
            break;
        //New Imports
        case "Abre_Config_AD":
        case "Abre_Config_Attendance":
        case "Abre_Config_Courses":
        case "Abre_Config_Staff_Schedules":
        case "Abre_Config_Student_Schedules":
        case "iReady_Diagnostic_ELA":
        case "iReady_Diagnostic_Math":
        case "iReady_Literacy_Benchmark_ELA":
        case "Inview_v1.0":
        case "Texas_CCMR":
        case "Abre_Ohio_Seals":
        case "Abre_Staff_Supplementary":
        case "Student_Interventions":
        case "TeacherEase_Grades":
        case "TeacherEase_Attendance":
        case "TeacherEase_Behavior":
        case "Illuminate_FastBridge_CBMR_English":
        case "Compton_Custom_Attendance":
        case "Compton_Custom_Attendance_Historical":
        case "Illuminate_FastBridge_AutoReading":
        case "Illuminate_FastBridge_aReading":
        case "Illuminate_FastBridge_Early_Reading_English":
        case "Illuminate_FastBridge_SAEBRS_2":
        case "Illuminate_FastBridge_aMath":
        case "Illuminate_FastBridge_Early_Math_2":
        case "Birmingham_Custom_Access":
        case "Aleks":
        case "District_Course_Credits":
        case "CSS_EL_Engagement_Logs":
        case "Lift_Student_Mapping":
        case "Canvas_Grades":
        case "Illuminate_FastBridge_CBMMath_Automaticity":
        case "Illuminate_FastBridge_CBMMath_Process":
        case "Illuminate_FastBridge_CBMmath_CAP":
        case "District_Industry_Credentials":
        case "Illuminate_FastBridge_Comprehension_Efficiency":
        case "CSS_Student_Detail":
        case "Abre_ACT_1.0":
        case "Compton_Demographics_Timeframe":
        case "District_Attendance_Cumulative_Daily":
        case "District_Finance_Projects":
        case "Renaissance_Freckle_Ela":
        case "Renaissance_Freckle_Math":
        case "Renaissance_Freckle_Science":
        case "Lexia_Core5_Cumulative":
        case "Lexia_PowerUp_Cumulative":
        case "Lexia_LE_Cumulative":
        case "Renaissance_Lalilo_Metadata":
        case "Renaissance_Lalilo_Student_Activity":
        case "Renaissance_Lalilo_Student_Registrations":
        case "District_Observation_Feedback":
        case "DIBELS_8_Progress_Monitoring":
        case "Amplify_DIBELS":
        case "Illuminate_FastBridge_AutoReading_PM":
        case "Illuminate_FastBridge_CBMMath_Automaticity_PM":
        case "Illuminate_FastBridge_CBMmath_CAP_PM":
        case "Illuminate_FastBridge_CBMMath_Process_PM":
        case "Illuminate_FastBridge_CBMR_English_PM":
        case "Illuminate_FastBridge_Comprehension_Efficiency_PM":
        case "Illuminate_FastBridge_Early_Math_PM":
        case "Illuminate_FastBridge_Early_Reading_English_PM":
        case "Abre_OST_EVAAS_Projections":
        case "Abre_Student_Enrollment_Dates":
        case "District_Student_Programs":
        case "Abre_OST_Benchmark":
        case "District_Course_Credits_To_Student_Credits_Table":
        case "Imagine_Learning":
        case "ELOP_Expanded_Learning_Opportunities_Program":
        case "Abre_Student_Schedules_Supplemental":
        case "TeacherEase_Attendance_Cumulative":
        case "TeacherEase_Attendance_Absences":
        case "TeacherEase_Behavior_Logs":
        case "Abre_Iowa_v2":
        case "Abre_CogAT_v2":
        case "Aperture_DESSA_Student_Wellness_Assessment":
        case "District_Student_Alerts":
        case "Grad_Plan_Details":
        case "Abre_Student_Social_Workers":
        case "District_Texas_CCMR_Secondary":
        case "District_Attendance_Independent_Study":
        case "District_Parent_Contact_Log":
        case "Abre_Attendance_Cumulative_v4.0":
        case "District_Future_Enrollment":
        case "iReady_ELA_Early_Literacy_Dyslexia_Screener":
        case "District_Student_Tags":
        case "District_School_Enrollment_Detail":
        case "ELLevation_ELL_Status":
        case "District_Students_Residency":
        case "District_Home_School":
        case "District_Course_Subjects":
        case "District_Texas_CCMR_Primary":
        case "SPED_Forms_IEP_Out":
        case "District_Observation_Feedback_25_26":
            require_once('job/Abre_SFTP_Import.php');
            runJob($db, $config->file, $config->target, $config->configs);
            break;
        //Resonant Imports
        case "Abre_Resonant_Overall_Scores":
            require_once('job/import_vendor/Abre_Resonant_Overall_Scores.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Resonant_Category_Scores":
            require_once('job/import_vendor/Abre_Resonant_Category_Scores.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Resonant_Item_Scores":
            require_once('job/import_vendor/Abre_Resonant_Item_Scores.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Resonant_Tokens":
            require_once('job/import_vendor/Abre_Resonant_Tokens.php');
            runJob($db, $config->target, $config->configs);
            break;
        //GG4L Supplimental
        case "GG4L_Demographics":
            require_once('job/gg4l/GG4L_Demographics.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "GG4L_Demographics_SSID":
            require_once('job/gg4l/GG4L_Demographics_SSID.php');
            runJob($db, $config->target, $config->configs);
            break;
        //one roster imports
        case "One_Roster_1.1_CSV":
            require_once('job/one_roster_1.1.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_CSV_Staff_Alternate_Email":
            require_once('job/one_roster_1.1_staff_alternate_email.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_CSV_Fort_Worth":
            require_once('job/one_roster_1.1_fort_worth.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_API":
            require_once('job/one_roster_1.1_api.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_API_Child_Terms":
            require_once('job/one_roster_1.1_api_child_terms.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_API_Child_Terms_2.0":
            require_once('job/one_roster_1.1_api_child_terms_2.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_ClassLink_API_Child_Terms":
            require_once('job/one_roster_1.1_api_child_terms_classlink.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "One_Roster_1.1_ClassLink_API_Child_Terms_Full":
            require_once('job/one_roster_1.1_api_child_terms_classlink_full.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Illuminate_Renaissance_DNA":
            require_once('job/import_vendor/Abre_Illuminate_Renaissance_DNA_Assessments.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Illuminate_FastBridge_Math":
            require_once('job/import_vendor/Abre_Illuminate_FastBridge_Math.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Illuminate_FastBridge_Reading":
            require_once('job/import_vendor/Abre_Illuminate_FastBridge_Reading.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Illuminate_FastBridge_Early_Math":
            require_once('job/import_vendor/Abre_Illuminate_FastBridge_Early_Math.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Illuminate_FastBridge_Early_Reading":
            require_once('job/import_vendor/Abre_Illuminate_FastBridge_Early_Reading.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Illuminate_FastBridge_SAEBRS":
            require_once('job/import_vendor/Abre_Illuminate_FastBridge_SAEBRS.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Mastery_Connect":
            require_once('job/import_vendor/Abre_MasteryConnect.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Learnosity":
            require_once('job/import_vendor/Abre_Learnosity.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Aeries_API":
            require_once('job/import_vendor/Abre_Aeries_API.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Schoology_Grades":
            require_once('job/import_vendor/Schoology_Grades.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Rethink_Education_Import":
            require_once('job/import_vendor/Rethink_Education.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "TeachWorks":
            require_once('job/import_vendor/TeachWorks.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "AIMSweb Import":
            require_once('job/import_vendor/AIMSweb.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Imagine_Learning_MyPath":
            require_once('job/import_vendor/imagine_learning_mypath.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Imagine_Learning_Language_Literacy":
            require_once('job/import_vendor/imagine_learning_language_literacy.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Imagine_Learning_Math":
            require_once('job/import_vendor/imagine_learning_math.php');
            runJob($db, $config->target, $config->configs);
            break;
        //vendorlink data imports
        case "Abre_VendorLink":
            require_once('job/Abre_Vendorlink.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorLink_Discipline":
            require_once('job/Abre_Vendorlink_Discipline.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorLink_Student_Pictures":
            require_once('job/vendorlink/Abre_VendorLink_SIS_StudentPictures.php');
            runJob($db, $config->target, $config->configs);
            break;
        //sends pics to GCS bucket
        case "Abre_VendorLink_Student_Pictures_Bucket":
            require_once('job/vendorlink/Abre_VendorLink_SIS_StudentPictures_Bucket.php');
            runJob($db, $config->target, $config->configs);
            break;
        //vendor exports
        case "Abre_VendorExport_AimsWebPlus":
            require_once('job/exports/Abre_VendorExport_AimsWebPlus.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_Canvas":
            require_once('job/exports/Abre_VendorExport_Canvas.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Canvas_Grades_Daily":
            require_once('job/exports/Canvas_Grades_Daily.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_Cengage":
            require_once('job/exports/Abre_VendorExport_Cengage.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_DiscoverEducation":
            require_once('job/exports/Abre_VendorExport_DiscoverEducation.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_Ellevation":
            require_once('job/exports/Abre_VendorExport_Ellevation.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_HoughtonMifflinHarcourt_Ross":
            require_once('job/exports/Abre_VendorExport_HoughtonMifflinHarcourt_Ross.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_HoughtonMifflinHarcourt":
            require_once('job/exports/Abre_VendorExport_HoughtonMifflinHarcourt.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_iReady":
            require_once('job/exports/Abre_VendorExport_iReady.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_iReady_OneRoster":
            require_once('job/exports/Abre_VendorExport_iReady_OneRoster.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_LearningAZ":
            require_once('job/exports/Abre_VendorExport_LearningAZ.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_McGrawHill":
            require_once('job/exports/Abre_VendorExport_McGrawHill.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_Pearson":
            require_once('job/exports/Abre_VendorExport_Pearson.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_MobyMax":
            require_once('job/exports/Abre_VendorExport_MobyMax.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_VendorExport_ApexLearning":
            require_once('job/exports/Abre_VendorExport_ApexLearning.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_NWEA_Rostering":
            require_once('job/exports/Abre_NWEA_Rostering_Export.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Resonant_Rostering":
            require_once('job/exports/Abre_VendorExport_ResonantEducation.php');
            runJob($db, $config->target, $config->configs);
            break;
        //processing
        case "Processing_Class360":
            require_once('job/processing/Process_Class360.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Processing_MTSS":
            require_once('job/processing/Process_MTSS.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "SFTP_to_GCS":
            require_once('job/sis/Abre_SFTP_to_GCS.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Josh_Stored_Procedures":
            require_once('job/processing/Process_StoredProcedures_Josh.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "Abre_Notion_Sync":
            require_once('job/processing/notion.php');
            runJob($db, $config->target, $config->configs);
            break;
        //abre exports
        case "abre_partner_export_v1.0":
            require_once('job/exports/abre_partner_exportv1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "abre_behavior_export_v1.0":
            require_once('job/exports/abre_behavior_exportv1.0.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "abre_behavior_export_v1.0_alternate":
            require_once('job/exports/abre_behavior_exportv1.0_alternate.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "LearnosityFull":
            require_once('job/import_vendor/Abre_Learnosity_Full.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "AcademicBenchmark":
            require_once('job/import_vendor/Academic_Benchmark.php');
            runJob($db, $config->target, $config->configs);
            break;
        case "abre_plans_export":
            require_once('job/exports/abre_plans_export.php');
            runJob($db, $config->target, $config->configs);
            break;
        default:
            throw new Exception("Unrecognized Job File. $config->file");
    }
} catch (Exception $ex) {
    error_log($ex);
} finally {
    if (isset($db)) {
        $siteID = $config->target;
        $file   = $config->file;
        $sql = "DELETE from debounce_event where site_id = ? AND action = 'run sync' AND action_id IN (
                    SELECT
                        j.id
                    FROM
                        job j
                    LEFT JOIN job_definition jd on j.job_definition_id = jd.id
                    WHERE jd.job_file = ?
                )";
        $stmt = $db->stmt_init();
        $stmt->prepare($sql);
        $stmt->bind_param("is", $siteID, $file);
        $stmt->execute();
        $db->close();
    }
}

function setupDb($dbConfig)
{
    return $db = new mysqli(
        $dbConfig->host,
        $dbConfig->user,
        $dbConfig->password,
        $dbConfig->database
    );
}
